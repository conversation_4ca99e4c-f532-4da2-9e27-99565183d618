import 'dart:io';
import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';
import '../../../domain/repos/profile_repo.dart';
import '../../../../auth/domain/entities/user_entity.dart';

part 'update_profile_image_state.dart';

class UpdateProfileImageCubit extends Cubit<UpdateProfileImageState> {
  UpdateProfileImageCubit(this._profileRepo)
      : super(UpdateProfileImageInitial());

  final ProfileRepo _profileRepo;

  Future<void> updateProfileImage({
    required File imageFile,
    required UserEntity currentUser,
  }) async {
    emit(UpdateProfileImageLoading());

    final result = await _profileRepo.updateProfileImage(
      imageFile: imageFile,
      userId: currentUser.uId,
      oldImageUrl: currentUser.imageUrl,
    );

    result.fold(
      (failure) => emit(UpdateProfileImageError(message: failure.message)),
      (imageUrl) {
        // The repository now handles updating the user data in Firestore and local storage
        // We just need to create the updated user entity for the UI
        final updatedUser = UserEntity(
          name: currentUser.name,
          phone: currentUser.phone,
          email: currentUser.email,
          uId: currentUser.uId,
          imageUrl: imageUrl,
        );

        emit(UpdateProfileImageSuccess(
          imageUrl: imageUrl,
          updatedUser: updatedUser,
        ));
      },
    );
  }
}
