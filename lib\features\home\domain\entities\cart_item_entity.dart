import 'dart:developer';
import 'package:equatable/equatable.dart';
import '../../../../core/entities/product_entity.dart';

class CartItemEntity extends Equatable {
  final ProductEntity productEntity;
  final int quantity;

  const CartItemEntity({required this.productEntity, this.quantity = 0});

  num calculateTotalPrice() => productEntity.price * quantity;

  CartItemEntity increaseQuantity() {
    log('⬆️ [CartItemEntity] Increasing quantity for ${productEntity.name}: $quantity → ${quantity + 1}');
    return CartItemEntity(
      productEntity: productEntity,
      quantity: quantity + 1,
    );
  }

  CartItemEntity decreaseQuantity() {
    final newQuantity = quantity > 1 ? quantity - 1 : quantity;
    log('⬇️ [CartItemEntity] Decreasing quantity for ${productEntity.name}: $quantity → $newQuantity');
    return CartItemEntity(
      productEntity: productEntity,
      quantity: newQuantity,
    );
  }

  @override
  List<Object?> get props => [productEntity, quantity];
}
