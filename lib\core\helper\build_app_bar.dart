import 'package:flutter/material.dart';

import '../../constants.dart';
import '../utils/responsive_utils.dart';
import '../widgets/custom_notifications_widget.dart';

AppBar buildAppBarWithAlarmWidget(
  BuildContext context, {
  VoidCallback? onTap,
  required String title,
  bool isBack = true,
  bool isNotification = true,
}) {
  return AppBar(
    title: Text(
      title,
      style: Theme.of(context).appBarTheme.titleTextStyle,
    ),
    centerTitle: true,
    leading: isBack
        ? IconButton(
            icon: Icon(
              Icons.arrow_back_ios,
              size: ResponsiveUtils.iconSize(18),
            ),
            onPressed: () {
              Navigator.of(context).pop();
            },
          )
        : null,
    actions: [
      Visibility(
        visible: isNotification,
        child: Padding(
          padding: REdgeInsets.only(right: kHorizontalPadding16),
          child: const CustomNotificationsWidget(),
        ),
      ),
    ],
  );
}
