import 'dart:developer';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../../../core/entities/product_entity.dart';
import '../../../domain/entities/favorite_product_entity.dart';
import '../../../domain/repos/favorites_repo.dart';

part 'favorites_state.dart';

class FavoritesCubit extends Cubit<FavoritesState> {
  FavoritesCubit(this._favoritesRepo) : super(FavoritesInitial());

  final FavoritesRepo _favoritesRepo;

  Future<void> getFavorites({required String userId}) async {
    try {
      log('FavoritesCubit: Getting favorites for user: $userId');
      emit(FavoritesLoading());

      final result = await _favoritesRepo.getFavorites(userId: userId);
      result.fold(
        (failure) {
          log('FavoritesCubit: Error getting favorites: ${failure.message}');
          emit(FavoritesError(message: failure.message));
        },
        (favorites) {
          log('FavoritesCubit: Got ${favorites.length} favorites');
          for (var fav in favorites) {
            log('Favorite: ${fav.product.name} (${fav.product.code})');
          }
          emit(FavoritesLoaded(favorites: favorites));
        },
      );
    } catch (e) {
      log('FavoritesCubit: Exception in getFavorites: ${e.toString()}');
      emit(const FavoritesError(message: 'حدث خطأ في جلب المفضلة'));
    }
  }

  Future<void> addToFavorites(
      {required String userId, required ProductEntity product}) async {
    final result =
        await _favoritesRepo.addToFavorites(userId: userId, product: product);
    result.fold(
      (failure) => emit(FavoritesError(message: failure.message)),
      (_) {
        emit(FavoriteAdded());
        getFavorites(userId: userId); // Reload favorites
      },
    );
  }

  Future<void> removeFromFavorites(
      {required String userId, required String productId}) async {
    final result = await _favoritesRepo.removeFromFavorites(
        userId: userId, productId: productId);
    result.fold(
      (failure) => emit(FavoritesError(message: failure.message)),
      (_) {
        emit(FavoriteRemoved());
        getFavorites(userId: userId); // Reload favorites
      },
    );
  }

  Future<void> toggleFavorite(
      {required String userId, required ProductEntity product}) async {
    final isFavResult = await _favoritesRepo.isFavorite(
        userId: userId, productId: product.code);
    isFavResult.fold(
      (failure) => emit(FavoritesError(message: failure.message)),
      (isFavorite) {
        if (isFavorite) {
          removeFromFavorites(userId: userId, productId: product.code);
        } else {
          addToFavorites(userId: userId, product: product);
        }
      },
    );
  }
}
