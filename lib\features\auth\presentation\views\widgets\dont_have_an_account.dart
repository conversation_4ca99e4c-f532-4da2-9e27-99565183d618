import 'package:flutter/material.dart';

InkWell buildDontHavaAnAccount(context,
    {required VoidCallback onTap,
    required String title,
    required String titleBtn}) {
  return InkWell(
    onTap: onTap,
    child: Text.rich(
      TextSpan(
        children: [
          TextSpan(
            text: title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
          ),
          TextSpan(
            text: titleBtn,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.primary,
                ),
          ),
        ],
      ),
    ),
  );
}
