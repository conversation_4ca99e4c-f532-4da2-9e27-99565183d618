import 'package:flutter/material.dart';
import 'app_colors/app_colors_light.dart';

ThemeData get lightTheme {
  return ThemeData(
    useMaterial3: true,
    brightness: Brightness.light,
    fontFamily: 'Cairo',

    // Color Scheme
    colorScheme: ColorScheme.fromSeed(
      seedColor: AppColorsLight.primaryColor,
      brightness: Brightness.light,
      primary: AppColorsLight.primaryColor,
      secondary: AppColorsLight.accentColor,
      surface: AppColorsLight.surfaceColor,
      error: AppColorsLight.errorColor,
      onPrimary: Colors.white,
      onSecondary: Colors.white,
      onSurface: AppColorsLight.textColor,
      onError: Colors.white,
    ),

    // Scaffold
    scaffoldBackgroundColor: AppColorsLight.backgroundColor,

    // AppBar Theme
    appBarTheme: const AppBarTheme(
      backgroundColor: AppColorsLight.primaryColor,
      foregroundColor: Colors.white,
      elevation: 0,
      centerTitle: true,
      titleTextStyle: TextStyle(
        color: Colors.white,
        fontSize: 18,
        fontWeight: FontWeight.w600,
        fontFamily: 'Cairo',
      ),
    ),

    // Icon Button Theme
    iconButtonTheme: IconButtonThemeData(
      style: IconButton.styleFrom(
        foregroundColor: AppColorsLight.iconColor,
      ),
    ),

    // Card Theme
    cardTheme: CardThemeData(
      color: AppColorsLight.cardColor,
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
    ),

    // Elevated Button Theme
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        textStyle: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          fontFamily: 'Cairo',
        ),
      ),
    ),

    // Text Theme
    textTheme: const TextTheme(
      displayLarge: TextStyle(
        color: AppColorsLight.textColor,
        fontSize: 32,
        fontWeight: FontWeight.bold,
        fontFamily: 'Cairo',
      ),
      displayMedium: TextStyle(
        color: AppColorsLight.textColor,
        fontSize: 28,
        fontWeight: FontWeight.bold,
        fontFamily: 'Cairo',
      ),
      displaySmall: TextStyle(
        color: AppColorsLight.textColor,
        fontSize: 24,
        fontWeight: FontWeight.bold,
        fontFamily: 'Cairo',
      ),
      headlineLarge: TextStyle(
        color: AppColorsLight.textColor,
        fontSize: 22,
        fontWeight: FontWeight.w600,
        fontFamily: 'Cairo',
      ),
      headlineMedium: TextStyle(
        color: AppColorsLight.textColor,
        fontSize: 20,
        fontWeight: FontWeight.w600,
        fontFamily: 'Cairo',
      ),
      headlineSmall: TextStyle(
        color: AppColorsLight.textColor,
        fontSize: 18,
        fontWeight: FontWeight.w600,
        fontFamily: 'Cairo',
      ),
      titleLarge: TextStyle(
        color: AppColorsLight.textColor,
        fontSize: 16,
        fontWeight: FontWeight.w600,
        fontFamily: 'Cairo',
      ),
      titleMedium: TextStyle(
        color: AppColorsLight.textColor,
        fontSize: 14,
        fontWeight: FontWeight.w500,
        fontFamily: 'Cairo',
      ),
      titleSmall: TextStyle(
        color: AppColorsLight.lightTextColor,
        fontSize: 12,
        fontWeight: FontWeight.w500,
        fontFamily: 'Cairo',
      ),
      bodyLarge: TextStyle(
        color: AppColorsLight.textColor,
        fontSize: 16,
        fontWeight: FontWeight.normal,
        fontFamily: 'Cairo',
      ),
      bodyMedium: TextStyle(
        color: AppColorsLight.textColor,
        fontSize: 14,
        fontWeight: FontWeight.normal,
        fontFamily: 'Cairo',
      ),
      bodySmall: TextStyle(
        color: AppColorsLight.lightTextColor,
        fontSize: 12,
        fontWeight: FontWeight.normal,
        fontFamily: 'Cairo',
      ),
    ),

    // Input Decoration Theme
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: AppColorsLight.surfaceColor,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: AppColorsLight.secondaryColor),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: AppColorsLight.secondaryColor),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide:
            const BorderSide(color: AppColorsLight.primaryColor, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: AppColorsLight.errorColor),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide:
            const BorderSide(color: AppColorsLight.errorColor, width: 2),
      ),
      labelStyle: const TextStyle(
        color: AppColorsLight.lightTextColor,
        fontFamily: 'Cairo',
      ),
      hintStyle: const TextStyle(
        color: AppColorsLight.lightTextColor,
        fontFamily: 'Cairo',
      ),
    ),

    // Bottom Navigation Bar Theme
    bottomNavigationBarTheme: const BottomNavigationBarThemeData(
      backgroundColor: AppColorsLight.backgroundColor,
      selectedItemColor: AppColorsLight.primaryColor,
      unselectedItemColor: AppColorsLight.lightTextColor,
      type: BottomNavigationBarType.fixed,
      elevation: 8,
    ),

    // Divider Theme
    dividerTheme: const DividerThemeData(
      color: AppColorsLight.secondaryColor,
      thickness: 1,
    ),

    // Icon Theme
    iconTheme: const IconThemeData(
      color: AppColorsLight.textColor,
    ),

    // Primary Icon Theme
    primaryIconTheme: const IconThemeData(
      color: Colors.white,
    ),

    // Switch Theme
    switchTheme: SwitchThemeData(
      thumbColor: WidgetStateProperty.resolveWith<Color>((states) {
        if (states.contains(WidgetState.selected)) {
          return AppColorsLight.primaryColor;
        }
        return AppColorsLight.lightTextColor;
      }),
      trackColor: WidgetStateProperty.resolveWith<Color>((states) {
        if (states.contains(WidgetState.selected)) {
          return AppColorsLight.primaryColor.withValues(alpha: 0.5);
        }
        return AppColorsLight.secondaryColor;
      }),
    ),

    // List Tile Theme
    listTileTheme: const ListTileThemeData(
      textColor: AppColorsLight.textColor,
      iconColor: AppColorsLight.textColor,
      tileColor: AppColorsLight.backgroundColor,
    ),

    // Floating Action Button Theme
    floatingActionButtonTheme: const FloatingActionButtonThemeData(
      backgroundColor: AppColorsLight.primaryColor,
      foregroundColor: Colors.white,
    ),

    // Snack Bar Theme
    snackBarTheme: const SnackBarThemeData(
      backgroundColor: AppColorsLight.primaryColor,
      contentTextStyle: TextStyle(
        color: Colors.white,
        fontFamily: 'Cairo',
      ),
    ),

    // Dialog Theme
    dialogTheme: const DialogThemeData(
      backgroundColor: AppColorsLight.backgroundColor,
      titleTextStyle: TextStyle(
        color: AppColorsLight.textColor,
        fontSize: 18,
        fontWeight: FontWeight.w600,
        fontFamily: 'Cairo',
      ),
      contentTextStyle: TextStyle(
        color: AppColorsLight.textColor,
        fontSize: 16,
        fontWeight: FontWeight.normal,
        fontFamily: 'Cairo',
      ),
    ),

    // Chip Theme
    chipTheme: const ChipThemeData(
      backgroundColor: AppColorsLight.secondaryColor,
      selectedColor: AppColorsLight.primaryColor,
      disabledColor: AppColorsLight.lightTextColor,
      labelStyle: TextStyle(
        color: AppColorsLight.textColor,
        fontFamily: 'Cairo',
      ),
      secondaryLabelStyle: TextStyle(
        color: Colors.white,
        fontFamily: 'Cairo',
      ),
      brightness: Brightness.light,
    ),

    // Tab Bar Theme
    tabBarTheme: const TabBarThemeData(
      labelColor: AppColorsLight.primaryColor,
      unselectedLabelColor: AppColorsLight.lightTextColor,
      indicatorColor: AppColorsLight.primaryColor,
      labelStyle: TextStyle(
        fontWeight: FontWeight.w600,
        fontFamily: 'Cairo',
      ),
      unselectedLabelStyle: TextStyle(
        fontWeight: FontWeight.normal,
        fontFamily: 'Cairo',
      ),
    ),

    // Progress Indicator Theme
    progressIndicatorTheme: const ProgressIndicatorThemeData(
      color: AppColorsLight.primaryColor,
      linearTrackColor: AppColorsLight.secondaryColor,
      circularTrackColor: AppColorsLight.secondaryColor,
    ),

    // Slider Theme
    sliderTheme: SliderThemeData(
      activeTrackColor: AppColorsLight.primaryColor,
      inactiveTrackColor: AppColorsLight.secondaryColor,
      thumbColor: AppColorsLight.primaryColor,
      overlayColor: AppColorsLight.primaryColor.withValues(alpha: 0.2),
      valueIndicatorColor: AppColorsLight.primaryColor,
      valueIndicatorTextStyle: const TextStyle(
        color: Colors.white,
        fontFamily: 'Cairo',
      ),
    ),

    // Tooltip Theme
    tooltipTheme: const TooltipThemeData(
      decoration: BoxDecoration(
        color: AppColorsLight.textColor,
        borderRadius: BorderRadius.all(Radius.circular(8)),
      ),
      textStyle: TextStyle(
        color: AppColorsLight.backgroundColor,
        fontFamily: 'Cairo',
      ),
    ),

    // Banner Theme
    bannerTheme: const MaterialBannerThemeData(
      backgroundColor: AppColorsLight.primaryColor,
      contentTextStyle: TextStyle(
        color: Colors.white,
        fontFamily: 'Cairo',
      ),
    ),

    // Navigation Rail Theme
    navigationRailTheme: const NavigationRailThemeData(
      backgroundColor: AppColorsLight.backgroundColor,
      selectedIconTheme: IconThemeData(
        color: AppColorsLight.primaryColor,
      ),
      unselectedIconTheme: IconThemeData(
        color: AppColorsLight.lightTextColor,
      ),
      selectedLabelTextStyle: TextStyle(
        color: AppColorsLight.primaryColor,
        fontFamily: 'Cairo',
      ),
      unselectedLabelTextStyle: TextStyle(
        color: AppColorsLight.lightTextColor,
        fontFamily: 'Cairo',
      ),
    ),
  );
}
