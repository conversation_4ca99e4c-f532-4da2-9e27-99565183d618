import 'package:flutter/material.dart';
import '../../../../../core/helper/get_user.dart';
import '../../../../../core/widgets/custom_notifications_widget.dart';
import '../../../../profile/presentation/views/widgets/profile_image_display.dart';

class CustomHomeAppBar extends StatelessWidget {
  const CustomHomeAppBar({super.key});

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: ProfileImageDisplay(
        imageUrl: getUser().imageUrl,
        size: 60,
        showBorder: true,
      ),
      title: Text(
        'صباح الخير..!',
        style: Theme.of(context).textTheme.bodySmall,
      ),
      subtitle: Text(
        getUser().name,
        style: Theme.of(context).textTheme.titleMedium,
      ),
      trailing: const CustomNotificationsWidget(),
    );
  }
}
