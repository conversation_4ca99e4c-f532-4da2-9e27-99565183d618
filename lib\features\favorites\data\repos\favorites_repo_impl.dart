import 'dart:developer';
import 'package:dartz/dartz.dart';
import '../../../../core/entities/product_entity.dart';
import '../../../../core/errors/failure.dart';
import '../../../../core/models/product_model.dart';
import '../../../../core/services/data_service.dart';
import '../../../../core/utils/backend_end_points.dart';
import '../models/favorite_product_model.dart';
import '../../domain/entities/favorite_product_entity.dart';
import '../../domain/repos/favorites_repo.dart';

class FavoritesRepoImpl implements FavoritesRepo {
  final DatabaseService databaseService;

  FavoritesRepoImpl({required this.databaseService});

  @override
  Future<Either<Failure, List<FavoriteProductEntity>>> getFavorites(
      {required String userId}) async {
    try {
      log('Getting favorites for user: $userId');
      log('Using collection path: ${BackendEndPoints.getUserFavorites}');

      // Get all favorites for this user from the favorites collection
      final favoritesData = await databaseService.getData(
        path: BackendEndPoints.getUserFavorites,
        query: {'userId': userId},
      );

      log('Favorites data received: $favoritesData');
      log('Favorites data type: ${favoritesData.runtimeType}');

      if (favoritesData == null) {
        log('No favorites data found');
        return right([]);
      }

      final favoritesList = <FavoriteProductEntity>[];

      if (favoritesData is List) {
        log('Processing ${favoritesData.length} favorite items');
        // Handle list response
        for (var favoriteItem in favoritesData) {
          if (favoriteItem is Map<String, dynamic> &&
              favoriteItem['userId'] == userId) {
            final productId = favoriteItem['productId'];
            log('Processing favorite with productId: $productId');

            if (productId != null) {
              final product = await _getProductByCode(productId);
              if (product != null) {
                log('Found product: ${product.name}');
                // Parse addedAt with error handling
                DateTime addedAt;
                try {
                  addedAt = DateTime.parse(favoriteItem['addedAt'] ??
                      DateTime.now().toIso8601String());
                } catch (e) {
                  log('Error parsing addedAt date, using current time: $e');
                  addedAt = DateTime.now();
                }

                final favorite = FavoriteProductModel(
                  id: favoriteItem['id'] ?? productId,
                  userId: userId,
                  product: product,
                  addedAt: addedAt,
                );
                favoritesList.add(favorite);
              } else {
                log('Product not found for ID: $productId');
              }
            }
          }
        }
      }

      log('Returning ${favoritesList.length} favorites');
      return right(favoritesList);
    } catch (e) {
      log('Exception in FavoritesRepoImpl.getFavorites: ${e.toString()}');
      return left(ServerFailure('حدث خطأ في جلب المفضلة'));
    }
  }

  @override
  Future<Either<Failure, void>> addToFavorites(
      {required String userId, required ProductEntity product}) async {
    try {
      log('Adding favorite: userId=$userId, productCode=${product.code}');

      final favoriteData = {
        'userId': userId,
        //! I need to modify it to get product id instead of code
        'productId': product.code,
        'addedAt': DateTime.now().toIso8601String(),
      };

      log('Favorite data: $favoriteData');

      final documentId = '${userId}_${product.code}';
      log('Document ID: $documentId');

      // Add to favorites collection
      await databaseService.addData(
        path: BackendEndPoints.getUserFavorites,
        data: favoriteData,
        documentId: documentId,
      );

      log('Successfully added favorite to Firebase');
      return right(null);
    } catch (e) {
      log('Exception in FavoritesRepoImpl.addToFavorites: ${e.toString()}');
      return left(ServerFailure('حدث خطأ في إضافة المنتج للمفضلة'));
    }
  }

  @override
  Future<Either<Failure, void>> removeFromFavorites(
      {required String userId, required String productId}) async {
    try {
      // Delete the favorite document using the combined ID
      await databaseService.deleteData(
        path: BackendEndPoints.getUserFavorites,
        docuementId: '${userId}_$productId',
      );

      return right(null);
    } catch (e) {
      log('Exception in FavoritesRepoImpl.removeFromFavorites: ${e.toString()}');
      return left(ServerFailure('حدث خطأ في إزالة المنتج من المفضلة'));
    }
  }

  @override
  Future<Either<Failure, bool>> isFavorite(
      {required String userId, required String productId}) async {
    try {
      // Check if the favorite document exists
      final exists = await databaseService.checkIfDataExists(
        path: BackendEndPoints.getUserFavorites,
        docuementId: '${userId}_$productId',
      );

      return right(exists);
    } catch (e) {
      log('Exception in FavoritesRepoImpl.isFavorite: ${e.toString()}');
      return left(ServerFailure('حدث خطأ في التحقق من المفضلة'));
    }
  }

  Future<ProductEntity?> _getProductByCode(String productCode) async {
    try {
      log('Getting product by ID: $productCode');

      // First try to get by document ID
      var productData = await databaseService.getData(
        path: BackendEndPoints.getProducts,
        docuementId: productCode,
      );

      log('Product data by document ID for $productCode: $productData');

      // If not found by document ID, try to search by code field
      if (productData == null) {
        log('Product not found by document ID, searching by code field...');
        final productsData = await databaseService.getData(
          path: BackendEndPoints.getProducts,
          query: {'code': productCode},
        );

        log('Products search result: $productsData');

        if (productsData is List && productsData.isNotEmpty) {
          productData = productsData.first;
          log('Found product by code search: $productData');
        }
      }

      if (productData != null) {
        try {
          final product =
              ProductModel.fromJson(productData as Map<String, dynamic>)
                  .toEntity();
          log('Found product: ${product.name}');
          return product;
        } catch (e) {
          log('Error parsing product data for $productCode: $e');
          log('Product data: $productData');
          return null;
        }
      }

      log('No product found for ID: $productCode');
      return null;
    } catch (e) {
      log('Error getting product by ID: ${e.toString()}');
      return null;
    }
  }
}
