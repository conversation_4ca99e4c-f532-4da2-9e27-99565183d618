import 'package:flutter/material.dart';
import '../../../../../core/utils/responsive_utils.dart';

class RatingInputWidget extends StatefulWidget {
  const RatingInputWidget({
    super.key,
    required this.onRatingChanged,
    this.initialRating = 0,
    this.size = 32,
  });

  final Function(double rating) onRatingChanged;
  final double initialRating;
  final double size;

  @override
  State<RatingInputWidget> createState() => _RatingInputWidgetState();
}

class _RatingInputWidgetState extends State<RatingInputWidget> {
  double _currentRating = 0;
  double _hoveredRating = 0;

  @override
  void initState() {
    super.initState();
    _currentRating = widget.initialRating;
  }

  @override
  Widget build(BuildContext context) {
    context.initResponsive();
    final theme = Theme.of(context);

    return Container(
      padding: REdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(ResponsiveUtils.radius(16)),
        border: Border.all(
          color: theme.dividerColor.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.star_rate_rounded,
                size: ResponsiveUtils.iconSize(20),
                color: theme.colorScheme.primary,
              ),
              SizedBox(width: ResponsiveUtils.spacing(8)),
              Text(
                'التقييم',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w700,
                  fontSize: ResponsiveUtils.fontSize(16),
                  color: theme.colorScheme.onSurface,
                ),
              ),
              if (_currentRating > 0) ...[
                const Spacer(),
                Container(
                  padding: REdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color:
                        _getRatingColor(_currentRating).withValues(alpha: 0.1),
                    borderRadius:
                        BorderRadius.circular(ResponsiveUtils.radius(8)),
                    border: Border.all(
                      color: _getRatingColor(_currentRating)
                          .withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    _currentRating.toStringAsFixed(1),
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: _getRatingColor(_currentRating),
                      fontSize: ResponsiveUtils.fontSize(12),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ],
          ),
          SizedBox(height: ResponsiveUtils.spacing(16)),

          // Enhanced star rating input
          Container(
            padding: REdgeInsets.all(12),
            decoration: BoxDecoration(
              color: theme.cardColor,
              borderRadius: BorderRadius.circular(ResponsiveUtils.radius(12)),
              boxShadow: [
                BoxShadow(
                  color: theme.shadowColor.withValues(alpha: 0.05),
                  blurRadius: ResponsiveUtils.radius(8),
                  offset: Offset(0, ResponsiveUtils.height(2)),
                ),
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: List.generate(5, (index) {
                final starIndex = index + 1;
                final isSelected = starIndex <= _currentRating;
                final isHovered = starIndex <= _hoveredRating;

                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _currentRating = starIndex.toDouble();
                    });
                    widget.onRatingChanged(_currentRating);
                  },
                  child: MouseRegion(
                    onEnter: (_) =>
                        setState(() => _hoveredRating = starIndex.toDouble()),
                    onExit: (_) => setState(() => _hoveredRating = 0),
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 200),
                      padding: REdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: (isSelected || isHovered)
                            ? _getRatingColor(starIndex.toDouble())
                                .withValues(alpha: 0.1)
                            : Colors.transparent,
                        borderRadius:
                            BorderRadius.circular(ResponsiveUtils.radius(8)),
                      ),
                      child: Icon(
                        (isSelected || isHovered)
                            ? Icons.star_rounded
                            : Icons.star_outline_rounded,
                        size: ResponsiveUtils.iconSize(widget.size),
                        color: (isSelected || isHovered)
                            ? _getRatingColor(starIndex.toDouble())
                            : theme.colorScheme.onSurface
                                .withValues(alpha: 0.3),
                      ),
                    ),
                  ),
                );
              }),
            ),
          ),

          SizedBox(height: ResponsiveUtils.spacing(12)),

          // Enhanced rating description
          if (_currentRating > 0) ...[
            Container(
              width: double.infinity,
              padding: REdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _getRatingColor(_currentRating).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(ResponsiveUtils.radius(10)),
                border: Border.all(
                  color: _getRatingColor(_currentRating).withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    _getRatingIcon(_currentRating),
                    size: ResponsiveUtils.iconSize(18),
                    color: _getRatingColor(_currentRating),
                  ),
                  SizedBox(width: ResponsiveUtils.spacing(8)),
                  Text(
                    _getRatingDescription(_currentRating),
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: _getRatingColor(_currentRating),
                      fontSize: ResponsiveUtils.fontSize(14),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ] else ...[
            Container(
              width: double.infinity,
              padding: REdgeInsets.all(12),
              decoration: BoxDecoration(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(ResponsiveUtils.radius(10)),
                border: Border.all(
                  color: theme.dividerColor.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.touch_app_rounded,
                    size: ResponsiveUtils.iconSize(18),
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                  SizedBox(width: ResponsiveUtils.spacing(8)),
                  Text(
                    'اختر تقييمك',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                      fontSize: ResponsiveUtils.fontSize(14),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  String _getRatingDescription(double rating) {
    switch (rating.toInt()) {
      case 1:
        return 'سيء جداً';
      case 2:
        return 'سيء';
      case 3:
        return 'متوسط';
      case 4:
        return 'جيد';
      case 5:
        return 'ممتاز';
      default:
        return 'اختر تقييمك';
    }
  }

  Color _getRatingColor(double rating) {
    switch (rating.toInt()) {
      case 1:
      case 2:
        return Colors.red;
      case 3:
        return Colors.orange;
      case 4:
      case 5:
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  IconData _getRatingIcon(double rating) {
    switch (rating.toInt()) {
      case 1:
        return Icons.sentiment_very_dissatisfied_rounded;
      case 2:
        return Icons.sentiment_dissatisfied_rounded;
      case 3:
        return Icons.sentiment_neutral_rounded;
      case 4:
        return Icons.sentiment_satisfied_rounded;
      case 5:
        return Icons.sentiment_very_satisfied_rounded;
      default:
        return Icons.help_outline_rounded;
    }
  }
}
