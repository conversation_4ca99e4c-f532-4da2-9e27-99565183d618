import 'package:flutter/material.dart';
import '../../../../../core/utils/responsive_utils.dart';

class RatingInputWidget extends StatefulWidget {
  const RatingInputWidget({
    super.key,
    required this.onRatingChanged,
    this.initialRating = 0,
    this.size = 32,
  });

  final Function(double rating) onRatingChanged;
  final double initialRating;
  final double size;

  @override
  State<RatingInputWidget> createState() => _RatingInputWidgetState();
}

class _RatingInputWidgetState extends State<RatingInputWidget> {
  double _currentRating = 0;

  @override
  void initState() {
    super.initState();
    _currentRating = widget.initialRating;
  }

  @override
  Widget build(BuildContext context) {
    context.initResponsive();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Star Rating Input
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(5, (index) {
            final starIndex = index + 1;
            return GestureDetector(
              onTap: () {
                setState(() {
                  _currentRating = starIndex.toDouble();
                });
                widget.onRatingChanged(_currentRating);
              },
              child: Padding(
                padding: REdgeInsets.symmetric(horizontal: 4),
                child: Icon(
                  _currentRating >= starIndex ? Icons.star : Icons.star_border,
                  size: ResponsiveUtils.iconSize(widget.size),
                  color: _currentRating >= starIndex 
                      ? Colors.amber 
                      : Colors.grey[400],
                ),
              ),
            );
          }),
        ),
        
        SizedBox(height: ResponsiveUtils.spacing(12)),
        
        // Rating Description
        Center(
          child: Text(
            _getRatingDescription(_currentRating),
            style: TextStyle(
              fontSize: ResponsiveUtils.fontSize(16),
              fontWeight: FontWeight.w500,
              color: _getRatingColor(_currentRating),
            ),
          ),
        ),
      ],
    );
  }

  String _getRatingDescription(double rating) {
    switch (rating.toInt()) {
      case 1:
        return 'سيء جداً';
      case 2:
        return 'سيء';
      case 3:
        return 'متوسط';
      case 4:
        return 'جيد';
      case 5:
        return 'ممتاز';
      default:
        return 'اختر تقييمك';
    }
  }

  Color _getRatingColor(double rating) {
    switch (rating.toInt()) {
      case 1:
      case 2:
        return Colors.red;
      case 3:
        return Colors.orange;
      case 4:
      case 5:
        return Colors.green;
      default:
        return Colors.grey;
    }
  }
}
