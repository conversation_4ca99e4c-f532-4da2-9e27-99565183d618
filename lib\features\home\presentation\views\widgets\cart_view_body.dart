import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../constants.dart';
import '../../../../../core/utils/app_animations_lottie.dart';
import '../../../../../core/utils/responsive_utils.dart';
import '../../../../../core/widgets/custom_empty_state_widget.dart';
import '../../manager/cart_cubit/cart_cubit.dart';
import 'cart_header.dart';
import 'cart_items_list.dart';
import 'custom_cart_button.dart';

class CartViewBody extends StatelessWidget {
  const CartViewBody({super.key});

  @override
  Widget build(BuildContext context) {
    context.initResponsive();

    return Stack(
      children: [
        CustomScrollView(
          physics: const BouncingScrollPhysics(),
          slivers: [
            const SliverToBoxAdapter(
              child: Column(
                children: [
                  RSizedBox.height(kTopPadding16),
                  Cart<PERSON>eader(),
                  RSizedBox.height(kVerticalPaddingSmall16),
                ],
              ),
            ),
            SliverToBoxAdapter(
              child: context.read<CartCubit>().cartEntity.cartItems.isNotEmpty
                  ? const CustomDivider()
                  : const CustomEmptyStateWidget(
                      lottieAsset: AppAnimationsLottie.emptyBox,
                      title: 'السلة فارغة',
                      subtitle: 'أضف منتجاتك إلى السلة لتظهر هنا',
                    ),
            ),
            CarItemsList(
              cartItems: context.watch<CartCubit>().cartEntity.cartItems,
            ),
            SliverToBoxAdapter(
              child: context.read<CartCubit>().cartEntity.cartItems.isNotEmpty
                  ? const CustomDivider()
                  : const SizedBox.shrink(),
            ),
            SliverToBoxAdapter(
              child: RSizedBox.height(
                ResponsiveUtils.screenHeight * 0.15,
              ),
            ),
          ],
        ),
        Positioned(
          bottom: ResponsiveUtils.spacing(16),
          left: ResponsiveUtils.spacing(16),
          right: ResponsiveUtils.spacing(16),
          child: context.watch<CartCubit>().cartEntity.cartItems.isNotEmpty
              ? const CustomCartButton()
              : const SizedBox.shrink(),
        ),
      ],
    );
  }
}
