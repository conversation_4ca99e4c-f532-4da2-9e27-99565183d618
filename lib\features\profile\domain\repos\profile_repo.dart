import 'dart:io';
import 'package:dartz/dartz.dart';
import '../../../../core/errors/failure.dart';
import '../../../auth/domain/entities/user_entity.dart';

abstract class ProfileRepo {
  Future<Either<Failure, UserEntity>> getUserProfile();
  Future<Either<Failure, void>> updateUserProfile(
      {required UserEntity userEntity});
  Future<Either<Failure, String>> updateProfileImage(
      {required File imageFile, required String userId, String? oldImageUrl});
  Future<Either<Failure, void>> deleteUserAccount();
  Future<Either<Failure, void>> logout();
}
