import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../core/cubits/favorites_cubit/favorites_cubit.dart';
import '../../../../../core/helper/build_custom_snack_bar.dart';
import '../../../../../core/helper/get_user.dart';
import '../../../../../core/services/get_it_service.dart';
import '../../../../../core/utils/app_animations_lottie.dart';
import '../../../../../core/utils/responsive_utils.dart';
import '../../../../../core/widgets/custom_empty_state_widget.dart';
import '../../../../../core/widgets/custom_loading_indicator.dart';
import '../../../../../core/widgets/custom_product_item.dart';
import '../../../../auth/presentation/views/login_view.dart';

import '../../manager/favorites_cubit/favorites_cubit.dart';
import '../favorites_view.dart';

class FavoritesViewBody extends StatelessWidget {
  const FavoritesViewBody({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<FavoritesCubit, FavoritesState>(
          listener: (context, state) {
            if (state is FavoritesError) {
              showErrorSnackBar(context, message: state.message);
            }
            if (state is FavoriteAdded) {
              showSuccessSnackBar(context,
                  message: 'تم إضافة المنتج للمفضلة',
                  actionLabel: 'الذهاب إلى المفضلة', onActionPressed: () {
                Navigator.pushNamed(context, FavoritesView.routeName);
              });
            }
            if (state is FavoriteRemoved) {
              showSuccessSnackBar(context,
                  message: 'تم إزالة المنتج من المفضلة');
            }
          },
        ),
        BlocListener<GlobalFavoritesCubit, GlobalFavoritesState>(
          bloc: getIt<GlobalFavoritesCubit>(),
          listener: (context, state) {
            // Refresh local favorites when global state changes
            if (state is GlobalFavoritesLoaded) {
              final user = getUser();
              context.read<FavoritesCubit>().getFavorites(userId: user.uId);
            }
          },
        ),
      ],
      child: BlocBuilder<FavoritesCubit, FavoritesState>(
        builder: (context, state) {
          if (state is FavoritesLoading) {
            return Center(
                child: CustomLoadingIndicator(
              color: Theme.of(context).colorScheme.primary,
            ));
          }

          if (state is FavoritesLoaded) {
            if (state.favorites.isEmpty) {
              return _buildEmptyState();
            }
            return _buildFavoritesList(context, state.favorites);
          }

          if (state is FavoritesError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Theme.of(context).colorScheme.error,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    state.message,
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      try {
                        final user = getUser();
                        context
                            .read<FavoritesCubit>()
                            .getFavorites(userId: user.uId);
                      } catch (e) {
                        // Handle error
                      }
                    },
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            );
          }

          // Load favorites on initial state with error handling
          try {
            final user = getUser();
            context.read<FavoritesCubit>().getFavorites(userId: user.uId);
            return Center(
                child: CustomLoadingIndicator(
              color: Theme.of(context).colorScheme.primary,
            ));
          } catch (e) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Theme.of(context).colorScheme.error,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'حدث خطأ في تحميل بيانات المستخدم. يرجى تسجيل الدخول مرة أخرى.',
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      try {
                        final user = getUser();
                        context
                            .read<FavoritesCubit>()
                            .getFavorites(userId: user.uId);
                      } catch (retryError) {
                        Navigator.pushReplacementNamed(
                            context, LoginView.routeName);
                      }
                    },
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            );
          }
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Builder(
      builder: (context) {
        context.initResponsive();
        return const CustomEmptyStateWidget(
          lottieAsset: AppAnimationsLottie.emptySearch,
          title: 'المفضلة فارغة!',
          subtitle: 'أضف منتجاتك المفضلة لتظهر هنا',
        );
      },
    );
  }

  Widget _buildFavoritesList(BuildContext context, favorites) {
    context.initResponsive();
    return Padding(
      padding: REdgeInsets.all(16),
      child: GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount:
              ResponsiveUtils.getGridCount(mobileCount: 2, tabletCount: 3),
          childAspectRatio: ResponsiveUtils.getAspectRatio(
              mobileRatio: 163 / 214, tabletRatio: 1.1),
          crossAxisSpacing: ResponsiveUtils.spacing(16),
          mainAxisSpacing: ResponsiveUtils.spacing(16),
        ),
        itemCount: favorites.length,
        itemBuilder: (context, index) {
          final favorite = favorites[index];
          return CustomProductItem(
            product: favorite.product,
          );
        },
      ),
    );
  }
}
