import 'package:flutter/material.dart';
import '../../../../../core/utils/responsive_utils.dart';
import '../../../domain/entities/bottom_nav_bar_entity.dart';
import 'nav_bar_item.dart';

class CustomBottomNavBar extends StatefulWidget {
  const CustomBottomNavBar({
    super.key,
    required this.onItemTapped,
    this.currentIndex = 0,
  });
  final ValueChanged<int> onItemTapped;
  final int currentIndex;
  @override
  State<CustomBottomNavBar> createState() => _CustomBottomNavBarState();
}

class _CustomBottomNavBarState extends State<CustomBottomNavBar> {
  @override
  Widget build(BuildContext context) {
    return Container(
      width: ResponsiveUtils.screenWidth,
      height: ResponsiveUtils.screenHeight * .08,
      decoration: ShapeDecoration(
        color: Theme.of(context).bottomNavigationBarTheme.backgroundColor,
        shape: RoundedRectangleBorder(
          borderRadius: RBorderRadius.only(
            topLeft: 30,
            topRight: 30,
          ),
        ),
        shadows: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            offset: const Offset(0, -2),
            blurRadius: ResponsiveUtils.elevation(10),
          ),
        ],
      ),
      child: Row(
        children: bottomNavBarItems.asMap().entries.map((entry) {
          final index = entry.key;
          final entity = entry.value;
          return Expanded(
            flex: index == widget.currentIndex ? 3 : 2,
            child: GestureDetector(
              onTap: () {
                widget.onItemTapped(index);
              },
              child: NavigationBarItem(
                bottomNavBarEntity: entity,
                isSelected: index == widget.currentIndex,
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
}
