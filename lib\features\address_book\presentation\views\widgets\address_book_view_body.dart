import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../core/helper/get_user.dart';
import '../../../../../core/widgets/custom_button.dart';
import '../../manager/address_book_cubit/address_book_cubit.dart';
import '../add_address_view.dart';
import '../edit_address_view.dart';
import '../widgets/address_item_widget.dart';

class AddressBookViewBody extends StatelessWidget {
  const AddressBookViewBody({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          CustomButton(
            text: 'إضافة عنوان جديد',
            onPressed: () {
              Navigator.pushNamed(context, AddAddressView.routeName);
            },
          ),
          const SizedBox(height: 16),
          Expanded(
            child: <PERSON><PERSON><PERSON>er<AddressBookCubit, AddressBookState>(
              builder: (context, state) {
                if (state is AddressBookLoading) {
                  return const Center(child: CircularProgressIndicator());
                } else if (state is AddressBookLoaded) {
                  if (state.addresses.isEmpty) {
                    return const Center(child: Text('لا توجد عناوين بعد.'));
                  }

                  return ListView.builder(
                    itemCount: state.addresses.length,
                    itemBuilder: (context, index) {
                      final address = state.addresses[index];

                      return AddressItemWidget(
                        address: address,
                        onEdit: () {
                          Navigator.pushNamed(
                            context,
                            EditAddressScreen.routeName,
                            arguments: address,
                          );
                        },
                        onDelete: () {
                          context
                              .read<AddressBookCubit>()
                              .deleteAddress(addressId: address.id, userId: getUser().uId);
                        },
                        onSetDefault: () {
                          context
                              .read<AddressBookCubit>()
                              .setDefaultAddress(userId: getUser().uId, addressId: address.id);
                        },
                      );
                    },
                  );
                } else if (state is AddressBookError) {
                  return Center(child: Text(state.message));
                } else {
                  return const SizedBox();
                }
              },
            ),
          ),
        ],
      ),
    );
  }
}
