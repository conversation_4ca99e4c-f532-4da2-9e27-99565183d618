import 'package:flutter/material.dart';
import '../../../../../core/utils/responsive_utils.dart';
import '../../../domain/entities/order_entity.dart';
import 'shipping_item.dart';
import 'package:provider/provider.dart';

class ShippingSection extends StatefulWidget {
  const ShippingSection({super.key});

  @override
  State<ShippingSection> createState() => _ShippingSectionState();
}

class _ShippingSectionState extends State<ShippingSection>
    with AutomaticKeepAliveClientMixin {
  int selectedIndex = -1; //? To not select any item at the beginning
  @override
  Widget build(BuildContext context) {
    super.build(context);
    final orderEntity = context.read<OrderEntity>();
    return Column(
      children: [
        const RSizedBox.height(24),
        ShippingItem(
          onTap: () {
            selectedIndex = 0;
            setState(() {});
            orderEntity.payWithCash = true;
          },
          isSelected: selectedIndex == 0,
          title: 'الدفع عند الاستلام',
          subTitle: 'التسليم من المكان',
          price: (context.read<OrderEntity>().cartEntity.getTotalPrice() + 30)
              .toString(),
        ),
        const RSizedBox.height(16),
        ShippingItem(
          onTap: () {
            selectedIndex = 1;
            setState(() {});
            orderEntity.payWithCash = false;
          },
          isSelected: selectedIndex == 1,
          title: 'الدفع اونلاين',
          subTitle: 'يرجي تحديد طريقه الدفع',
          price:
              context.read<OrderEntity>().cartEntity.getTotalPrice().toString(),
        ),
      ],
    );
  }

  @override
  bool get wantKeepAlive => true;
}
