import 'package:flutter/material.dart';

abstract class AppColorsLight {
  static const Color primaryColor = Color(0xff17386a);
  static const Color lightPrimaryColor = Color(0xFF1d355e);
  static const Color secondaryColor = Color(0xFFE5E5E5);
  static const Color accentColor = Color(0xFF42a5f5);
  static const Color textColor = Color(0xFF0f2543);
  static const Color lightTextColor = Color(0xFF8c9aa9);
  static const Color errorColor = Color(0xFFD32F2F);
  static const Color successColor = Color(0xFF388E3C);
  static const Color backgroundColor = Color(0xFFFFFFFF);
  static const Color surfaceColor = Color(0xFFFAFAFA);
  static const Color cardColor = Color(0xFFFFFFFF);
  static const Color iconColor = Color(0xFFE6EDF3);
}
