import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'data_service.dart';

class FireStoreService implements DatabaseService {
  FirebaseFirestore firestore = FirebaseFirestore.instance;
  @override
  Future<void> addData({
    required String path,
    required Map<String, dynamic> data,
    String? documentId,
  }) async {
    try {
      log('FirestoreService.addData - path: $path, documentId: $documentId, data: $data');

      if (documentId != null) {
        await firestore.collection(path).doc(documentId).set(data);
        log('Successfully added document with ID: $documentId');
      } else {
        final docRef = await firestore.collection(path).add(data);
        log('Successfully added document with auto-generated ID: ${docRef.id}');
      }
    } catch (e) {
      log('Error in FirestoreService.addData: ${e.toString()}');
      rethrow;
    }
  }

  @override
  Future<void> updateData({
    required String path,
    required Map<String, dynamic> data,
    required String documentId,
  }) async {
    try {
      log('FirestoreService.updateData - path: $path, documentId: $documentId, data: $data');

      await firestore.collection(path).doc(documentId).update(data);
      log('Successfully updated document with ID: $documentId');
    } catch (e) {
      log('Error in FirestoreService.updateData: ${e.toString()}');
      rethrow;
    }
  }

  @override
  Future<bool> checkIfDataExists({
    required String path,
    required String docuementId,
  }) async {
    final doc = await firestore
        .collection(path)
        .doc(
          docuementId,
        )
        .get();
    return doc.exists;
  }

  @override
  Future<dynamic> getData({
    required String path,
    String? docuementId,
    Map<String, dynamic>? query,
  }) async {
    try {
      log('FirestoreService.getData - path: $path, docuementId: $docuementId, query: $query');

      if (docuementId != null) {
        final data = await firestore.collection(path).doc(docuementId).get();
        log('Single document result: ${data.data()}');
        return data.data();
      } else {
        Query<Map<String, dynamic>> data = firestore.collection(path);
        if (query != null) {
          // Add where clause support
          if (query['userId'] != null) {
            log('Adding userId filter: ${query['userId']}');
            // For favorites collection, use 'userId' field directly
            // For orders collection, use 'uId' field
            if (path.toLowerCase().contains('favourite') ||
                path.toLowerCase().contains('favorite')) {
              data = data.where('userId', isEqualTo: query['userId']);
            } else {
              data = data.where('uId', isEqualTo: query['userId']);
            }
          }
          if (query['uId'] != null) {
            log('Adding uId filter: ${query['uId']}');
            data = data.where('uId', isEqualTo: query['uId']);
          }
          if (query['code'] != null) {
            log('Adding code filter: ${query['code']}');
            data = data.where('code', isEqualTo: query['code']);
          }
          if (query['category'] != null) {
            log('Adding category filter: ${query['category']}');
            data = data.where('category', isEqualTo: query['category']);
          }
          if (query['company'] != null) {
            log('Adding company filter: ${query['company']}');
            data = data.where('company', isEqualTo: query['company']);
          }
          if (query['orderBy'] != null) {
            final orderByField = query['orderBy'];
            final descending = query['descending'];
            data = data.orderBy(
              orderByField,
              descending: descending,
            );
          }
          if (query['limit'] != null) {
            final limit = query['limit'];
            data = data.limit(limit);
          }
          //? add more query options here
        }
        final result = await data.get();
        final resultList = result.docs.map((e) {
          try {
            final docData = e.data();
            // Add document ID to the data
            docData['documentId'] = e.id;
            return docData;
          } catch (docError) {
            log('Error processing document ${e.id}: $docError');
            // Return a minimal valid document structure
            return <String, dynamic>{
              'documentId': e.id,
              'error': 'Failed to parse document data'
            };
          }
        }).toList();
        log('Query result: Found ${resultList.length} documents');
        for (var doc in resultList) {
          if (!doc.containsKey('error')) {
            log('Document: $doc');
          } else {
            log('Error document: ${doc['documentId']} - ${doc['error']}');
          }
        }
        return resultList;
      }
    } catch (e) {
      log('Error in FirestoreService.getData: ${e.toString()}');
      rethrow;
    }
  }

  @override
  Future<void> deleteData({
    required String path,
    required String docuementId,
  }) async {
    await firestore.collection(path).doc(docuementId).delete();
  }
}
