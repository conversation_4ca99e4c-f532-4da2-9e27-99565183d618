import 'dart:convert';
import 'dart:developer';
import '../services/shared_preferences_singleton.dart';
import '../../constants.dart';

class DataCleanupHelper {
  /// Validates and cleans up user data in SharedPreferences
  static Future<bool> validateAndCleanUserData() async {
    try {
      final jsonString = SharedPreferencesSingleton.getString(kUserData);
      
      if (jsonString.isEmpty) {
        log('No user data found in SharedPreferences');
        return false;
      }
      
      // Try to decode the JSON
      final Map<String, dynamic> userData = jsonDecode(jsonString);
      
      // Validate required fields
      if (!userData.containsKey('uId') || 
          !userData.containsKey('name') || 
          !userData.containsKey('email')) {
        log('Invalid user data structure, clearing...');
        await SharedPreferencesSingleton.remove(kUserData);
        return false;
      }
      
      // Check if values are not null or empty
      if (userData['uId'] == null || userData['uId'].toString().isEmpty ||
          userData['name'] == null || userData['name'].toString().isEmpty ||
          userData['email'] == null || userData['email'].toString().isEmpty) {
        log('User data contains null or empty required fields, clearing...');
        await SharedPreferencesSingleton.remove(kUserData);
        return false;
      }
      
      log('User data validation passed');
      return true;
    } catch (e) {
      log('Error validating user data: $e');
      log('Clearing corrupted user data...');
      await SharedPreferencesSingleton.remove(kUserData);
      return false;
    }
  }
  
  /// Clears all app data (useful for debugging)
  static Future<void> clearAllAppData() async {
    try {
      await SharedPreferencesSingleton.remove(kUserData);
      log('All app data cleared');
    } catch (e) {
      log('Error clearing app data: $e');
    }
  }
  
  /// Validates JSON string format
  static bool isValidJson(String jsonString) {
    try {
      jsonDecode(jsonString);
      return true;
    } catch (e) {
      return false;
    }
  }
}
