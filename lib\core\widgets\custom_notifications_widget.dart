import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import '../utils/responsive_utils.dart';

class CustomNotificationsWidget extends StatelessWidget {
  const CustomNotificationsWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: ShapeDecoration(
        color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
        shape: const OvalBorder(),
      ),
      child: IconButton(
        icon: Icon(
          FontAwesomeIcons.bell,
          size: ResponsiveUtils.iconSize(18),
          color: Theme.of(context).iconTheme.color,
        ),
        onPressed: () {},
      ),
    );
  }
}
