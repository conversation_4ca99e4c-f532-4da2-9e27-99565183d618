import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../core/utils/responsive_utils.dart';
import '../../../domain/entities/cart_item_entity.dart';
import '../../manager/cart_cubit/cart_cubit.dart';
import '../../manager/cart_item_cubit/cart_item_cubit.dart';

class CartItemActionButtons extends StatelessWidget {
  const CartItemActionButtons({super.key, required this.cartItem});
  final CartItemEntity cartItem;
  @override
  Widget build(BuildContext context) {
    context.initResponsive();

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        CartItemActionButton(
          iconColor: Colors.white,
          icon: Icons.add,
          color: Theme.of(context).colorScheme.primary,
          onPressed: () {
            log('➕ [ActionButtons] Increase button pressed for: ${cartItem.productEntity.name}');
            log('➕ [ActionButtons] Current quantity: ${cartItem.quantity}');

            final updatedItem = cartItem.increaseQuantity();
            log('➕ [ActionButtons] New quantity: ${updatedItem.quantity}');

            // Update the item in the cart
            context.read<CartCubit>().updateCartItem(cartItem, updatedItem);
            context.read<CartItemCubit>().updateCartItem(updatedItem);
          },
        ),
        Padding(
          padding: REdgeInsets.symmetric(horizontal: 16),
          child: Text(
            cartItem.quantity.toString(),
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
          ),
        ),
        CartItemActionButton(
          iconColor: Theme.of(context).colorScheme.onSurfaceVariant,
          icon: Icons.remove,
          color: Theme.of(context).colorScheme.surfaceContainerHighest,
          onPressed: () {
            log('➖ [ActionButtons] Decrease button pressed for: ${cartItem.productEntity.name}');
            log('➖ [ActionButtons] Current quantity: ${cartItem.quantity}');

            final updatedItem = cartItem.decreaseQuantity();
            log('➖ [ActionButtons] New quantity: ${updatedItem.quantity}');

            // Update the item in the cart
            context.read<CartCubit>().updateCartItem(cartItem, updatedItem);
            context.read<CartItemCubit>().updateCartItem(updatedItem);
          },
        )
      ],
    );
  }
}

class CartItemActionButton extends StatelessWidget {
  const CartItemActionButton(
      {super.key,
      required this.icon,
      required this.color,
      required this.onPressed,
      required this.iconColor});

  final IconData icon;
  final Color iconColor;
  final Color color;
  final VoidCallback onPressed;
  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onPressed,
        borderRadius: RBorderRadius.circular(12),
        splashColor:
            Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
        highlightColor:
            Theme.of(context).colorScheme.primary.withValues(alpha: 0.05),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 150),
          width: ResponsiveUtils.spacing(24),
          height: ResponsiveUtils.spacing(24),
          decoration: BoxDecoration(
            color: color,
            borderRadius: RBorderRadius.circular(12),
            border: Border.all(
              color: Theme.of(context).dividerColor.withValues(alpha: 0.3),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 2,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Icon(
            icon,
            color: iconColor,
            size: ResponsiveUtils.iconSize(16),
          ),
        ),
      ),
    );
  }
}
