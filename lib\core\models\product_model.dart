import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../features/reviews/data/models/review_model.dart';
import '../entities/product_entity.dart';

class ProductModel {
  final String name;
  final String description;
  final num price;
  final String category;
  final String company;
  String? imageUrl;
  final String code;
  final bool isFeatured;
  final double? discount;
  final List<String>? imageUrls;
  final String? thumbnailUrl;
  final List<File>? additionalImages;
  final List<String>? tags;
  final num? stockQuantity;
  final String? createdAt;
  final String? updatedAt;
  final List<ReviewModel> reviews;
  final num avgRating;
  final bool? isAvailable;
  final bool? isOnSale;
  final List<String>? specifications;
  final List<String>? colorsAvailable;
  final num originalPrice;
  final int sellingCount;

  ProductModel({
    required this.name,
    required this.description,
    required this.price,
    required this.category,
    required this.company,
    this.imageUrl,
    required this.code,
    required this.isFeatured,
    this.discount,
    this.imageUrls,
    this.thumbnailUrl,
    this.additionalImages,
    this.tags,
    this.stockQuantity,
    this.createdAt,
    this.updatedAt,
    required this.reviews,
    required this.avgRating,
    this.isAvailable,
    this.isOnSale,
    this.specifications,
    this.colorsAvailable,
    required this.originalPrice,
    this.sellingCount = 0,
  });

  factory ProductModel.fromJson(Map<String, dynamic> json) {
    return ProductModel(
      name: json['name'],
      description: json['description'],
      price: json['price'],
      category: json['category'],
      company: json['company'],
      imageUrl: json['imageUrl'],
      code: json['code'],
      isFeatured: json['isFeatured'],
      discount: json['discount']?.toDouble(),
      imageUrls: json['imageUrls'] != null
          ? List<String>.from(json['imageUrls'])
          : null,
      thumbnailUrl: json['thumbnailUrl'],
      additionalImages: json['additionalImages'] != null
          ? List<File>.from(json['additionalImages'])
          : null,
      tags: json['tags'] != null ? List<String>.from(json['tags']) : null,
      stockQuantity: json['stockQuantity'],
      createdAt:
          json['createdAt'] != null ? _parseDateTime(json['createdAt']) : null,
      updatedAt:
          json['updatedAt'] != null ? _parseDateTime(json['updatedAt']) : null,
      reviews: json['reviews'] != null
          ? List<ReviewModel>.from(
              json['reviews'].map((x) => ReviewModel.fromJson(x)))
          : [],
      avgRating: json['avgRating'] ?? 0,
      isAvailable: json['isAvailable'],
      isOnSale: json['isOnSale'],
      specifications: json['specifications'] != null
          ? List<String>.from(json['specifications'])
          : null,
      colorsAvailable: json['colorsAvailable'] != null
          ? List<String>.from(json['colorsAvailable'])
          : null,
      originalPrice: json['originalPrice'],
      sellingCount: json['sellingCount'] ?? 0,
    );
  }

  ProductEntity toEntity() {
    return ProductEntity(
      name: name,
      description: description,
      price: price,
      category: category,
      company: company,
      imageUrl: imageUrl,
      code: code,
      isFeatured: isFeatured,
      discount: discount,
      imageUrls: imageUrls,
      thumbnailUrl: thumbnailUrl,
      additionalImages: additionalImages,
      tags: tags,
      stockQuantity: stockQuantity,
      createdAt: createdAt,
      updatedAt: updatedAt,
      isAvailable: isAvailable,
      isOnSale: isOnSale,
      specifications: specifications,
      colorsAvailable: colorsAvailable,
      originalPrice: originalPrice,
      reviews: reviews.map((e) => e.toEntity()).toList(),
    );
  }

  factory ProductModel.fromEntity(ProductEntity entity) {
    return ProductModel(
      name: entity.name,
      description: entity.description,
      price: entity.price,
      category: entity.category,
      company: entity.company,
      imageUrl: entity.imageUrl,
      code: entity.code,
      isFeatured: entity.isFeatured,
      discount: entity.discount,
      imageUrls: entity.imageUrls,
      thumbnailUrl: entity.thumbnailUrl,
      additionalImages: entity.additionalImages,
      tags: entity.tags,
      stockQuantity: entity.stockQuantity,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      isAvailable: entity.isAvailable,
      isOnSale: entity.isOnSale,
      specifications: entity.specifications,
      colorsAvailable: entity.colorsAvailable,
      originalPrice: entity.originalPrice,
      reviews: entity.reviews.map((e) => ReviewModel.fromEntity(e)).toList(),
      avgRating: entity.reviews.isNotEmpty
          ? entity.reviews.map((e) => e.rating).reduce((a, b) => a + b) /
              entity.reviews.length
          : 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'description': description,
      'price': price,
      'category': category,
      'company': company,
      'imageUrl': imageUrl,
      'code': code,
      'isFeatured': isFeatured,
      'discount': discount,
      'imageUrls': imageUrls,
      'thumbnailUrl': thumbnailUrl,
      'additionalImages': additionalImages,
      'tags': tags,
      'stockQuantity': stockQuantity,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'reviews': reviews.map((e) => e.toJson()).toList(),
      'isAvailable': isAvailable,
      'isOnSale': isOnSale,
      'specifications': specifications,
      'colorsAvailable': colorsAvailable,
      'originalPrice': originalPrice,
      'sellingCount': sellingCount,
    };
  }

  /// Helper method to parse datetime from either Timestamp or String
  static String? _parseDateTime(dateTime) {
    if (dateTime == null) return null;

    if (dateTime is Timestamp) {
      // If it's a Firestore Timestamp, convert to ISO8601 string
      return dateTime.toDate().toIso8601String();
    } else if (dateTime is String) {
      // If it's already a string, validate and return it
      try {
        DateTime.parse(dateTime); // Validate the string format
        return dateTime;
      } catch (e) {
        // If parsing fails, return current time as fallback
        return DateTime.now().toIso8601String();
      }
    } else {
      // For any other type, return current time as fallback
      return DateTime.now().toIso8601String();
    }
  }
}
