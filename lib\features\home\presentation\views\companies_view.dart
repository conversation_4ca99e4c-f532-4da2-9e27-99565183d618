import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:skeletonizer/skeletonizer.dart';
import '../../../../core/helper/get_dummy_data.dart';
import '../../../../core/services/get_it_service.dart';
import '../../../../core/utils/responsive_utils.dart';
import '../../../../core/widgets/custom_error_widget.dart';
import '../../../auth/presentation/views/widgets/custom_app_bar.dart';
import '../../domain/entities/company_entity.dart';
import '../manager/companies_cubit/companies_cubit.dart';
import 'company_products_view.dart';
import 'widgets/company_item.dart';

class CompaniesView extends StatelessWidget {
  const CompaniesView({super.key});
  static const String routeName = '/companies';

  @override
  Widget build(BuildContext context) {
    context.initResponsive();
    return BlocProvider(
      create: (_) => CompaniesCubit(getIt.get())..getCompanies(),
      child: Scaffold(
        appBar: buildCustomAppBar(
          context,
          title: 'العلامات التجارية',
          onTap: () => Navigator.pop(context),
        ),
        body: BlocBuilder<CompaniesCubit, CompaniesState>(
          builder: (context, state) {
            if (state is CompaniesLoading) {
              return Skeletonizer(
                child: _buildCompaniesGrid(context, dummyCompanies),
              );
            } else if (state is CompaniesFailure) {
              return CustomErrorWidget(
                errMessage: state.errMessage,
                onRetry: () {
                  context.read<CompaniesCubit>().getCompanies();
                },
              );
            } else if (state is CompaniesSuccess) {
              return _buildCompaniesGrid(context, state.companies);
            }
            return const SizedBox.shrink();
          },
        ),
      ),
    );
  }

  Widget _buildCompaniesGrid(
      BuildContext context, List<CompanyEntity> companies) {
    return Padding(
      padding: REdgeInsets.all(16),
      child: GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 0.85,
          crossAxisSpacing: ResponsiveUtils.spacing(16),
          mainAxisSpacing: ResponsiveUtils.spacing(16),
        ),
        itemCount: companies.length,
        itemBuilder: (context, index) {
          return CompanyItem(
            company: companies[index],
            onTap: () {
              debugPrint('🏢 Company tapped: ${companies[index].name}');
              Navigator.pushNamed(
                context,
                CompanyProductsView.routeName,
                arguments: companies[index].name,
              );
            },
          );
        },
      ),
    );
  }
}
