abstract class DatabaseService {
  Future<void> addData({
    required String path, //? path to collection
    required Map<String, dynamic> data,
    String? documentId,
  });

  Future<void> updateData({
    required String path,
    required Map<String, dynamic> data,
    required String documentId,
  });

  Future<dynamic> getData({
    required String path,
    String? docuementId,
    Map<String, dynamic>? query,
  });

  Future<bool> checkIfDataExists({
    required String path,
    required String docuementId,
  });

  Future<void> deleteData({
    required String path,
    required String docuementId,
  });
}
