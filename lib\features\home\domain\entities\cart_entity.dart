import 'dart:developer';
import '../../../../core/entities/product_entity.dart';
import 'cart_item_entity.dart';

class CartEntity {
  final List<CartItemEntity> cartItems;

  CartEntity({required this.cartItems});

  addCartItem(CartItemEntity cartItemEntity) {
    log('📦 [CartEntity] Adding item: ${cartItemEntity.productEntity.name} - Qty: ${cartItemEntity.quantity}');
    cartItems.add(cartItemEntity);
    log('📦 [CartEntity] Item added. Total items: ${cartItems.length}');
  }

  bool isExistProduct(ProductEntity product) {
    log('🔍 [CartEntity] Checking if product exists: ${product.name} (${product.code})');
    for (var carItem in cartItems) {
      if (carItem.productEntity == product) {
        log('🔍 [CartEntity] Product found in cart');
        return true;
      }
    }
    log('🔍 [CartEntity] Product not found in cart');
    return false;
  }

  CartItemEntity getCartItem(ProductEntity product) {
    for (var carItem in cartItems) {
      if (carItem.productEntity == product) {
        return carItem;
      }
    }
    return CartItemEntity(
      productEntity: product,
      quantity: 1,
    );
  }

  num getTotalPrice() {
    num totalPrice = 0;
    for (var carItem in cartItems) {
      totalPrice += carItem.calculateTotalPrice();
    }
    return totalPrice;
  }
}
