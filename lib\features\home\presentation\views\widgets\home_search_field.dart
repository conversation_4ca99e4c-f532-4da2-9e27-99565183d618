import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../../../../core/utils/responsive_utils.dart';

class HomeSearchTextField extends StatelessWidget {
  const HomeSearchTextField({super.key, this.onTabChange});

  final ValueChanged<int>? onTabChange;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // Switch to Products tab (index 1)
        onTabChange?.call(1);
      },
      child: Container(
        decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.04),
              blurRadius: 9,
              offset: const Offset(0, 2),
            )
          ],
        ),
        child: TextField(
          enabled: false, // Make TextField non-interactive
          keyboardType: TextInputType.text,
          decoration: InputDecoration(
            prefixIcon: Icon(
              FontAwesomeIcons.magnifyingGlass,
              size: ResponsiveUtils.iconSize(18),
              color: Theme.of(context).iconTheme.color?.withValues(alpha: 0.6),
            ),
            suffixIcon: GestureDetector(
              onTap: () {
                // Switch to Products tab (index 1)
                onTabChange?.call(1);
                log('Filter button tapped - switching to products tab');
              },
              child: SvgPicture.asset(
                'assets/icons/setting_filter.svg',
                fit: BoxFit.none,
              ),
            ),
            hintStyle: Theme.of(context).textTheme.bodyMedium,
            hintText: 'ابحث عن.......',
            filled: true,
            fillColor: Theme.of(context).inputDecorationTheme.fillColor,
            border: buildBorder(),
            enabledBorder: buildBorder(),
            focusedBorder: buildBorder(),
            disabledBorder: buildBorder(),
          ),
        ),
      ),
    );
  }

  OutlineInputBorder buildBorder() {
    return OutlineInputBorder(
      borderRadius: BorderRadius.circular(4),
      borderSide: const BorderSide(
        color: Colors.white,
      ),
    );
  }
}
