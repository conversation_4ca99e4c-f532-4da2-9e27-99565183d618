import 'dart:developer';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../core/repos/products_repos/products_repo.dart';
import 'best_selling_state.dart';

class BestSellingCubit extends Cubit<BestSellingState> {
  BestSellingCubit(this.productsRepo) : super(BestSellingInitial());

  final ProductsRepo productsRepo;

  Future<void> getBestSellingProducts() async {
    log('🔥 [BestSellingCubit] Getting best selling products');
    emit(BestSellingLoading());
    
    final result = await productsRepo.getBestSellingProducts();
    
    result.fold(
      (failure) {
        log('❌ [BestSellingCubit] Error getting best selling products: ${failure.message}');
        emit(BestSellingError(failure.message));
      },
      (products) {
        log('✅ [BestSellingCubit] Got ${products.length} best selling products');
        emit(BestSellingLoaded(products));
      },
    );
  }
}
