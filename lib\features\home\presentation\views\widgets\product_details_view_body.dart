import 'package:flutter/material.dart';
import '../../../../../core/entities/product_entity.dart';
import '../../../../../core/utils/responsive_utils.dart';
import '../../../../reviews/presentation/views/widgets/product_reviews_section.dart';
import 'product_image_section.dart';
import 'product_info_section.dart';
import 'product_price_section.dart';
import 'product_quantity_section.dart';
import 'product_description_section.dart';
import 'related_products_section.dart';

class ProductDetailsViewBody extends StatefulWidget {
  const ProductDetailsViewBody({
    super.key,
    required this.product,
  });

  final ProductEntity product;

  @override
  State<ProductDetailsViewBody> createState() => _ProductDetailsViewBodyState();
}

class _ProductDetailsViewBodyState extends State<ProductDetailsViewBody> {
  int quantity = 1;

  @override
  Widget build(BuildContext context) {
    context.initResponsive();

    return CustomScrollView(
      physics: const BouncingScrollPhysics(),
      slivers: [
        // Content
        SliverToBoxAdapter(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Product Image
              ProductImageSection(product: widget.product),

              // Product Info
              Padding(
                padding: REdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const RSizedBox.height(16),

                    // Product Name & Company
                    ProductInfoSection(product: widget.product),

                    const RSizedBox.height(16),

                    // Price Section
                    ProductPriceSection(product: widget.product),

                    const RSizedBox.height(20),

                    // Quantity Section
                    ProductQuantitySection(
                      quantity: quantity,
                      onQuantityChanged: (newQuantity) {
                        setState(() {
                          quantity = newQuantity;
                        });
                      },
                    ),

                    const RSizedBox.height(20),

                    // Description
                    ProductDescriptionSection(product: widget.product),

                    const RSizedBox.height(24),
                  ],
                ),
              ),

              // Reviews Section
              ProductReviewsSection(
                productId: widget.product.code,
                productName: widget.product.name,
              ),

              const RSizedBox.height(24),

              // Related Products
              RelatedProductsSection(currentProduct: widget.product),

              const RSizedBox.height(100), // Space for bottom button
            ],
          ),
        ),
      ],
    );
  }
}
