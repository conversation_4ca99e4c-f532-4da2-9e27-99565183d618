import 'dart:developer';
import 'package:dartz/dartz.dart';
import '../../../../core/entities/product_entity.dart';
import '../../../../core/errors/failure.dart';
import '../../../../core/models/product_model.dart';
import '../../../../core/services/data_service.dart';
import '../../../../core/utils/backend_end_points.dart';
import '../../domain/entities/company_entity.dart';
import '../../domain/repos/companies_repo.dart';

class CompaniesRepoImpl implements CompaniesRepo {
  final DatabaseService databaseService;

  CompaniesRepoImpl({required this.databaseService});

  @override
  Future<Either<Failure, List<CompanyEntity>>> getCompanies() async {
    try {
      final data = await databaseService.getData(
        path: BackendEndPoints.getCompanies,
      ) as List<Map<String, dynamic>>;

      final List<CompanyEntity> companies = data.map((e) {
        final companyData = {
          'id': e['documentId'], // Use documentId from the map
          'name': e['name'],
          'image': e['image'],
        };
        log('Creating CompanyEntity with data: $companyData'); // Debug log
        return CompanyEntity.fromJson(companyData);
      }).toList();

      log('Successfully created ${companies.length} companies'); // Debug log
      return Right(companies);
    } catch (e, stackTrace) {
      log('Error in getCompanies: $e'); // Debug log
      log('Stack trace: $stackTrace'); // Debug log
      return Left(
        ServerFailure('حدث خطأ في تحميل الشركات. حاول مرة أخرى.'),
      );
    }
  }

  @override
  Future<Either<Failure, List<ProductEntity>>> getCompanyProducts(
      {required String company}) async {
    try {
      log('🔍 [CompaniesRepo] Searching for products with company: "$company"');

      final data = await databaseService.getData(
        path: BackendEndPoints.getProducts,
        query: {
          'company': company,
        },
      ) as List<Map<String, dynamic>>;

      log('📦 [CompaniesRepo] Raw data received: ${data.length} items');

      // Log first few products to see their companies
      if (data.isNotEmpty) {
        for (int i = 0; i < (data.length > 3 ? 3 : data.length); i++) {
          log('📦 Product $i: name="${data[i]['name']}", company="${data[i]['company']}"');
        }
      }

      final List<ProductEntity> products =
          data.map((e) => ProductModel.fromJson(e).toEntity()).toList();

      log('✅ [CompaniesRepo] Products Company fetched: ${products.length} products for company "$company"');

      // Log the companies of returned products
      if (products.isNotEmpty) {
        final companies = products.map((p) => p.company).toSet();
        log('📋 [CompaniesRepo] Returned product companies: $companies');
      }

      return Right(products);
    } catch (e) {
      log('❌ [CompaniesRepo] Error: $e');
      return Left(
        ServerFailure('حدث خطأ في تحميل المنتجات. حاول مرة أخرى.'),
      );
    }
  }
}
