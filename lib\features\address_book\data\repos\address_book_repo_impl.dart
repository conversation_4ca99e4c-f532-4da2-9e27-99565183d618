import 'dart:developer';
import 'package:dartz/dartz.dart';
import '../../../../core/errors/failure.dart';
import '../../../../core/services/data_service.dart';
import '../models/address_model.dart';
import '../../domain/entities/address_entity.dart';
import '../../domain/repos/address_book_repo.dart';

class AddressBookRepoImpl implements AddressBookRepo {
  final DatabaseService databaseService;
  
  // In-memory storage for demo purposes
  static final List<AddressEntity> _addresses = [];

  AddressBookRepoImpl({required this.databaseService});

  @override
  Future<Either<Failure, List<AddressEntity>>> getAddresses({required String userId}) async {
    try {
    
      final userAddresses = _addresses.where((addr) => addr.userId == userId).toList();
      return right(userAddresses);
    } catch (e) {
      log('Exception in AddressBookRepoImpl.getAddresses: ${e.toString()}');
      return left(ServerFailure('حدث خطأ في جلب العناوين'));
    }
  }

  @override
  Future<Either<Failure, void>> addAddress({required AddressEntity address}) async {
    try {
      _addresses.add(address);
      return right(null);
    } catch (e) {
      log('Exception in AddressBookRepoImpl.addAddress: ${e.toString()}');
      return left(ServerFailure('حدث خطأ في إضافة العنوان'));
    }
  }

  @override
  Future<Either<Failure, void>> updateAddress({required AddressEntity address}) async {
    try {
      final index = _addresses.indexWhere((addr) => addr.id == address.id);
      if (index != -1) {
        _addresses[index] = address;
      }
      return right(null);
    } catch (e) {
      log('Exception in AddressBookRepoImpl.updateAddress: ${e.toString()}');
      return left(ServerFailure('حدث خطأ في تحديث العنوان'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteAddress({required String addressId}) async {
    try {
      _addresses.removeWhere((addr) => addr.id == addressId);
      return right(null);
    } catch (e) {
      log('Exception in AddressBookRepoImpl.deleteAddress: ${e.toString()}');
      return left(ServerFailure('حدث خطأ في حذف العنوان'));
    }
  }

  @override
  Future<Either<Failure, void>> setDefaultAddress({required String userId, required String addressId}) async {
    try {
      // Remove default from all user addresses
      for (int i = 0; i < _addresses.length; i++) {
        if (_addresses[i].userId == userId) {
          _addresses[i] = AddressModel(
            id: _addresses[i].id,
            userId: _addresses[i].userId,
            title: _addresses[i].title,
            fullName: _addresses[i].fullName,
            phoneNumber: _addresses[i].phoneNumber,
            street: _addresses[i].street,
            city: _addresses[i].city,
            state: _addresses[i].state,
            postalCode: _addresses[i].postalCode,
            country: _addresses[i].country,
            isDefault: _addresses[i].id == addressId,
            createdAt: _addresses[i].createdAt,
          );
        }
      }
      return right(null);
    } catch (e) {
      log('Exception in AddressBookRepoImpl.setDefaultAddress: ${e.toString()}');
      return left(ServerFailure('حدث خطأ في تعيين العنوان الافتراضي'));
    }
  }

}
