import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/helper/build_app_bar.dart';
import '../../../../core/services/get_it_service.dart';
import '../../../profile/domain/repos/profile_repo.dart';
import '../../../profile/presentation/manager/profile_cubit/profile_cubit.dart';
import 'widgets/account_settings_view_body.dart';

class AccountSettingsView extends StatelessWidget {
  const AccountSettingsView({super.key});
  static const String routeName = '/account-settings';

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => ProfileCubit(getIt<ProfileRepo>()),
      child: Scaffold(
        appBar: buildAppBarWithAlarmWidget(
          context,
          title: 'إعدادات الحساب',
          isNotification: false,
        ),
        body: const AccountSettingsViewBody(),
      ),
    );
  }
}
