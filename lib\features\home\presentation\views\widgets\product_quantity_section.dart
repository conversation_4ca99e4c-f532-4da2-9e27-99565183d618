import 'package:flutter/material.dart';
import '../../../../../core/utils/responsive_utils.dart';

class ProductQuantitySection extends StatelessWidget {
  const ProductQuantitySection({
    super.key,
    required this.quantity,
    required this.onQuantityChanged,
  });

  final int quantity;
  final ValueChanged<int> onQuantityChanged;

  @override
  Widget build(BuildContext context) {
    context.initResponsive();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section Title
        Text(
          'الكمية',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
        ),
        
        const RSizedBox.height(12),
        
        // Quantity Controls
        Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
            ),
            borderRadius: RBorderRadius.circular(8),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Decrease Button
              Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: quantity > 1 
                      ? () => onQuantityChanged(quantity - 1)
                      : null,
                  borderRadius: RBorderRadius.only(
                    topRight: 8,
                    bottomRight: 8,
                  ),
                  child: Container(
                    width: ResponsiveUtils.spacing(48),
                    height: ResponsiveUtils.spacing(48),
                    decoration: BoxDecoration(
                      color: quantity > 1 
                          ? Theme.of(context).colorScheme.surfaceContainer
                          : Theme.of(context).colorScheme.surfaceContainerHighest,
                      borderRadius: RBorderRadius.only(
                        topRight: 8,
                        bottomRight: 8,
                      ),
                    ),
                    child: Icon(
                      Icons.remove,
                      size: ResponsiveUtils.iconSize(20),
                      color: quantity > 1 
                          ? Theme.of(context).colorScheme.onSurface
                          : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.4),
                    ),
                  ),
                ),
              ),
              
              // Quantity Display
              Container(
                width: ResponsiveUtils.spacing(60),
                height: ResponsiveUtils.spacing(48),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  border: Border.symmetric(
                    vertical: BorderSide(
                      color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                    ),
                  ),
                ),
                child: Center(
                  child: Text(
                    '$quantity',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                  ),
                ),
              ),
              
              // Increase Button
              Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: () => onQuantityChanged(quantity + 1),
                  borderRadius: RBorderRadius.only(
                    topLeft: 8,
                    bottomLeft: 8,
                  ),
                  child: Container(
                    width: ResponsiveUtils.spacing(48),
                    height: ResponsiveUtils.spacing(48),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary,
                      borderRadius: RBorderRadius.only(
                        topLeft: 8,
                        bottomLeft: 8,
                      ),
                    ),
                    child: Icon(
                      Icons.add,
                      size: ResponsiveUtils.iconSize(20),
                      color: Theme.of(context).colorScheme.onPrimary,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
