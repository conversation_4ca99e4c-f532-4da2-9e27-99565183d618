import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../constants.dart';
import '../../../../../core/cubits/products_cubit/products_cubit.dart';
import '../../../../../core/entities/product_entity.dart';
import '../../manager/search_filter_cubit/search_filter_cubit.dart';
import 'products_gridview_blocbuilder.dart';
import 'products_result_header.dart';
import 'search_and_sort_header.dart';

class ProductsViewBody extends StatefulWidget {
  const ProductsViewBody({super.key});

  @override
  State<ProductsViewBody> createState() => _ProductsViewBodyState();
}

class _ProductsViewBodyState extends State<ProductsViewBody> {
  @override
  void initState() {
    super.initState();
    context.read<ProductsCubit>().getProducts();
  }

  @override
  Widget build(BuildContext context) {
    return CustomScrollView(
      slivers: [
        SliverToBoxAdapter(
          child: Column(
            children: [
              const SizedBox(height: kTopPadding16),
              // Search and Sort Header
              BlocBuilder<ProductsCubit, ProductsState>(
                builder: (context, productsState) {
                  List<ProductEntity>? products;
                  if (productsState is ProductsLoaded) {
                    products = productsState.products;
                  }

                  return SearchAndSortHeader(
                    hintText: 'ابحث في جميع المنتجات...',
                    products: products,
                    onSearchChanged: (query) {
                      try {
                        if (productsState is ProductsLoaded) {
                          context.read<SearchFilterCubit>().searchProducts(
                                query,
                                productsState.products,
                              );
                        }
                      } catch (e) {
                        // Handle case where SearchFilterCubit is not available
                        debugPrint('SearchFilterCubit not found: $e');
                      }
                    },
                  );
                },
              ),
              const SizedBox(height: kVerticalPaddingSmall16),
              // Products Count Header
              BlocBuilder<ProductsCubit, ProductsState>(
                builder: (context, productsState) {
                  int productsLength = 0;

                  // Try to get search state, fallback to products state
                  try {
                    final searchState = context.read<SearchFilterCubit>().state;
                    if (searchState is ProductsFiltered) {
                      productsLength = searchState.products.length;
                    } else if (productsState is ProductsLoaded) {
                      productsLength = productsState.products.length;
                    }
                  } catch (e) {
                    // If SearchFilterCubit is not available, use products state
                    if (productsState is ProductsLoaded) {
                      productsLength = productsState.products.length;
                    }
                  }

                  return Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: kHorizontalPadding16),
                    child: ProductsResultHeader(
                      productsLength: productsLength,
                    ),
                  );
                },
              ),
              const SizedBox(height: kVerticalPaddingSmall16),
            ],
          ),
        ),
        const ProductsGridViewBlocBuilder(),
      ],
    );
  }
}
