import 'dart:io';
import 'package:bloc/bloc.dart';
import '../../../data/repos/auth_repo.dart';
import 'package:meta/meta.dart';

import '../../../domain/entities/user_entity.dart';

part 'signup_state.dart';

class SignupCubit extends Cubit<SignupState> {
  SignupCubit(this._authRepo) : super(SignupInitial());
  final AuthRepo _authRepo;

  Future<void> createUserWithEmailAndPassword({
    required String name,
    required String phone,
    required String email,
    required String password,
    File? profileImage,
  }) async {
    emit(SignupLoading());
    final result = await _authRepo.createUserWithEmailAndPassword(
      name,
      phone,
      email,
      password,
      profileImage: profileImage,
    );
    result.fold(
      (failure) => emit(SignupError(message: failure.message)),
      (userEntity) => emit(SignupSuccess(userEntity: userEntity)),
    );
  }
}
