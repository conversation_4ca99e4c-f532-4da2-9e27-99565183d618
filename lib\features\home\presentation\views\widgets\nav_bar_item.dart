import 'package:flutter/material.dart';
import 'active_item.dart';
import 'inactive_item.dart';

import '../../../domain/entities/bottom_nav_bar_entity.dart';

class NavigationBarItem extends StatelessWidget {
  const NavigationBarItem(
      {super.key, required this.isSelected, required this.bottomNavBarEntity});
  final bool isSelected;
  final BottomNavBarEntity bottomNavBarEntity;
  @override
  Widget build(BuildContext context) {
    return isSelected
        ?  ActiveItem(
            image: bottomNavBarEntity.activeImage,
            text:  bottomNavBarEntity.name,
          )
        : InActiveItem(
            image: bottomNavBarEntity.inActiveImage,
          );
  }
}