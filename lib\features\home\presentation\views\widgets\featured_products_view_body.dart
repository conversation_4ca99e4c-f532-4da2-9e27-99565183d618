import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../../../../core/cubits/products_cubit/products_cubit.dart';
import '../../../../../core/helper/get_dummy_products.dart';
import '../../../../../core/utils/app_animations_lottie.dart';
import '../../../../../core/widgets/custom_empty_state_widget.dart';
import '../../../../../core/widgets/custom_error_widget.dart';
import 'products_grid_view.dart';

class FeaturedProductsViewBody extends StatelessWidget {
  const FeaturedProductsViewBody({super.key});

  @override
  Widget build(BuildContext context) {
    return CustomScrollView(
      physics: const BouncingScrollPhysics(),
      slivers: [
        BlocBuilder<ProductsCubit, ProductsState>(
          builder: (context, state) {
            if (state is ProductsLoading) {
              return Skeletonizer.sliver(
                child: ProductsGridView(
                  products: getDummyProducts(),
                ),
              );
            }
            if (state is ProductsLoaded) {
              if (state.products.isEmpty) {
                return const SliverToBoxAdapter(
                  child: CustomEmptyStateWidget(
                    lottieAsset: AppAnimationsLottie.emptyBox,
                    title: 'لا توجد منتجات',
                    subtitle: 'لا توجد منتجات مميزة حاليا.\nتحقق لاحقا.',
                  ),
                );
              }

              return ProductsGridView(
                products: state.products,
              );
            }
            return SliverToBoxAdapter(
              child: CustomErrorWidget(
                errMessage: state is ProductsError ? state.message : '',
                onRetry: () {
                  context.read<ProductsCubit>().getFeaturedProducts();
                },
              ),
            );
          },
        ),
      ],
    );
  }
}
