import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:skeletonizer/skeletonizer.dart';
import '../../../../core/helper/get_dummy_data.dart';
import '../../../../core/services/get_it_service.dart';
import '../../../../core/utils/responsive_utils.dart';
import '../../../../core/widgets/custom_error_widget.dart';
import '../../../auth/presentation/views/widgets/custom_app_bar.dart';
import '../../domain/entities/category_entity.dart';
import '../manager/categories_cubit/categories_cubit.dart';
import 'category_products_view.dart';
import 'widgets/category_item.dart';

class CategoriesView extends StatelessWidget {
  const CategoriesView({super.key});
  static const String routeName = '/categories';

  @override
  Widget build(BuildContext context) {
    context.initResponsive();
    return BlocProvider(
      create: (_) => CategoriesCubit(getIt.get())..getCategories(),
      child: Scaffold(
        appBar: buildCustomAppBar(
          context,
          title: 'الفئات',
          onTap: () => Navigator.pop(context),
        ),
        body: BlocBuilder<CategoriesCubit, CategoriesState>(
          builder: (context, state) {
            if (state is CategoriesLoading) {
              return Skeletonizer(
                child: _buildCategoriesGrid(context, dummyCategories),
              );
            } else if (state is CategoriesFailure) {
              return CustomErrorWidget(
                errMessage: state.errMessage,
                onRetry: () {
                  context.read<CategoriesCubit>().getCategories();
                },
              );
            } else if (state is CategoriesSuccess) {
              log('Categories loaded successfully: ${state.categories.length} categories');
              for (var category in state.categories) {
                log('Category: ${category.name}, Image: ${category.image}');
              }
              return _buildCategoriesGrid(context, state.categories);
            }
            return const SizedBox.shrink();
          },
        ),
      ),
    );
  }

  Widget _buildCategoriesGrid(
      BuildContext context, List<CategoryEntity> categories) {
    return Padding(
      padding: REdgeInsets.all(16),
      child: GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 1.3,
          crossAxisSpacing: ResponsiveUtils.spacing(16),
          mainAxisSpacing: ResponsiveUtils.spacing(16),
        ),
        itemCount: categories.length,
        itemBuilder: (context, index) {
          return CategoryItem(
            category: categories[index],
            hasImage: true,
            onTap: () {
              debugPrint('🏷️ Category tapped: ${categories[index].name}');
              Navigator.pushNamed(
                context,
                CategoryProductsView.routeName,
                arguments: categories[index].name,
              );
            },
          );
        },
      ),
    );
  }
}
