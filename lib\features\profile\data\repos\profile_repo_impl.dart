import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:dartz/dartz.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../../../constants.dart';
import '../../../../core/errors/failure.dart';
import '../../../../core/helper/get_user.dart';
import '../../../../core/services/data_service.dart';
import '../../../../core/services/firebase_auth_service.dart';
import '../../../../core/services/shared_preferences_singleton.dart';
import '../../../../core/services/supabase_storage_service.dart';
import '../../../../core/utils/backend_end_points.dart';
import '../../../auth/data/models/user_model.dart';
import '../../../auth/domain/entities/user_entity.dart';
import '../../domain/repos/profile_repo.dart';

class ProfileRepoImpl implements ProfileRepo {
  final FirebaseAuthService firebaseAuthService;
  final DatabaseService databaseService;
  final SupabaseStorageService storageService;

  ProfileRepoImpl({
    required this.firebaseAuthService,
    required this.databaseService,
    required this.storageService,
  });

  @override
  Future<Either<Failure, UserEntity>> getUserProfile() async {
    try {
      // Get user from local storage first
      final userEntity = getUser();
      return right(userEntity);
    } catch (e) {
      log('Exception in ProfileRepoImpl.getUserProfile: ${e.toString()}');
      return left(ServerFailure('حدث خطأ في جلب بيانات المستخدم'));
    }
  }

  @override
  Future<Either<Failure, void>> updateUserProfile(
      {required UserEntity userEntity}) async {
    try {
      // Use addData with documentId to update (Firestore will overwrite)
      await databaseService.addData(
        path: BackendEndPoints.getUserData,
        documentId: userEntity.uId,
        data: UserModel.fromEntity(userEntity).toMap(),
      );

      // Update local storage
      await _saveUserDataLocally(userEntity);

      return right(null);
    } catch (e) {
      log('Exception in ProfileRepoImpl.updateUserProfile: ${e.toString()}');
      return left(ServerFailure('حدث خطأ في تحديث البيانات'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteUserAccount() async {
    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser != null) {
        // Note: Since DatabaseService doesn't have deleteData method,
        // we'll just delete the Firebase Auth user and clear local storage
        // The Firestore document can be handled by Firebase Functions or manually

        // Delete Firebase Auth user
        await firebaseAuthService.deleteUser();

        // Clear local storage
        await _clearUserDataLocally();
      }

      return right(null);
    } catch (e) {
      log('Exception in ProfileRepoImpl.deleteUserAccount: ${e.toString()}');
      return left(ServerFailure('حدث خطأ في حذف الحساب'));
    }
  }

  @override
  Future<Either<Failure, void>> logout() async {
    try {
      await FirebaseAuth.instance.signOut();
      return right(null);
    } catch (e) {
      log('Exception in ProfileRepoImpl.logout: ${e.toString()}');
      return left(ServerFailure('حدث خطأ في تسجيل الخروج'));
    }
  }

  @override
  Future<Either<Failure, String>> updateProfileImage({
    required File imageFile,
    required String userId,
    String? oldImageUrl,
  }) async {
    try {
      // Update image using storage service
      final imageUrl = await storageService.updateUserImage(
        newImageFile: imageFile,
        userId: userId,
        oldImageUrl: oldImageUrl,
      );

      // Get current user data
      final currentUser = getUser();

      // Create updated user with new image URL
      final updatedUser = UserEntity(
        name: currentUser.name,
        phone: currentUser.phone,
        email: currentUser.email,
        uId: currentUser.uId,
        imageUrl: imageUrl,
      );

      // Update user data in Firestore
      await databaseService.addData(
        path: BackendEndPoints.getUserData,
        documentId: userId,
        data: UserModel.fromEntity(updatedUser).toMap(),
      );

      // Update local storage
      await _saveUserDataLocally(updatedUser);

      log('Profile image updated successfully: $imageUrl');
      return right(imageUrl);
    } catch (e) {
      log('Exception in ProfileRepoImpl.updateProfileImage: ${e.toString()}');
      return left(ServerFailure('حدث خطأ في تحديث الصورة'));
    }
  }

  Future<void> _saveUserDataLocally(UserEntity userEntity) async {
    final jsonData = UserModel.fromEntity(userEntity).toMap();
    await SharedPreferencesSingleton.setString(
      kUserData,
      jsonEncode(jsonData),
    );
  }

  Future<void> _clearUserDataLocally() async {
    await SharedPreferencesSingleton.remove(kUserData);
    await SharedPreferencesSingleton.remove(kIsOnBoardingViewSeen);
  }
}
