import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import '../utils/app_assets.dart';

class CustomNetworkImage extends StatelessWidget {
  const CustomNetworkImage({
    super.key,
    required this.imageUrl,
  });

  final String imageUrl;

  @override
  Widget build(BuildContext context) {
    if (imageUrl.isEmpty) {
      return Image.asset(Assets.assetsImagesIfEmptyImage);
    }

    return CachedNetworkImage(
      imageUrl: imageUrl,
      errorWidget: (context, url, error) {
        return Image.asset(Assets.assetsImagesIfEmptyImage);
      },
      fit: BoxFit.contain,
      placeholder: (context, url) =>
          Image.asset(Assets.assetsImagesIfEmptyImage),
    );
  }
}
