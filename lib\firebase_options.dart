// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAQrRREPRcAUIgZWTP9x7nr5BMLQoeNB6A',
    appId: '1:1082612353380:web:ee93ea64a3193d94451271',
    messagingSenderId: '1082612353380',
    projectId: 'shatabuha-375f4',
    authDomain: 'shatabuha-375f4.firebaseapp.com',
    storageBucket: 'shatabuha-375f4.firebasestorage.app',
    measurementId: 'G-DE08XK2WMK',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCF0Ouqb3xvPRcPkNZF84u7sNEc6Bf1S-w',
    appId: '1:1082612353380:android:664eb3a4a2a4f485451271',
    messagingSenderId: '1082612353380',
    projectId: 'shatabuha-375f4',
    storageBucket: 'shatabuha-375f4.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCgGztDe6k0JSGnAXiORu0ebufIBP7Jkuw',
    appId: '1:1082612353380:ios:23015f4b24c22f93451271',
    messagingSenderId: '1082612353380',
    projectId: 'shatabuha-375f4',
    storageBucket: 'shatabuha-375f4.firebasestorage.app',
    iosBundleId: 'com.example.hootECommerce',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyCgGztDe6k0JSGnAXiORu0ebufIBP7Jkuw',
    appId: '1:1082612353380:ios:23015f4b24c22f93451271',
    messagingSenderId: '1082612353380',
    projectId: 'shatabuha-375f4',
    storageBucket: 'shatabuha-375f4.firebasestorage.app',
    iosBundleId: 'com.example.hootECommerce',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyAQrRREPRcAUIgZWTP9x7nr5BMLQoeNB6A',
    appId: '1:1082612353380:web:d2a15910dc3414b8451271',
    messagingSenderId: '1082612353380',
    projectId: 'shatabuha-375f4',
    authDomain: 'shatabuha-375f4.firebaseapp.com',
    storageBucket: 'shatabuha-375f4.firebasestorage.app',
    measurementId: 'G-VHS8NW4SRX',
  );
}
