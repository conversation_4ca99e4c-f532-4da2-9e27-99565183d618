part of 'reviews_cubit.dart';

abstract class ReviewsState extends Equatable {
  const ReviewsState();

  @override
  List<Object?> get props => [];
}

class ReviewsInitial extends ReviewsState {}

class ReviewsLoading extends ReviewsState {}

class ReviewActionLoading extends ReviewsState {}

class ReviewsLoaded extends ReviewsState {
  final List<ReviewEntity> reviews;

  const ReviewsLoaded({required this.reviews});

  @override
  List<Object> get props => [reviews];
}

class UserReviewsLoaded extends ReviewsState {
  final List<ReviewEntity> reviews;

  const UserReviewsLoaded({required this.reviews});

  @override
  List<Object> get props => [reviews];
}

class ReviewsError extends ReviewsState {
  final String message;

  const ReviewsError({required this.message});

  @override
  List<Object> get props => [message];
}

class ReviewActionSuccess extends ReviewsState {
  final String message;

  const ReviewActionSuccess({required this.message});

  @override
  List<Object> get props => [message];
}

class UserReviewStatusChecked extends ReviewsState {
  final bool hasReviewed;

  const UserReviewStatusChecked({required this.hasReviewed});

  @override
  List<Object> get props => [hasReviewed];
}

class ReviewStatsLoaded extends ReviewsState {
  final Map<String, dynamic> stats;

  const ReviewStatsLoaded({required this.stats});

  @override
  List<Object> get props => [stats];
}
