import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:intl/intl.dart';
import '../../../../../core/utils/order_id_formatter.dart';
import '../../../../../core/utils/responsive_utils.dart';
import '../../../../../core/widgets/custom_loading_indicator.dart';
import '../../../../../core/widgets/custom_progress_hud.dart';
import '../../../../../core/helper/build_custom_snack_bar.dart';
import '../../../domain/entities/order_status_entity.dart';
import '../../../domain/entities/user_order_entity.dart';
import '../../manager/orders_cubit/orders_cubit.dart';

class OrderDetailsViewBody extends StatelessWidget {
  const OrderDetailsViewBody({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<OrdersCubit, OrdersState>(
      listener: (context, state) {
        if (state is OrdersError) {
          showErrorSnackBar(context, message: state.message);
        }
        if (state is OrderCancelled) {
          showSuccessSnackBar(context, message: 'تم إلغاء الطلب بنجاح');
          // Navigate back to orders list after successful cancellation
          Navigator.of(context).pop();
        }
        if (state is OrderStatusUpdated) {
          showSuccessSnackBar(context, message: 'تم تحديث حالة الطلب بنجاح');
        }
      },
      builder: (context, state) {
        return CustomProgressHud(
          isLoading: state is OrdersLoading,
          child: Builder(
            builder: (context) {
              if (state is OrdersLoading && state is! OrderDetailsLoaded) {
                return Center(
                    child: CustomLoadingIndicator(
                  color: Theme.of(context).colorScheme.primary,
                ));
              }

              if (state is OrderDetailsLoaded) {
                return _buildOrderDetails(state.order, context);
              }

              if (state is OrdersError) {
                return _buildErrorState(state.message, context);
              }

              return Center(
                  child: CustomLoadingIndicator(
                color: Theme.of(context).colorScheme.primary,
              ));
            },
          ),
        );
      },
    );
  }

  Widget _buildOrderDetails(UserOrderEntity order, BuildContext context) {
    return RefreshIndicator(
      onRefresh: () async {
        // Refresh order details
        context.read<OrdersCubit>().getOrderById(orderId: order.id);
      },
      child: CustomScrollView(
        physics: const AlwaysScrollableScrollPhysics(
          parent: BouncingScrollPhysics(),
        ),
        slivers: [
          // Order Header Section
          SliverPadding(
            padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
            sliver: SliverToBoxAdapter(
              child: _buildOrderHeader(order, context),
            ),
          ),

          // Order Items Section
          SliverPadding(
            padding: const EdgeInsets.fromLTRB(16, 20, 16, 0),
            sliver: SliverToBoxAdapter(
              child: _buildOrderItems(order, context),
            ),
          ),

          // Order Info Section
          SliverPadding(
            padding: const EdgeInsets.fromLTRB(16, 20, 16, 0),
            sliver: SliverToBoxAdapter(
              child: _buildOrderInfo(order, context),
            ),
          ),

          // Action Buttons Section
          SliverPadding(
            padding: const EdgeInsets.all(16),
            sliver: SliverToBoxAdapter(
              child: _buildActionButtons(order, context),
            ),
          ),

          // Bottom spacing for better UX
          const SliverPadding(
            padding: EdgeInsets.only(bottom: 20),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(String message, BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            FontAwesomeIcons.triangleExclamation,
            size: ResponsiveUtils.iconSize(64),
            color: Theme.of(context).colorScheme.error,
          ),
          const RSizedBox.height(16),
          Text(
            message,
            style: Theme.of(context).textTheme.bodyLarge,
            textAlign: TextAlign.center,
          ),
          const RSizedBox.height(16),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('العودة'),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderHeader(UserOrderEntity order, BuildContext context) {
    return Card(
      child: Padding(
        padding: REdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  OrderIdFormatter.getDisplayText(order.id),
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                _buildStatusChip(order.status, context),
              ],
            ),
            const RSizedBox.height(12),
            Row(
              children: [
                Icon(
                  FontAwesomeIcons.calendar,
                  size: ResponsiveUtils.iconSize(16),
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                const RSizedBox.width(8),
                Text(
                  DateFormat('dd/MM/yyyy - hh:mm a', 'ar')
                      .format(order.orderDate),
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ],
            ),
            const RSizedBox.height(8),
            Row(
              children: [
                Icon(
                  FontAwesomeIcons.dollarSign,
                  size: ResponsiveUtils.iconSize(16),
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                const RSizedBox.width(8),
                Text(
                  '${order.totalAmount.toStringAsFixed(2)} جنيه',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(OrderStatus status, BuildContext context) {
    Color chipColor;
    String statusText;

    switch (status) {
      case OrderStatus.pending:
        chipColor = Colors.orange;
        statusText = 'في الانتظار';
        break;
      case OrderStatus.confirmed:
        chipColor = Colors.blue;
        statusText = 'مؤكد';
        break;
      case OrderStatus.processing:
        chipColor = Colors.purple;
        statusText = 'قيد التحضير';
        break;
      case OrderStatus.shipped:
        chipColor = Colors.indigo;
        statusText = 'تم الشحن';
        break;
      case OrderStatus.delivered:
        chipColor = Colors.green;
        statusText = 'تم التسليم';
        break;
      case OrderStatus.cancelled:
        chipColor = Colors.red;
        statusText = 'ملغي';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: chipColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: chipColor.withValues(alpha: 0.3)),
      ),
      child: Text(
        statusText,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: chipColor,
              fontWeight: FontWeight.bold,
            ),
      ),
    );
  }

  Widget _buildOrderItems(UserOrderEntity order, BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المنتجات (${order.items.length})',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 12),
            // Use ListView.builder for better performance with many items
            if (order.items.length > 5)
              SizedBox(
                height:
                    order.items.length * 100.0, // Approximate height per item
                child: ListView.builder(
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: order.items.length,
                  itemBuilder: (context, index) {
                    return _buildOrderItem(order.items[index], context);
                  },
                ),
              )
            else
              ...order.items.map((item) => _buildOrderItem(item, context)),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderItem(OrderItemEntity item, BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).dividerColor,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // Product Image
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.network(
              item.productImage,
              width: ResponsiveUtils.spacing(40),
              height: ResponsiveUtils.spacing(40),
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  width: ResponsiveUtils.spacing(40),
                  height: ResponsiveUtils.spacing(40),
                  color: Theme.of(context).colorScheme.surfaceContainerHighest,
                  child: Icon(
                    FontAwesomeIcons.image,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                );
              },
            ),
          ),
          const RSizedBox.width(12),
          // Product Details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.productName,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const RSizedBox.height(4),
                Text(
                  'الكمية: ${item.quantity}',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
                const RSizedBox.height(4),
                Text(
                  '${item.totalPrice.toStringAsFixed(2)} جنيه',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderInfo(UserOrderEntity order, BuildContext context) {
    return Card(
      child: Padding(
        padding: REdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات الطلب',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const RSizedBox.height(12),
            _buildInfoRow(
              context,
              FontAwesomeIcons.locationDot,
              'عنوان التسليم',
              order.shippingAddress,
            ),
            const RSizedBox.height(12),
            _buildInfoRow(
              context,
              FontAwesomeIcons.creditCard,
              'طريقة الدفع',
              order.paymentMethod,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(
      BuildContext context, IconData icon, String title, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          size: ResponsiveUtils.iconSize(16),
          color: Theme.of(context).colorScheme.onSurfaceVariant,
        ),
        const RSizedBox.width(8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
              ),
              const RSizedBox.height(4),
              Text(
                value,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
        ),
      ],
    );
  }

  bool _canCancelOrder(OrderStatus status) {
    // Only allow cancellation for pending and confirmed orders
    return status == OrderStatus.pending || status == OrderStatus.confirmed;
  }

  Widget _buildActionButtons(UserOrderEntity order, BuildContext context) {
    return Column(
      children: [
        if (_canCancelOrder(order.status)) ...[
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () {
                _showCancelOrderDialog(context, order);
              },
              icon: const Icon(FontAwesomeIcons.xmark),
              label: const Text('إلغاء الطلب'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
          const RSizedBox.height(12),
        ],
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('سيتم إضافة هذه الميزة قريباً')),
              );
            },
            icon: const Icon(FontAwesomeIcons.repeat),
            label: const Text('إعادة الطلب'),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
      ],
    );
  }

  void _showCancelOrderDialog(BuildContext context, UserOrderEntity order) {
    // Capture the cubit reference before showing the dialog
    final ordersCubit = context.read<OrdersCubit>();

    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: const Text('إلغاء الطلب'),
          content: const Text('هل أنت متأكد من رغبتك في إلغاء هذا الطلب؟'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(),
              child: const Text('لا'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(dialogContext).pop();
                ordersCubit.cancelOrder(
                  orderId: order.id,
                  userId: order.userId,
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('نعم، إلغاء'),
            ),
          ],
        );
      },
    );
  }
}
