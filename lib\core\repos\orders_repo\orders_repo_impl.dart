import 'package:dartz/dartz.dart';
import '../../errors/failure.dart';
import 'orders_repo.dart';
import '../../services/data_service.dart';
import '../../utils/backend_end_points.dart';
import '../../../features/checkout/data/models/order_model.dart';
import '../../../features/checkout/domain/entities/order_entity.dart';


class OrdersRepoImpl implements OrdersRepo {
  final DatabaseService dataBaseService;

  OrdersRepoImpl(this.dataBaseService);
  @override
  Future<Either<Failure, void>> addOrder({required OrderEntity order}) async {
    try {
      await dataBaseService.addData(
        path: BackendEndPoints.addOrder,
        data: OrderModel.fromEntity(order).toJson(),
      );
      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }
}
