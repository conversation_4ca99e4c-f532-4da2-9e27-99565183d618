import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/cubits/products_cubit/products_cubit.dart';
import '../../../../core/entities/product_entity.dart';
import '../../../../core/helper/build_app_bar.dart';
import '../../../../core/repos/products_repos/products_repo.dart';
import '../../../../core/services/get_it_service.dart';
import '../../../../core/helper/build_custom_snack_bar.dart';
import '../../domain/repos/categories_repo.dart';
import '../manager/cart_cubit/cart_cubit.dart';
import '../manager/categories_cubit/categories_cubit.dart';
import 'widgets/bottom_cart_btn_product_details_view.dart';
import 'widgets/product_details_view_body.dart';

class ProductDetailsView extends StatelessWidget {
  const ProductDetailsView({
    super.key,
    required this.product,
  });

  final ProductEntity product;

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => ProductsCubit(
            getIt.get<ProductsRepo>(),
          ),
        ),
        BlocProvider(
          create: (context) => CategoriesCubit(
            getIt.get<CategoriesRepo>(),
          ),
        ),
        BlocProvider.value(
          value: getIt<CartCubit>(),
        ),
      ],
      child: BlocListener<CartCubit, CartState>(
        listener: (context, state) {
          if (state is CartItemAdded) {
            showSuccessSnackBar(
              context,
              message: 'تم إضافة ${product.name} إلى السلة بنجاح',
            );
          }
        },
        child: Scaffold(
          appBar: buildAppBarWithAlarmWidget(
            context,
            title: product.name,
            isNotification: false,
          ),
          body: ProductDetailsViewBody(product: product),
          bottomNavigationBar:
              BottomCartBtnProductDetailsView(product: product),
        ),
      ),
    );
  }
}
