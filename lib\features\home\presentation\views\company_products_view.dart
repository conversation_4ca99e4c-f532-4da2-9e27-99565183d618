import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/helper/build_app_bar.dart';
import '../../../../core/services/get_it_service.dart';
import '../../domain/repos/companies_repo.dart';
import '../manager/companies_cubit/companies_cubit.dart';
import '../manager/cart_cubit/cart_cubit.dart';
import '../manager/search_filter_cubit/search_filter_cubit.dart';
import 'widgets/company_products_view_body.dart';

class CompanyProductsView extends StatelessWidget {
  const CompanyProductsView({
    super.key,
    required this.companyName,
  });

  final String companyName;
  static const String routeName = '/company-products';

  @override
  Widget build(BuildContext context) {
    debugPrint(
        '🏢 [CompanyProductsView] Building view for company: "$companyName"');

    return Scaffold(
      appBar: buildAppBarWithAlarmWidget(
        context,
        title: companyName,
        isBack: true,
        isNotification: false,
      ),
      body: MultiBlocProvider(
        providers: [
          BlocProvider(
            create: (context) => CompaniesCubit(
              getIt.get<CompaniesRepo>(),
            )..getCompanyProducts(
                company: companyName,
              ),
          ),
          BlocProvider(
            create: (context) => SearchFilterCubit(),
          ),
          BlocProvider.value(
            value: getIt<CartCubit>(),
          ),
        ],
        child: CompanyProductsViewBody(companyName: companyName),
      ),
    );
  }
}
