import 'dart:developer';
import 'package:dartz/dartz.dart';
import '../../../../core/entities/product_entity.dart';
import '../../../../core/errors/failure.dart';
import '../../../../core/models/product_model.dart';
import '../../../../core/services/data_service.dart';
import '../../../../core/utils/backend_end_points.dart';
import '../../domain/entities/category_entity.dart';
import '../../domain/repos/categories_repo.dart';

class CategoriesRepoImpl implements CategoriesRepo {
  final DatabaseService databaseService;

  CategoriesRepoImpl({required this.databaseService});

  @override
  Future<Either<Failure, List<CategoryEntity>>> getCategories() async {
    try {
      final data = await databaseService.getData(
        path: BackendEndPoints.getCategories,
      ) as List<Map<String, dynamic>>;

      final List<CategoryEntity> categories = data.map((e) {
        log('Category data: $e');
        final categoryData = {
          'id': e['documentId'], // Use documentId from the map
          'name': e['name'] ?? '',
          'image': e['image'] ?? '',
          'description': e['description'],
          'createdAt': e['createdAt'],
          'updatedAt': e['updatedAt'],
        };
        return CategoryEntity.fromJson(categoryData);
      }).toList();

      return Right(categories);
    } catch (e) {
      return Left(
        ServerFailure('حدث خطأ في تحميل الفئات. حاول مرة أخرى.'),
      );
    }
  }

  @override
  Future<Either<Failure, List<ProductEntity>>> getCategoryProducts(
      {required String category}) async {
    try {
      log('🔍 [CategoriesRepo] Searching for products with category: "$category"');

      final data = await databaseService.getData(
        path: BackendEndPoints.getProducts,
        query: {'category': category},
      ) as List<Map<String, dynamic>>;

      log('📦 [CategoriesRepo] Raw data received: ${data.length} items');

      // Log first few products to see their categories
      if (data.isNotEmpty) {
        for (int i = 0; i < (data.length > 3 ? 3 : data.length); i++) {
          log('📦 Product $i: name="${data[i]['name']}", category="${data[i]['category']}"');
        }
      }

      final List<ProductEntity> products =
          data.map((e) => ProductModel.fromJson(e).toEntity()).toList();

      log('✅ [CategoriesRepo] Products Category fetched: ${products.length} products for category "$category"');

      // Log the categories of returned products
      if (products.isNotEmpty) {
        final categories = products.map((p) => p.category).toSet();
        log('📋 [CategoriesRepo] Returned product categories: $categories');
      }

      return Right(products);
    } catch (e) {
      log('❌ [CategoriesRepo] Error: $e');
      return Left(
        ServerFailure('حدث خطأ في تحميل المنتجات. حاول مرة أخرى.'),
      );
    }
  }
}
