import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../core/cubits/products_cubit/products_cubit.dart';
import '../../../../../core/helper/get_dummy_products.dart';
import '../../../../../core/widgets/custom_err_widget.dart';
import '../../../../../core/widgets/custom_empty_state_widget.dart';
import '../../../../../core/utils/app_animations_lottie.dart';
import '../../manager/search_filter_cubit/search_filter_cubit.dart';
import 'products_grid_view.dart';
import 'package:skeletonizer/skeletonizer.dart';

class ProductsGridViewBlocBuilder extends StatelessWidget {
  const ProductsGridViewBlocBuilder({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    // First check if SearchFilterCubit is available
    try {
      return BlocBuilder<SearchFilterCubit, SearchFilterState>(
        builder: (context, searchState) {
          // If we have search results, show them
          if (searchState is ProductsFiltered) {
            if (searchState.products.isEmpty) {
              return SliverToBoxAdapter(
                child: CustomEmptyStateWidget(
                  lottieAsset: AppAnimationsLottie.emptySearch,
                  title: 'لا توجد نتائج',
                  subtitle: searchState.searchQuery.isNotEmpty
                      ? 'لم نجد منتجات تطابق "${searchState.searchQuery}"'
                      : 'لا توجد منتجات حاليا.',
                ),
              );
            }

            return ProductsGridView(
              products: searchState.products,
            );
          }

          // Otherwise, show original products
          return _buildOriginalProductsView(context);
        },
      );
    } catch (e) {
      // If SearchFilterCubit is not available, just show original products
      return _buildOriginalProductsView(context);
    }
  }

  Widget _buildOriginalProductsView(BuildContext context) {
    return BlocBuilder<ProductsCubit, ProductsState>(
      builder: (context, state) {
        if (state is ProductsLoading) {
          return Skeletonizer.sliver(
            child: ProductsGridView(
              products: getDummyProducts(),
            ),
          );
        }

        if (state is ProductsLoaded) {
          if (state.products.isEmpty) {
            return const SliverToBoxAdapter(
              child: CustomEmptyStateWidget(
                lottieAsset: AppAnimationsLottie.emptyBox,
                title: 'لا توجد منتجات',
                subtitle: 'لا توجد منتجات متاحة حاليا.\nتحقق لاحقا.',
              ),
            );
          }

          return ProductsGridView(
            products: state.products,
          );
        }

        return SliverToBoxAdapter(
          child: CustomErrorWidget(
            errorMessage: state is ProductsError ? state.message : '',
            onRetry: () {
              context.read<ProductsCubit>().getProducts();
            },
          ),
        );
      },
    );
  }
}
