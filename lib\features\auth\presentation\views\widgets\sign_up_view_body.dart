import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../core/helper/build_custom_snack_bar.dart';
import '../../../../../core/utils/responsive_utils.dart';
import '../../manager/signUp_cubit/signup_cubit.dart';
import 'custom_phone_field.dart';
import 'dont_have_an_account.dart';
import 'image_picker_widget.dart';
import 'terms_and_conditions.dart';

import '../../../../../constants.dart';
import '../../../../../core/widgets/custom_button.dart';
import '../../../../../core/widgets/custom_pass_field.dart';
import '../../../../../core/widgets/custom_text_field.dart';

class SignUpViewBody extends StatefulWidget {
  const SignUpViewBody({super.key});

  @override
  State<SignUpViewBody> createState() => _SignUpViewBodyState();
}

class _SignUpViewBodyState extends State<SignUpViewBody> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  AutovalidateMode _autovalidateMode = AutovalidateMode.disabled;
  late String _name, _phone, _email, _password;
  String _countryCode = '20';
  late bool isTermsAccepted = false;
  File? _profileImage;
  @override
  Widget build(BuildContext context) {
    context.initResponsive();
    return SingleChildScrollView(
      physics: const BouncingScrollPhysics(),
      child: Padding(
        padding: REdgeInsets.symmetric(horizontal: kHorizontalPadding16),
        child: Form(
          key: _formKey,
          autovalidateMode: _autovalidateMode,
          child: Column(
            children: [
              const RSizedBox.height(24),
              // Profile Image Picker
              ImagePickerWidget(
                onImageSelected: (image) {
                  _profileImage = image;
                },
              ),
              const RSizedBox.height(24),
              CustomTextFormField(
                label: 'الاسم كامل',
                keyboardType: TextInputType.name,
                onSaved: (p0) {
                  _name = p0!;
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'برجاء إدخال الاسم كامل';
                  }
                  return null;
                },
              ),
              const RSizedBox.height(16),
              CustomPhoneField(
                onCountryChanged: (countryCode) {
                  _countryCode = countryCode;
                },
                onSaved: (p0) {
                  _phone = '+$_countryCode${p0!.number}';
                },
                hintText: 'ادخل رقم هاتفك',
                validator: (p0) {
                  if (p0 == null || p0.number.isEmpty) {
                    return 'برجاء إدخال رقم الهاتف';
                  }

                  return null;
                },
              ),
              const RSizedBox.height(16),
              CustomTextFormField(
                label: 'البريد الالكتروني',
                keyboardType: TextInputType.emailAddress,
                onSaved: (p0) {
                  _email = p0!;
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'برجاء إدخال البريد الالكتروني';
                  }
                  if (!RegExp(r'^[^@]+@[^@]+\.[^@]+').hasMatch(value)) {
                    return 'برجاء إدخال بريد الكتروني صحيح';
                  }
                  return null;
                },
              ),
              const RSizedBox.height(16),
              CustomPasswordField(
                isLoginView: false, //? to handle validation
                onSaved: (p0) {
                  _password = p0!;
                },
              ),
              const RSizedBox.height(16),
              TermsAndConditions(
                onCheckboxChanged: (value) {
                  setState(() {
                    isTermsAccepted = value;
                  });
                },
              ),
              const RSizedBox.height(32),
              CustomButton(
                text: 'إنشاء حساب جديد',
                bgColor: Theme.of(context).colorScheme.primary,
                textColor: Colors.white,
                onPressed: () {
                  if (_formKey.currentState!.validate()) {
                    _formKey.currentState!.save();
                    if (isTermsAccepted) {
                      context
                          .read<SignupCubit>()
                          .createUserWithEmailAndPassword(
                            name: _name,
                            phone: _phone,
                            email: _email,
                            password: _password,
                            profileImage: _profileImage,
                          );
                    } else {
                      showWarningSnackBar(
                        context,
                        message: 'يجب عليك الموافقة على الشروط والاحكام!',
                      );
                    }
                  } else {
                    setState(() {
                      _autovalidateMode = AutovalidateMode.always;
                    });
                  }
                },
              ),
              const RSizedBox.height(26),
              buildDontHavaAnAccount(
                context,
                title: 'تمتلك حساب بالفعل؟',
                titleBtn: ' تسجيل دخول',
                onTap: () {
                  Navigator.pop(context);
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
