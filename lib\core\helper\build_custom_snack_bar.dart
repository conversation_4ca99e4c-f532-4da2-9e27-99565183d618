import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../utils/responsive_utils.dart';

enum SnackBarType {
  success,
  error,
  warning,
  info,
}

void buildCustomSnackBar(
  BuildContext context, {
  required String message,
  SnackBarType type = SnackBarType.info,
  Duration duration = const Duration(seconds: 2),
  String? actionLabel,
  VoidCallback? onActionPressed,
  bool showCloseButton = true,
}) {
  context.initResponsive();

  final theme = Theme.of(context);
  final colorScheme = theme.colorScheme;

  // Get colors and icons based on type
  final snackBarConfig = _getSnackBarConfig(type, colorScheme);

  ScaffoldMessenger.of(context).clearSnackBars();
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(
      content: _buildSnackBarContent(
        context,
        message,
        snackBarConfig,
        showCloseButton,
      ),
      backgroundColor: snackBarConfig.backgroundColor,
      duration: duration,
      behavior: SnackBarBehavior.floating,
      margin: REdgeInsets.all(8),
      shape: RoundedRectangleBorder(
        borderRadius: RBorderRadius.circular(8),
      ),
      elevation: ResponsiveUtils.elevation(8),
      action: actionLabel != null
          ? SnackBarAction(
              label: actionLabel,
              textColor: snackBarConfig.actionColor,
              onPressed: onActionPressed ?? () {},
            )
          : null,
    ),
  );
}

class _SnackBarConfig {
  final Color backgroundColor;
  final Color textColor;
  final Color iconColor;
  final Color actionColor;
  final IconData icon;

  _SnackBarConfig({
    required this.backgroundColor,
    required this.textColor,
    required this.iconColor,
    required this.actionColor,
    required this.icon,
  });
}

_SnackBarConfig _getSnackBarConfig(SnackBarType type, ColorScheme colorScheme) {
  switch (type) {
    case SnackBarType.success:
      return _SnackBarConfig(
        backgroundColor: Colors.green.shade600,
        textColor: Colors.white,
        iconColor: Colors.white,
        actionColor: Colors.green.shade100,
        icon: FontAwesomeIcons.circleCheck,
      );
    case SnackBarType.error:
      return _SnackBarConfig(
        backgroundColor: Colors.red.shade600,
        textColor: Colors.white,
        iconColor: Colors.white,
        actionColor: Colors.red.shade100,
        icon: FontAwesomeIcons.circleXmark,
      );
    case SnackBarType.warning:
      return _SnackBarConfig(
        backgroundColor: Colors.orange.shade600,
        textColor: Colors.white,
        iconColor: Colors.white,
        actionColor: Colors.orange.shade100,
        icon: FontAwesomeIcons.triangleExclamation,
      );
    case SnackBarType.info:
      return _SnackBarConfig(
        backgroundColor: colorScheme.primary,
        textColor: colorScheme.onPrimary,
        iconColor: colorScheme.onPrimary,
        actionColor: colorScheme.primaryContainer,
        icon: FontAwesomeIcons.circleInfo,
      );
  }
}

Widget _buildSnackBarContent(
  BuildContext context,
  String message,
  _SnackBarConfig config,
  bool showCloseButton,
) {
  return Row(
    children: [
      // Icon
      Container(
        padding: REdgeInsets.all(8),
        decoration: BoxDecoration(
          color: config.iconColor.withValues(alpha: 0.2),
          borderRadius: RBorderRadius.circular(8),
        ),
        child: Icon(
          config.icon,
          color: config.iconColor,
          size: ResponsiveUtils.iconSize(12),
        ),
      ),
      const RSizedBox.width(12),

      // Message
      Expanded(
        child: Text(
          message,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: config.textColor,
                fontWeight: FontWeight.w500,
              ),
        ),
      ),

      // Close button
      if (showCloseButton) ...[
        const RSizedBox.width(8),
        InkWell(
          onTap: () => ScaffoldMessenger.of(context).hideCurrentSnackBar(),
          borderRadius: RBorderRadius.circular(8),
          child: Container(
            padding: REdgeInsets.all(4),
            child: Icon(
              FontAwesomeIcons.xmark,
              color: config.iconColor.withValues(alpha: 0.8),
              size: ResponsiveUtils.iconSize(12),
            ),
          ),
        ),
      ],
    ],
  );
}

// Convenience methods for different types
void showSuccessSnackBar(
  BuildContext context, {
  required String message,
  Duration duration = const Duration(seconds: 2),
  String? actionLabel,
  VoidCallback? onActionPressed,
}) {
  buildCustomSnackBar(
    context,
    message: message,
    type: SnackBarType.success,
    duration: duration,
    actionLabel: actionLabel,
    onActionPressed: onActionPressed,
  );
}

void showErrorSnackBar(
  BuildContext context, {
  required String message,
  Duration duration = const Duration(seconds: 3),
  String? actionLabel,
  VoidCallback? onActionPressed,
}) {
  buildCustomSnackBar(
    context,
    message: message,
    type: SnackBarType.error,
    duration: duration,
    actionLabel: actionLabel,
    onActionPressed: onActionPressed,
  );
}

void showWarningSnackBar(
  BuildContext context, {
  required String message,
  Duration duration = const Duration(seconds: 4),
  String? actionLabel,
  VoidCallback? onActionPressed,
}) {
  buildCustomSnackBar(
    context,
    message: message,
    type: SnackBarType.warning,
    duration: duration,
    actionLabel: actionLabel,
    onActionPressed: onActionPressed,
  );
}

void showInfoSnackBar(
  BuildContext context, {
  required String message,
  Duration duration = const Duration(seconds: 3),
  String? actionLabel,
  VoidCallback? onActionPressed,
}) {
  buildCustomSnackBar(
    context,
    message: message,
    type: SnackBarType.info,
    duration: duration,
    actionLabel: actionLabel,
    onActionPressed: onActionPressed,
  );
}
