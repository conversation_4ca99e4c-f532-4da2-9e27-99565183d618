import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:skeletonizer/skeletonizer.dart';
import '../../../../../constants.dart';
import '../../../../../core/helper/get_dummy_products.dart';
import '../../../../../core/widgets/custom_error_widget.dart';
import '../../manager/best_selling_cubit/best_selling_cubit.dart';
import '../../manager/best_selling_cubit/best_selling_state.dart';
import 'products_grid_view.dart';

class BestSellingViewBody extends StatelessWidget {
  const BestSellingViewBody({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: kHorizontalPadding16),
      child: BlocBuilder<BestSellingCubit, BestSellingState>(
        builder: (context, state) {
          if (state is BestSellingLoading) {
            return Skeletonizer(
              child: CustomScrollView(slivers: [
                ProductsGridView(
                  products: getDummyProducts(),
                ),
              ]),
            );
          } else if (state is BestSellingError) {
            return Center(
              child: CustomErrorWidget(
                errMessage: state.message,
                onRetry: () {
                  context.read<BestSellingCubit>().getBestSellingProducts();
                },
              ),
            );
          } else if (state is BestSellingLoaded) {
            if (state.products.isEmpty) {
              return const Center(
                child: Text(
                  'لا توجد منتجات متاحة حاليًا',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              );
            }

            return CustomScrollView(
              slivers: [
                const SliverToBoxAdapter(
                  child: SizedBox(height: 24),
                ),
                ProductsGridView(
                  products: state.products,
                ),
              ],
            );
          }

          return const SizedBox.shrink();
        },
      ),
    );
  }
}
