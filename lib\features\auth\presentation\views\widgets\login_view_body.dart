import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../constants.dart';
import '../../../../../core/utils/responsive_utils.dart';
import '../../../../../core/widgets/custom_button.dart';
import '../../../../../core/widgets/custom_text_field.dart';
import '../../manager/login_cubit/login_cubit.dart';
import 'custom_social_btn.dart';
import 'dont_have_an_account.dart';
import 'or_divider.dart';

import '../../../../../core/widgets/custom_pass_field.dart';
import '../forget_password_view.dart';
import '../sign_up_view.dart';

class LoginViewBody extends StatefulWidget {
  const LoginViewBody({super.key});

  @override
  State<LoginViewBody> createState() => _LoginViewBodyState();
}

class _LoginViewBodyState extends State<LoginViewBody> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  AutovalidateMode _autovalidateMode = AutovalidateMode.disabled;
  late String _email, _password;
  @override
  Widget build(BuildContext context) {
    context.initResponsive();
    return SingleChildScrollView(
      physics: const BouncingScrollPhysics(),
      child: Padding(
        padding: REdgeInsets.symmetric(horizontal: kHorizontalPadding16),
        child: Form(
          key: _formKey,
          autovalidateMode: _autovalidateMode,
          child: Column(
            children: [
              const RSizedBox.height(24),
              CustomTextFormField(
                onSaved: (p0) {
                  _email = p0!;
                },
                label: 'البريد الالكتروني',
                keyboardType: TextInputType.emailAddress,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'برجاء ادخال البريد الالكتروني';
                  }
                  if (!RegExp(r'^[^@]+@[^@]+\.[^@]+').hasMatch(value)) {
                    return 'برجاء ادخال بريد الكتروني صحيح';
                  }
                  return null;
                },
              ),
              const RSizedBox.height(16),
              CustomPasswordField(
                isLoginView: true, //? to handle validation
                onSaved: (p0) {
                  _password = p0!;
                },
              ),
              const RSizedBox.height(16),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  InkWell(
                    onTap: () {
                      Navigator.pushNamed(
                          context, ForgetPasswordView.routeName);
                    },
                    child: Text(
                      'هل نسيت كلمة المرور؟',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Theme.of(context).colorScheme.primary,
                          ),
                    ),
                  ),
                ],
              ),
              const RSizedBox.height(32),
              CustomButton(
                text: 'تسجيل الدخول',
                bgColor: Theme.of(context).colorScheme.primary,
                textColor: Colors.white,
                onPressed: () {
                  if (_formKey.currentState!.validate()) {
                    _formKey.currentState!.save();
                    context.read<LoginCubit>().signInWithEmailAndPassword(
                          email: _email,
                          password: _password,
                        );
                  } else {
                    setState(() {
                      _autovalidateMode = AutovalidateMode.always;
                    });
                  }
                },
              ),
              const RSizedBox.height(32),
              buildDontHavaAnAccount(
                context,
                title: 'لا تمتلك حساب؟',
                titleBtn: ' قم بإنشاء حساب',
                onTap: () {
                  Navigator.pushNamed(context, SignUpView.routeName);
                },
              ),
              const RSizedBox.height(32),
              buildOrDivider(context),
              const RSizedBox.height(16),
              CustomSocialButton(
                label: 'تسجيل بواسطة جوجل',
                svgAssetPath: 'assets/icons/google.svg',
                onPressed: () {
                  context.read<LoginCubit>().signInWithGoogle();
                },
              ),
              const RSizedBox.height(16),
              CustomSocialButton(
                label: 'تسجيل بواسطة فيسبوك',
                svgAssetPath: 'assets/icons/fb.svg',
                onPressed: () {
                  context.read<LoginCubit>().signInWithFacebook();
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
