import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../../../core/utils/responsive_utils.dart';

class CustomSocialButton extends StatelessWidget {
  final String label;
  final String svgAssetPath;
  final VoidCallback onPressed;

  const CustomSocialButton({
    super.key,
    required this.label,
    required this.svgAssetPath,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPressed,
      child: Container(
        width: double.infinity,
        padding: REdgeInsets.symmetric(vertical: 12, horizontal: 20),
        decoration: BoxDecoration(
          border: Border.all(color: Theme.of(context).dividerColor),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            SvgPicture.asset(
              svgAssetPath,
              height: ResponsiveUtils.iconSize(20),
              width: ResponsiveUtils.iconSize(20),
            ),
            const RSizedBox.width(60),
            Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }
}
