class CategoryEntity {
  final String id;
  final String name;
  final String image;
  final String? description;
  final String? createdAt;
  final String? updatedAt;

  CategoryEntity({
    required this.id,
    required this.name,
    required this.image,
    this.description,
    this.createdAt,
    this.updatedAt,
  });

  factory CategoryEntity.fromJson(Map<String, dynamic> json) {
    return CategoryEntity(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      image: json['image'] ?? '',
      description: json['description'],
      createdAt: json['createdAt'],
      updatedAt: json['updatedAt'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'image': image,
      'description': description,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CategoryEntity &&
        other.id == id &&
        other.name == name &&
        other.image == image;
  }

  @override
  int get hashCode => id.hashCode ^ name.hashCode ^ image.hashCode;

  @override
  String toString() =>
      'CategoryEntity(id: $id, name: $name, image: $image, description: $description, createdAt: $createdAt, updatedAt: $updatedAt)';
}
