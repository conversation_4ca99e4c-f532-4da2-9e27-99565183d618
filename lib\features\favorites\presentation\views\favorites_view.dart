import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/services/get_it_service.dart';
import '../../../auth/presentation/views/widgets/custom_app_bar.dart';
import '../../../home/<USER>/manager/cart_cubit/cart_cubit.dart';
import '../../domain/repos/favorites_repo.dart';
import '../manager/favorites_cubit/favorites_cubit.dart';
import 'widgets/favorites_view_body.dart';

class FavoritesView extends StatelessWidget {
  const FavoritesView({super.key});
  static const String routeName = '/favorites';

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => FavoritesCubit(getIt<FavoritesRepo>()),
        ),
        BlocProvider.value(
          value: getIt<CartCubit>(),
        ),
      ],
      child: Scaffold(
        appBar: buildCustomAppBar(
          context,
          title: 'المفضلة',
          onTap: () => Navigator.pop(context),
        ),
        body: const FavoritesViewBody(),
      ),
    );
  }
}
