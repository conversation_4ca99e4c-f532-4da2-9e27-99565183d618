import 'package:firebase_auth/firebase_auth.dart';
import '../../domain/entities/user_entity.dart';

class UserModel extends UserEntity {
  UserModel({
    required super.name,
    required super.phone,
    required super.email,
    required super.uId,
    super.imageUrl,
  });

  factory UserModel.fromFirebaseUser(User user) {
    return UserModel(
      name: user.displayName ?? '',
      phone: user.phoneNumber ?? '',
      email: user.email ?? '',
      uId: user.uid,
      imageUrl: user.photoURL,
    );
  }

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      name: json['name'],
      phone: json['phone'],
      email: json['email'],
      uId: json['uId'],
      imageUrl: json['imageUrl'],
    );
  }

  factory UserModel.fromEntity(UserEntity user) {
    return UserModel(
      name: user.name,
      phone: user.phone,
      email: user.email,
      uId: user.uId,
      imageUrl: user.imageUrl,
    );
  }

  toMap() {
    return {
      'name': name,
      'phone': phone,
      'email': email,
      'uId': uId,
      'imageUrl': imageUrl,
    };
  }
}
