# Custom Dialog Widget

A beautiful, customizable dialog widget that follows the app's design system and provides consistent user experience across the application.

## Features

- 🎨 **Beautiful Design**: Modern, clean design with rounded corners and shadows
- 🌈 **Customizable Colors**: Support for custom colors and themes
- 📱 **Responsive**: Uses ScreenUtil for consistent sizing across devices
- 🔧 **Flexible**: Multiple predefined types and full customization options
- 🌍 **RTL Support**: Fully supports Arabic text and RTL layout
- ⚡ **Easy to Use**: Simple API with sensible defaults

## Usage

### Basic Usage

```dart
import 'package:your_app/core/widgets/custom_dialog.dart';

// Simple dialog
showCustomDialog(
  context: context,
  title: 'عنوان الحوار',
  content: 'محتوى الرسالة هنا',
  onPrimaryPressed: () {
    Navigator.of(context).pop();
    // Handle action
  },
);
```

### Predefined Dialog Types

#### 1. Warning Dialog
```dart
DialogTypes.showWarningDialog(
  context: context,
  title: 'تحذير',
  content: 'هذا الإجراء قد يؤثر على بياناتك',
  onConfirm: () {
    // Handle confirmation
  },
);
```

#### 2. Error Dialog
```dart
DialogTypes.showErrorDialog(
  context: context,
  title: 'حدث خطأ',
  content: 'عذراً، حدث خطأ غير متوقع',
  onConfirm: () {
    // Handle error acknowledgment
  },
);
```

#### 3. Success Dialog
```dart
DialogTypes.showSuccessDialog(
  context: context,
  title: 'تم بنجاح',
  content: 'تمت العملية بنجاح!',
  onConfirm: () {
    // Handle success
  },
);
```

#### 4. Confirmation Dialog
```dart
DialogTypes.showConfirmationDialog(
  context: context,
  title: 'تأكيد العملية',
  content: 'هل أنت متأكد من رغبتك في المتابعة؟',
  confirmButtonText: 'نعم',
  cancelButtonText: 'لا',
  isDanger: false, // Set to true for dangerous actions
  onConfirm: () {
    // Handle confirmation
  },
  onCancel: () {
    // Handle cancellation
  },
);
```

### Advanced Customization

```dart
showCustomDialog(
  context: context,
  title: 'حوار مخصص',
  content: 'محتوى مخصص مع تصميم خاص',
  icon: FontAwesomeIcons.star,
  iconColor: Colors.purple,
  primaryButtonColor: Colors.purple,
  primaryButtonText: 'موافق',
  secondaryButtonText: 'إلغاء',
  showSecondaryButton: true,
  isDanger: false,
  barrierDismissible: true,
  onPrimaryPressed: () {
    // Handle primary action
  },
  onSecondaryPressed: () {
    // Handle secondary action
  },
);
```

## Parameters

### CustomDialog Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `title` | `String` | Required | Dialog title |
| `content` | `String` | Required | Dialog content/message |
| `icon` | `IconData?` | `null` | Optional icon to display |
| `iconColor` | `Color?` | `null` | Custom icon color |
| `primaryButtonText` | `String` | `'موافق'` | Primary button text |
| `secondaryButtonText` | `String` | `'إلغاء'` | Secondary button text |
| `onPrimaryPressed` | `VoidCallback?` | `null` | Primary button callback |
| `onSecondaryPressed` | `VoidCallback?` | `null` | Secondary button callback |
| `showSecondaryButton` | `bool` | `true` | Show/hide secondary button |
| `primaryButtonColor` | `Color?` | `null` | Custom primary button color |
| `isDanger` | `bool` | `false` | Apply danger styling |

### showCustomDialog Parameters

All CustomDialog parameters plus:

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `context` | `BuildContext` | Required | Build context |
| `barrierDismissible` | `bool` | `true` | Allow dismissing by tapping outside |

## Styling

The dialog automatically adapts to your app's theme and uses:

- **Colors**: `AppColors.primaryColor`, `AppColors.textColor`, `AppColors.lightTextColor`
- **Text Styles**: `TextStyles.bold19`, `TextStyles.regular16`, `TextStyles.bold16`
- **Responsive Sizing**: ScreenUtil for consistent sizing

## Migration from AlertDialog

### Before (AlertDialog)
```dart
showDialog(
  context: context,
  builder: (context) => AlertDialog(
    title: Text('Title'),
    content: Text('Content'),
    actions: [
      TextButton(
        onPressed: () => Navigator.pop(context),
        child: Text('Cancel'),
      ),
      TextButton(
        onPressed: () {
          Navigator.pop(context);
          // Handle action
        },
        child: Text('OK'),
      ),
    ],
  ),
);
```

### After (CustomDialog)
```dart
DialogTypes.showConfirmationDialog(
  context: context,
  title: 'Title',
  content: 'Content',
  confirmButtonText: 'OK',
  cancelButtonText: 'Cancel',
  onConfirm: () {
    Navigator.pop(context);
    // Handle action
  },
  onCancel: () => Navigator.pop(context),
);
```

## Best Practices

1. **Use Predefined Types**: Use `DialogTypes` for common scenarios
2. **Consistent Messaging**: Keep titles short and content clear
3. **Action Clarity**: Use clear, action-oriented button text
4. **Dangerous Actions**: Always use `isDanger: true` for destructive actions
5. **Navigation**: Always handle navigation in callbacks
6. **Accessibility**: The dialog includes proper semantic labels

## Examples

See `dialog_examples.dart` for complete usage examples and demonstrations.
