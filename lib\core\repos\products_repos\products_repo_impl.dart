import 'package:dartz/dartz.dart';
import '../../entities/product_entity.dart';
import '../../errors/failure.dart';
import '../../models/product_model.dart';
import 'products_repo.dart';
import '../../services/data_service.dart';
import '../../utils/backend_end_points.dart';

class ProductsRepoImpl implements ProductsRepo {
  final DatabaseService databaseService;

  ProductsRepoImpl(this.databaseService);
  @override
  Future<Either<Failure, List<ProductEntity>>> getBestSellingProducts() async {
    try {
      final data = await databaseService.getData(
        path: BackendEndPoints.getProducts,
        query: {
          'orderBy': 'sellingCount',
          'limit': 10,
          'descending': true,
        },
      ) as List<Map<String, dynamic>>;

      final List<ProductEntity> products =
          data.map((e) => ProductModel.fromJson(e).toEntity()).toList();
      return Right(products);
    } catch (e) {
      return Left(
        ServerFailure(e.toString()),
      );
    }
  }

  @override
  Future<Either<Failure, List<ProductEntity>>> getProducts() async {
    try {
      final data = await databaseService.getData(
          path: BackendEndPoints.getProducts) as List<Map<String, dynamic>>;

      final List<ProductEntity> products =
          data.map((e) => ProductModel.fromJson(e).toEntity()).toList();
      return Right(products);
    } catch (e) {
      return Left(
        ServerFailure('Error fetching products'),
      );
    }
  }
  
  @override
  Future<Either<Failure, List<ProductEntity>>> getFeaturedProducts() async{
    try {
      final data = await databaseService.getData(
        path: BackendEndPoints.getProducts,
        query: {
          'orderBy': 'isFeatured',
          'limit': 10,
          'descending': true,
        },
      ) as List<Map<String, dynamic>>;

      final List<ProductEntity> products =
          data.map((e) => ProductModel.fromJson(e).toEntity()).toList();
      return Right(products);
    } catch (e) {
      return Left(
        ServerFailure('Error fetching products'),
      );
    }

  }
}
