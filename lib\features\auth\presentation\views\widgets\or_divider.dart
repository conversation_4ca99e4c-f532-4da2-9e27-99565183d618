import 'package:flutter/material.dart';

import '../../../../../core/utils/responsive_utils.dart';

Row buildOrDivider(BuildContext context) {
  return Row(
    mainAxisAlignment: MainAxisAlignment.center,
    children: [
      Expanded(
        child: Divider(
          color: Theme.of(context).dividerColor,
        ),
      ),
      Padding(
        padding: REdgeInsets.symmetric(horizontal: 16),
        child: Text(
          'أو',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
        ),
      ),
      Expanded(
        child: Divider(
          color: Theme.of(context).dividerColor,
        ),
      ),
    ],
  );
}
