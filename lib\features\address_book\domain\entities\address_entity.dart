class AddressEntity {
  final String id;
  final String userId;
  final String title;
  final String fullName;
  final String phoneNumber;
  final String street;
  final String city;
  final String state;
  final String postalCode;
  final String country;
  final bool isDefault;
  final DateTime createdAt;

  AddressEntity({
    required this.id,
    required this.userId,
    required this.title,
    required this.fullName,
    required this.phoneNumber,
    required this.street,
    required this.city,
    required this.state,
    required this.postalCode,
    required this.country,
    required this.isDefault,
    required this.createdAt,
  });

  String get fullAddress {
    return '$street, $city, $state $postalCode, $country';
  }
}
