import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import '../../../../../core/cubits/favorites_cubit/favorites_cubit.dart';
import '../../../../../core/entities/product_entity.dart';
import '../../../../../core/services/get_it_service.dart';
import '../../../../../core/utils/app_assets.dart';
import '../../../../../core/utils/responsive_utils.dart';
import '../../../../../core/widgets/custom_loading_indicator.dart';

class ProductImageSection extends StatefulWidget {
  const ProductImageSection({
    super.key,
    required this.product,
  });

  final ProductEntity product;

  @override
  State<ProductImageSection> createState() => _ProductImageSectionState();
}

class _ProductImageSectionState extends State<ProductImageSection> {
  int currentImageIndex = 0;
  late PageController pageController;

  @override
  void initState() {
    super.initState();
    pageController = PageController();
  }

  @override
  void dispose() {
    pageController.dispose();
    super.dispose();
  }

  List<String> get imageUrls {
    final List<String> urls = [];
    if (widget.product.imageUrl != null) {
      urls.add(widget.product.imageUrl!);
    }
    if (widget.product.imageUrls != null) {
      urls.addAll(widget.product.imageUrls!);
    }
    return urls.isEmpty ? [''] : urls;
  }

  @override
  Widget build(BuildContext context) {
    context.initResponsive();

    return Container(
      height: ResponsiveUtils.screenHeight * 0.4,
      width: double.infinity,
      color: Theme.of(context).colorScheme.surface,
      child: Stack(
        children: [
          // Main Image
          PageView.builder(
            controller: pageController,
            onPageChanged: (index) {
              setState(() {
                currentImageIndex = index;
              });
            },
            itemCount: imageUrls.length,
            itemBuilder: (context, index) {
              return Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                ),
                child: imageUrls[index].isNotEmpty
                    ? CachedNetworkImage(
                        imageUrl: imageUrls[index],
                        fit: BoxFit.cover,
                        placeholder: (context, url) => const Center(
                          child: CustomLoadingIndicator(),
                        ),
                        errorWidget: (context, url, error) => Container(
                          color: Theme.of(context)
                              .colorScheme
                              .surfaceContainerHighest,
                          child: Icon(
                            Icons.image_not_supported_outlined,
                            size: ResponsiveUtils.iconSize(64),
                            color:
                                Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                        ),
                      )
                    : Container(
                        color: Theme.of(context)
                            .colorScheme
                            .surfaceContainerHighest,
                        child: Icon(
                          Icons.image_not_supported_outlined,
                          size: ResponsiveUtils.iconSize(64),
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
              );
            },
          ),

          // Page Indicator
          if (imageUrls.length > 1)
            Positioned(
              bottom: ResponsiveUtils.spacing(16),
              left: 0,
              right: 0,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(
                  imageUrls.length,
                  (index) => Container(
                    margin: REdgeInsets.symmetric(horizontal: 4),
                    width: ResponsiveUtils.spacing(8),
                    height: ResponsiveUtils.spacing(8),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: currentImageIndex == index
                          ? Theme.of(context).colorScheme.primary
                          : Theme.of(context)
                              .colorScheme
                              .onSurface
                              .withValues(alpha: 0.3),
                    ),
                  ),
                ),
              ),
            ),

          // Favorite Button (Floating)
          Positioned(
            top: ResponsiveUtils.spacing(16),
            right: ResponsiveUtils.spacing(16),
            child: BlocBuilder<GlobalFavoritesCubit, GlobalFavoritesState>(
              bloc: getIt<GlobalFavoritesCubit>(),
              builder: (context, state) {
                final isFavorite = getIt<GlobalFavoritesCubit>()
                    .isFavorite(widget.product.code);
                return InkWell(
                  onTap: () {
                    getIt<GlobalFavoritesCubit>()
                        .toggleFavorite(widget.product);
                  },
                  child: isFavorite
                      ? SvgPicture.asset(
                          Assets.assetsImagesSoildHeartIcon,
                        )
                      : SvgPicture.asset(
                          Assets.assetsImagesOutlineHeartIcon,
                        ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
