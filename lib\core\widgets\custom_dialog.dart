import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../utils/responsive_utils.dart';

class CustomDialog extends StatelessWidget {
  const CustomDialog({
    super.key,
    required this.title,
    required this.content,
    this.icon,
    this.iconColor,
    this.primaryButtonText = 'موافق',
    this.secondaryButtonText = 'إلغاء',
    this.onPrimaryPressed,
    this.onSecondaryPressed,
    this.showSecondaryButton = true,
    this.primaryButtonColor,
    this.isDanger = false,
  });

  final String title;
  final String content;
  final IconData? icon;
  final Color? iconColor;
  final String primaryButtonText;
  final String secondaryButtonText;
  final VoidCallback? onPrimaryPressed;
  final VoidCallback? onSecondaryPressed;
  final bool showSecondaryButton;
  final Color? primaryButtonColor;
  final bool isDanger;

  @override
  Widget build(BuildContext context) {
    context.initResponsive();
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: RBorderRadius.circular(20),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: _buildDialogContent(context),
    );
  }

  Widget _buildDialogContent(BuildContext context) {
    return Container(
      padding: REdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).dialogTheme.backgroundColor,
        borderRadius: RBorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: ResponsiveUtils.elevation(10),
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Icon
          if (icon != null) ...[
            Container(
              width: ResponsiveUtils.spacing(80),
              height: ResponsiveUtils.spacing(80),
              decoration: BoxDecoration(
                color: _getIconBackgroundColor(context),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                size: ResponsiveUtils.iconSize(40),
                color: _getIconColor(context),
              ),
            ),
            const RSizedBox.height(20),
          ],

          // Title
          Text(
            title,
            style: Theme.of(context).textTheme.headlineSmall,
            textAlign: TextAlign.center,
          ),
          const RSizedBox.height(12),

          // Content
          Text(
            content,
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const RSizedBox.height(24),

          // Buttons
          Row(
            children: [
              if (showSecondaryButton) ...[
                Expanded(
                  child: _buildSecondaryButton(context),
                ),
                const RSizedBox.width(12),
              ],
              Expanded(
                child: _buildPrimaryButton(context),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPrimaryButton(BuildContext context) {
    return Container(
      height: ResponsiveUtils.spacing(48),
      decoration: BoxDecoration(
        color: _getPrimaryButtonColor(context),
        borderRadius: RBorderRadius.circular(12),
      ),
      child: Material(
        color: Theme.of(context).colorScheme.primary,
        child: InkWell(
          borderRadius: RBorderRadius.circular(12),
          onTap: onPrimaryPressed ?? () => Navigator.of(context).pop(),
          child: Center(
            child: Text(
              primaryButtonText,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.white,
                  ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSecondaryButton(BuildContext context) {
    return Container(
      height: ResponsiveUtils.spacing(48),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: RBorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).dividerColor,
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: RBorderRadius.circular(12),
          onTap: onSecondaryPressed ?? () => Navigator.of(context).pop(),
          child: Center(
            child: Text(
              secondaryButtonText,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ),
      ),
    );
  }

  Color _getIconBackgroundColor(BuildContext context) {
    if (iconColor != null) {
      return iconColor!.withValues(alpha: 0.1);
    }
    if (isDanger) {
      return Theme.of(context).colorScheme.error.withValues(alpha: 0.1);
    }
    return Theme.of(context).primaryColor.withValues(alpha: 0.1);
  }

  Color _getIconColor(BuildContext context) {
    if (iconColor != null) {
      return iconColor!;
    }
    if (isDanger) {
      return Theme.of(context).colorScheme.error;
    }
    return Theme.of(context).primaryColor;
  }

  Color _getPrimaryButtonColor(BuildContext context) {
    if (primaryButtonColor != null) {
      return primaryButtonColor!;
    }
    if (isDanger) {
      return Theme.of(context).colorScheme.error;
    }
    return Theme.of(context).primaryColor;
  }
}

// Helper function to show the custom dialog
Future<T?> showCustomDialog<T>({
  required BuildContext context,
  required String title,
  required String content,
  IconData? icon,
  Color? iconColor,
  String primaryButtonText = 'موافق',
  String secondaryButtonText = 'إلغاء',
  VoidCallback? onPrimaryPressed,
  VoidCallback? onSecondaryPressed,
  bool showSecondaryButton = true,
  Color? primaryButtonColor,
  bool isDanger = false,
  bool barrierDismissible = true,
}) {
  return showDialog<T>(
    context: context,
    barrierDismissible: barrierDismissible,
    builder: (BuildContext context) {
      return CustomDialog(
        title: title,
        content: content,
        icon: icon,
        iconColor: iconColor,
        primaryButtonText: primaryButtonText,
        secondaryButtonText: secondaryButtonText,
        onPrimaryPressed: onPrimaryPressed,
        onSecondaryPressed: onSecondaryPressed,
        showSecondaryButton: showSecondaryButton,
        primaryButtonColor: primaryButtonColor,
        isDanger: isDanger,
      );
    },
  );
}

// Predefined dialog types for common use cases
class DialogTypes {
  static Future<T?> showWarningDialog<T>({
    required BuildContext context,
    required String title,
    required String content,
    String primaryButtonText = 'موافق',
    String secondaryButtonText = 'إلغاء',
    VoidCallback? onConfirm,
    VoidCallback? onCancel,
  }) {
    return showCustomDialog<T>(
      context: context,
      title: title,
      content: content,
      icon: FontAwesomeIcons.triangleExclamation,
      iconColor: Colors.orange.shade600,
      primaryButtonText: primaryButtonText,
      secondaryButtonText: secondaryButtonText,
      onPrimaryPressed: onConfirm,
      onSecondaryPressed: onCancel,
    );
  }

  static Future<T?> showErrorDialog<T>({
    required BuildContext context,
    required String title,
    required String content,
    String primaryButtonText = 'موافق',
    VoidCallback? onConfirm,
  }) {
    return showCustomDialog<T>(
      context: context,
      title: title,
      content: content,
      icon: FontAwesomeIcons.circleXmark,
      isDanger: true,
      primaryButtonText: primaryButtonText,
      onPrimaryPressed: onConfirm,
      showSecondaryButton: false,
    );
  }

  static Future<T?> showSuccessDialog<T>({
    required BuildContext context,
    required String title,
    required String content,
    String primaryButtonText = 'موافق',
    VoidCallback? onConfirm,
  }) {
    return showCustomDialog<T>(
      context: context,
      title: title,
      content: content,
      icon: FontAwesomeIcons.circleCheck,
      iconColor: Colors.green.shade600,
      primaryButtonText: primaryButtonText,
      onPrimaryPressed: onConfirm,
      showSecondaryButton: false,
    );
  }

  static Future<T?> showConfirmationDialog<T>({
    required BuildContext context,
    required String title,
    required String content,
    String confirmButtonText = 'تأكيد',
    String cancelButtonText = 'إلغاء',
    VoidCallback? onConfirm,
    VoidCallback? onCancel,
    bool isDanger = false,
  }) {
    return showCustomDialog<T>(
      context: context,
      title: title,
      content: content,
      icon: FontAwesomeIcons.circleQuestion,
      primaryButtonText: confirmButtonText,
      secondaryButtonText: cancelButtonText,
      onPrimaryPressed: onConfirm,
      onSecondaryPressed: onCancel,
      isDanger: isDanger,
    );
  }
}
