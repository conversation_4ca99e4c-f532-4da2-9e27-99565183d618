part of 'orders_cubit.dart';

abstract class OrdersState extends Equatable {
  const OrdersState();

  @override
  List<Object> get props => [];
}

class OrdersInitial extends OrdersState {}

class OrdersLoading extends OrdersState {}

class OrdersLoaded extends OrdersState {
  final List<UserOrderEntity> orders;

  const OrdersLoaded({required this.orders});

  @override
  List<Object> get props => [orders];
}

class OrderDetailsLoaded extends OrdersState {
  final UserOrderEntity order;

  const OrderDetailsLoaded({required this.order});

  @override
  List<Object> get props => [order];
}

class OrdersError extends OrdersState {
  final String message;

  const OrdersError({required this.message});

  @override
  List<Object> get props => [message];
}

class OrderCancelled extends OrdersState {}

class OrderStatusUpdated extends OrdersState {}
