import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../manager/signUp_cubit/signup_cubit.dart';
import 'widgets/signup_view_body_bloc_consumer.dart';

import '../../../../core/services/get_it_service.dart';
import 'widgets/custom_app_bar.dart';
import '../../data/repos/auth_repo.dart';

class SignUpView extends StatelessWidget {
  const SignUpView({super.key});
  static const routeName = '/sign-up';
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => SignupCubit(
        getIt<AuthRepo>(),
      ),
      child: Scaffold(
        appBar: buildCustomAppBar(
          context,
          title: 'حساب جديد',
          onTap: () {
            Navigator.pop(context);
          },
        ),
        body: const SignUpViewBodyBlocConsumer(),
      ),
    );
  }
}
