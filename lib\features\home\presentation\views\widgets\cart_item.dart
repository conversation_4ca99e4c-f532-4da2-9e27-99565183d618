import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../../../../core/utils/responsive_utils.dart';
import '../../../../../core/widgets/custom_network_image.dart';
import '../../../domain/entities/cart_item_entity.dart';
import '../../manager/cart_cubit/cart_cubit.dart';
import 'cart_item_action_buttons.dart';

class CartItem extends StatefulWidget {
  const CartItem({super.key, required this.carItemEntity, required this.index});
  final CartItemEntity carItemEntity;
  final int index;

  @override
  State<CartItem> createState() => _CartItemState();
}

class _CartItemState extends State<CartItem> {
  late CartItemEntity currentItem;

  @override
  void initState() {
    super.initState();
    currentItem = widget.carItemEntity;
  }

  @override
  Widget build(BuildContext context) {
    log('🎨 [CartItem] Building item at index ${widget.index}: ${widget.carItemEntity.productEntity.name}');

    return BlocBuilder<CartCubit, CartState>(
      builder: (context, state) {
        log('🎨 [CartItem] BlocBuilder triggered for index ${widget.index}');

        // Get the current item directly from the cart using the index
        final cartItems = context.read<CartCubit>().cartEntity.cartItems;
        log('🎨 [CartItem] Total cart items: ${cartItems.length}');

        // Use the index to get the correct item
        if (widget.index >= 0 && widget.index < cartItems.length) {
          final itemToDisplay = cartItems[widget.index];
          log('🎨 [CartItem] Item to display at index ${widget.index}: ${itemToDisplay.productEntity.name} - Qty: ${itemToDisplay.quantity}');

          return _buildCartItemWidget(context, itemToDisplay);
        } else {
          log('🎨 [CartItem] ❌ Invalid index ${widget.index} for cart size ${cartItems.length}');
          return const SizedBox.shrink();
        }
      },
    );
  }

  Widget _buildCartItemWidget(
      BuildContext context, CartItemEntity itemToDisplay) {
    return IntrinsicHeight(
      child: Row(
        children: [
          Container(
            width: ResponsiveUtils.spacing(73),
            height: ResponsiveUtils.spacing(92),
            decoration:
                BoxDecoration(color: Theme.of(context).colorScheme.surface),
            child: CustomNetworkImage(
              imageUrl: itemToDisplay.productEntity.imageUrl!,
            ),
          ),
          const RSizedBox.width(17),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Flexible(
                      child: Text(
                        itemToDisplay.productEntity.name,
                        maxLines: 1,
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ),
                    const Spacer(),
                    GestureDetector(
                      onTap: () {
                        log('🗑️ [CartItem] Delete button tapped for index ${widget.index}');
                        log('🗑️ [CartItem] Item to delete: ${itemToDisplay.productEntity.name}');
                        context
                            .read<CartCubit>()
                            .deleteCartItemByIndex(widget.index);
                      },
                      child: Container(
                        padding: REdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.red.withValues(alpha: 0.1),
                          borderRadius: RBorderRadius.circular(8),
                        ),
                        child: SvgPicture.asset(
                          'assets/icons/trash.svg',
                          width: ResponsiveUtils.iconSize(16),
                          height: ResponsiveUtils.iconSize(16),
                          colorFilter: ColorFilter.mode(
                            Colors.red.shade600,
                            BlendMode.srcIn,
                          ),
                        ),
                      ),
                    )
                  ],
                ),
                Text(
                  '${itemToDisplay.quantity} قطعة',
                  textAlign: TextAlign.right,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                ),
                Row(
                  children: [
                    CartItemActionButtons(
                      cartItem: itemToDisplay,
                    ),
                    const Spacer(),
                    Text(
                      '${itemToDisplay.calculateTotalPrice().toString()} جنيه',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.primary),
                    )
                  ],
                )
              ],
            ),
          )
        ],
      ),
    );
  }
}
