import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../cubits/favorites_cubit/favorites_cubit.dart';
import '../entities/product_entity.dart';
import '../services/get_it_service.dart';
import '../utils/app_assets.dart';
import '../utils/responsive_utils.dart';
import 'custom_network_image.dart';
import '../../features/home/<USER>/manager/cart_cubit/cart_cubit.dart';
import '../../features/home/<USER>/views/product_details_view.dart';

class CustomProductItem extends StatelessWidget {
  const CustomProductItem({super.key, required this.product});

  final ProductEntity product;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => ProductDetailsView(product: product),
          ),
        );
      },
      child: Container(
        decoration: ShapeDecoration(
          color: Theme.of(context).cardColor,
          shape: RoundedRectangleBorder(
            borderRadius: RBorderRadius.circular(8),
          ),
        ),
        child: Stack(
          children: [
            Positioned.fill(
              child: Column(
                children: [
                  SizedBox(
                    height: ResponsiveUtils.spacing(8),
                  ),
                  Flexible(
                    fit: FlexFit.loose,
                    child: CustomNetworkImage(imageUrl: product.imageUrl!),
                  ),
                  SizedBox(
                    height: ResponsiveUtils.spacing(8),
                  ),
                  ListTile(
                    title: Text(
                      product.name,
                      textAlign: TextAlign.right,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            color: Theme.of(context).colorScheme.primary,
                          ),
                    ),
                    subtitle: Text.rich(
                      TextSpan(
                        children: [
                          TextSpan(
                            text: '${product.price.toString()} جنيه',
                            style: Theme.of(context).textTheme.bodyMedium,
                          ),
                          TextSpan(
                            text: '/',
                            style: Theme.of(context).textTheme.bodyMedium,
                          ),
                          TextSpan(
                            text: ' ',
                            style: Theme.of(context).textTheme.bodyMedium,
                          ),
                          TextSpan(
                            text: 'قطعة',
                            style: Theme.of(context).textTheme.bodyMedium,
                          ),
                        ],
                      ),
                      textAlign: TextAlign.right,
                    ),
                    trailing: GestureDetector(
                      onTap: () {
                        context.read<CartCubit>().addProduct(product);
                      },
                      child: CircleAvatar(
                        radius: ResponsiveUtils.iconSize(16),
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        child: const Icon(
                          Icons.add,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Positioned(
              top: 0,
              right: 0,
              child: BlocBuilder<GlobalFavoritesCubit, GlobalFavoritesState>(
                bloc: getIt<GlobalFavoritesCubit>(),
                builder: (context, state) {
                  final isFavorite =
                      getIt<GlobalFavoritesCubit>().isFavorite(product.code);
                  return InkWell(
                    onTap: () {
                      getIt<GlobalFavoritesCubit>().toggleFavorite(product);
                    },
                    child: isFavorite
                        ? SvgPicture.asset(
                            Assets.assetsImagesSoildHeartIcon,
                          )
                        : SvgPicture.asset(
                            Assets.assetsImagesOutlineHeartIcon,
                          ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
