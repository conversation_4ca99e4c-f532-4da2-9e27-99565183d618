import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../core/helper/get_user.dart';
import '../../../../../core/widgets/custom_button.dart';
import '../../../../core/services/get_it_service.dart';
import '../../domain/entities/address_entity.dart';
import '../../domain/repos/address_book_repo.dart';
import '../manager/address_book_cubit/address_book_cubit.dart';

class AddAddressView extends StatefulWidget {
  const AddAddressView({super.key});
  static const String routeName = '/add-address';

  @override
  State<AddAddressView> createState() => _AddAddressViewState();
}

class _AddAddressViewState extends State<AddAddressView> {
  final _formKey = GlobalKey<FormState>();

  final _titleController = TextEditingController();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _streetController = TextEditingController();
  final _cityController = TextEditingController();
  final _stateController = TextEditingController();
  final _postalController = TextEditingController();
  final _countryController = TextEditingController();

  @override
  void dispose() {
    _titleController.dispose();
    _nameController.dispose();
    _phoneController.dispose();
    _streetController.dispose();
    _cityController.dispose();
    _stateController.dispose();
    _postalController.dispose();
    _countryController.dispose();
    super.dispose();
  }

  void _saveAddress() {
    if (_formKey.currentState!.validate()) {
      final user = getUser();
      final newAddress = AddressEntity(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: user.uId,
        title: _titleController.text,
        fullName: _nameController.text,
        phoneNumber: _phoneController.text,
        street: _streetController.text,
        city: _cityController.text,
        state: _stateController.text,
        postalCode: _postalController.text,
        country: _countryController.text,
        isDefault: false,
        createdAt: DateTime.now(),
      );

      context.read<AddressBookCubit>().addAddress(address: newAddress);
      Navigator.pop(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<AddressBookCubit>(
      create: (context) => AddressBookCubit(getIt<AddressBookRepo>()),
      child: Scaffold(
        appBar: AppBar(title: const Text('إضافة عنوان جديد')),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Form(
            key: _formKey,
            child: Column(
              children: [
                _buildField(_titleController, 'عنوان (مثلاً: المنزل)'),
                _buildField(_nameController, 'الاسم الكامل'),
                _buildField(_phoneController, 'رقم الهاتف'),
                _buildField(_streetController, 'الشارع'),
                _buildField(_cityController, 'المدينة'),
                _buildField(_stateController, 'المنطقة / المحافظة'),
                _buildField(_postalController, 'الرمز البريدي'),
                _buildField(_countryController, 'البلد'),
                const SizedBox(height: 24),
                CustomButton(
                  text: 'حفظ العنوان',
                  onPressed: _saveAddress,
                  bgColor: Theme.of(context).colorScheme.primary,
                  textColor: Colors.white,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildField(TextEditingController controller, String hint) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: TextFormField(
        controller: controller,
        decoration: InputDecoration(
          hintText: hint,
          border: const OutlineInputBorder(),
        ),
        validator: (value) =>
            value == null || value.isEmpty ? 'هذا الحقل مطلوب' : null,
      ),
    );
  }
}
