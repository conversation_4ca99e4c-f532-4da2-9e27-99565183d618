import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../core/utils/responsive_utils.dart';
import '../../../../../core/widgets/custom_text_field.dart';
import '../../../../auth/presentation/views/widgets/custom_phone_field.dart';
import '../../../domain/entities/order_entity.dart';

import '../../../../../core/helper/get_user.dart';

class AddressInputSection extends StatelessWidget {
  const AddressInputSection(
      {super.key, required this.formKey, required this.valueListenable});

  final GlobalKey<FormState> formKey;
  final ValueListenable<AutovalidateMode> valueListenable;
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: ValueListenableBuilder<AutovalidateMode>(
        valueListenable: valueListenable,
        builder: (context, value, child) => Form(
          key: formKey,
          autovalidateMode: value,
          child: Column(
            children: [
              const RSizedBox.height(24),
              CustomTextFormField(
                onSaved: (value) {
                  context.read<OrderEntity>().shippingAddressEntity.name =
                      value;
                },
                label: 'الاسم كامل',
                initialValue: getUser().name,
                keyboardType: TextInputType.text,
                validator: (p0) {
                  if (p0 == null || p0.isEmpty) {
                    return 'برجاء ادخال الاسم كامل';
                  }
                  return null;
                },
              ),
              const RSizedBox.height(16),
              CustomTextFormField(
                onSaved: (value) {
                  context.read<OrderEntity>().shippingAddressEntity.email =
                      value;
                },
                label: 'البريد الإلكتروني',
                initialValue: getUser().email,
                keyboardType: TextInputType.text,
                validator: (p0) {
                  if (p0 == null || p0.isEmpty) {
                    return 'برجاء ادخال البريد الإلكتروني';
                  }
                  return null;
                },
              ),
              const RSizedBox.height(16),
              CustomTextFormField(
                onSaved: (value) {
                  context.read<OrderEntity>().shippingAddressEntity.address =
                      value;
                },
                label: 'العنوان',
                keyboardType: TextInputType.text,
                validator: (p0) {
                  if (p0 == null || p0.isEmpty) {
                    return 'برجاء ادخال العنوان';
                  }
                  return null;
                },
              ),
              const RSizedBox.height(16),
              CustomTextFormField(
                onSaved: (value) {
                  context.read<OrderEntity>().shippingAddressEntity.city =
                      value;
                },
                label: 'المدينه',
                keyboardType: TextInputType.text,
                validator: (p0) {
                  if (p0 == null || p0.isEmpty) {
                    return 'برجاء ادخال المدينه';
                  }
                  return null;
                },
              ),
              const RSizedBox.height(16),
              CustomTextFormField(
                onSaved: (value) {
                  context.read<OrderEntity>().shippingAddressEntity.floor =
                      value;
                },
                label: 'رقم الطابق , رقم الشقه ..',
                keyboardType: TextInputType.text,
                validator: (p0) {
                  if (p0 == null || p0.isEmpty) {
                    return 'برجاء ادخال رقم الطابق';
                  }
                  return null;
                },
              ),
              const RSizedBox.height(16),
              CustomPhoneField(
                onSaved: (value) {
                  context.read<OrderEntity>().shippingAddressEntity.phone =
                      value!.number;
                },
                initialValue: getUser().phone,
                validator: (p0) {
                  if (p0 == null || p0.number.isEmpty) {
                    return 'برجاء إدخال رقم الهاتف';
                  }

                  return null;
                },
              ),
              const RSizedBox.height(24),
            ],
          ),
        ),
      ),
    );
  }
}
