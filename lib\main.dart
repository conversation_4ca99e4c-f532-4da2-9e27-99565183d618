import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'core/helper/generate_route.dart';
import 'core/services/custom_bloc_observer.dart';
import 'core/services/get_it_service.dart';
import 'core/services/shared_preferences_singleton.dart';
import 'core/services/supabase_storage_service.dart';
import 'test_supabase.dart';
import 'core/cubits/theme_cubit/theme_cubit.dart';
import 'core/cubits/theme_cubit/theme_state.dart';

import 'features/splash/presentation/views/splash_view.dart';
import 'firebase_options.dart';

import 'generated/l10n.dart';

// Global navigator key for safe navigation
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  Bloc.observer = CustomBlocObserver();
  await SharedPreferencesSingleton.init();
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  await SupabaseStorageService.initialize();
  await SupabaseTest.testConnection();
  setupGetit();
  // runApp(
  //   DevicePreview(
  //     enabled: false, // set to false for release builds
  //     builder: (context) => const ShatabhaApp(), // Your app
  //   ),
  // );
  runApp(const ShatabhaApp());
}

class ShatabhaApp extends StatelessWidget {
  const ShatabhaApp({super.key});
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => ThemeCubit(),
      child: BlocBuilder<ThemeCubit, ThemeState>(
        builder: (context, state) {
          return MaterialApp(
            navigatorKey:
                navigatorKey, // Add global navigator key for safe navigation
           // builder: DevicePreview.appBuilder, // <- this is important
            localizationsDelegates: const [
              S.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: S.delegate.supportedLocales,
            locale: const Locale('ar'),
            debugShowCheckedModeBanner: false,
            onGenerateRoute: onGenerateRoute,
            initialRoute: SplashView.routeName,
            theme: state.themeData,
          );
        },
      ),
    );
  }
}
