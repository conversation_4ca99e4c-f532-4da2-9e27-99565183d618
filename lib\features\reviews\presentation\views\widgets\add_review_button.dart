import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../core/utils/responsive_utils.dart';
import '../../../../../core/helper/get_user.dart';
import '../../manager/reviews_cubit/reviews_cubit.dart';
import '../add_review_view.dart';

class AddReviewButton extends StatefulWidget {
  const AddReviewButton({
    super.key,
    required this.productId,
  });

  final String productId;

  @override
  State<AddReviewButton> createState() => _AddReviewButtonState();
}

class _AddReviewButtonState extends State<AddReviewButton> {
  bool hasReviewed = false;

  @override
  void initState() {
    super.initState();
    _checkUserReviewStatus();
  }

  void _checkUserReviewStatus() {
    final user = getUser();
    context.read<ReviewsCubit>().checkUserReviewStatus(
          userId: user.uId,
          productId: widget.productId,
        );
  }

  @override
  Widget build(BuildContext context) {
    context.initResponsive();

    return BlocListener<ReviewsCubit, ReviewsState>(
      listener: (context, state) {
        if (state is UserReviewStatusChecked) {
          setState(() {
            hasReviewed = state.hasReviewed;
          });
        }
      },
      child: Container(
        width: double.infinity,
        margin: REdgeInsets.symmetric(vertical: 8),
        child: hasReviewed
            ? Container(
                padding: REdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.green.withOpacity(0.1),
                  borderRadius:
                      BorderRadius.circular(ResponsiveUtils.radius(12)),
                  border: Border.all(
                    color: Colors.green.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.check_circle,
                      color: Colors.green[700],
                      size: ResponsiveUtils.iconSize(20),
                    ),
                    SizedBox(width: ResponsiveUtils.spacing(12)),
                    Expanded(
                      child: Text(
                        'لقد قمت بتقييم هذا المنتج بالفعل',
                        style: TextStyle(
                          fontSize: ResponsiveUtils.fontSize(14),
                          color: Colors.green[700],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              )
            : ElevatedButton.icon(
                onPressed: () => _navigateToAddReview(context),
                icon: Icon(
                  Icons.rate_review,
                  size: ResponsiveUtils.iconSize(20),
                ),
                label: Text(
                  'إضافة تقييم',
                  style: TextStyle(
                    fontSize: ResponsiveUtils.fontSize(16),
                    fontWeight: FontWeight.w600,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  padding: REdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius:
                        BorderRadius.circular(ResponsiveUtils.radius(12)),
                  ),
                  elevation: 2,
                ),
              ),
      ),
    );
  }

  void _navigateToAddReview(BuildContext context) {
    Navigator.pushNamed(
      context,
      AddReviewView.routeName,
      arguments: {
        'productId': widget.productId,
        'productName': null,
      },
    ).then((_) {
      // Refresh the review status when returning from add review screen
      _checkUserReviewStatus();
    });
  }
}
