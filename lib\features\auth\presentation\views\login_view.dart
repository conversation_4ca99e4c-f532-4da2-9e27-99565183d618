import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'widgets/login_view_body_bloc_consumer.dart';

import '../../../../core/services/get_it_service.dart';
import 'widgets/custom_app_bar.dart';
import '../../data/repos/auth_repo.dart';
import '../manager/login_cubit/login_cubit.dart';

class LoginView extends StatelessWidget {
  const LoginView({super.key});

  static const routeName = '/login';

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => LoginCubit(
        getIt<AuthRepo>(),
      ),
      child: Scaffold(
        appBar: buildCustomAppBar(
          context,
          title: 'تسجيل الدخول',
        ),
        body: const LogInViewBodyBlocConsumer(),
      ),
    );
  }
}
