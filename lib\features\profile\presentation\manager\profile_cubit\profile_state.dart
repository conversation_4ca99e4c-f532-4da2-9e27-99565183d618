part of 'profile_cubit.dart';

abstract class ProfileState extends Equatable {
  const ProfileState();

  @override
  List<Object> get props => [];
}

class ProfileInitial extends ProfileState {}

class ProfileLoading extends ProfileState {}

class ProfileLoaded extends ProfileState {
  final UserEntity userEntity;

  const ProfileLoaded({required this.userEntity});

  @override
  List<Object> get props => [userEntity];
}

class ProfileError extends ProfileState {
  final String message;

  const ProfileError({required this.message});

  @override
  List<Object> get props => [message];
}

class ProfileUpdateSuccess extends ProfileState {}

class ProfileAccountDeleted extends ProfileState {}

class ProfileLoggedOut extends ProfileState {}
