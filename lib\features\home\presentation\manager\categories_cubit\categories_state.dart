part of 'categories_cubit.dart';

abstract class CategoriesState {}

class CategoriesInitial extends CategoriesState {}

class CategoriesLoading extends CategoriesState {}

class CategoriesSuccess extends CategoriesState {
  final List<CategoryEntity> categories;

  CategoriesSuccess(this.categories);
}

class CategoriesFailure extends CategoriesState {
  final String errMessage;

  CategoriesFailure(this.errMessage);
}

class CategoryProductsLoading extends CategoriesState {}

class CategoryProductsSuccess extends CategoriesState {
  final List<ProductEntity> products;

  CategoryProductsSuccess(this.products);
}

class CategoryProductsFailure extends CategoriesState {
  final String errMessage;

  CategoryProductsFailure(this.errMessage);
}
