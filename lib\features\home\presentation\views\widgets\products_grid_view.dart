import 'package:flutter/material.dart';
import '../../../../../core/entities/product_entity.dart';
import '../../../../../core/utils/responsive_utils.dart';
import '../../../../../core/widgets/custom_product_item.dart';

class ProductsGridView extends StatelessWidget {
  const ProductsGridView({super.key, required this.products});
  final List<ProductEntity> products;
  @override
  Widget build(BuildContext context) {
    context.initResponsive();
    return SliverGrid.builder(
      itemCount:  products.length,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount:
            ResponsiveUtils.getGridCount(mobileCount: 2, tabletCount: 3),
        childAspectRatio: ResponsiveUtils.getAspectRatio(
            mobileRatio: 163 / 214, tabletRatio: 1.1),
        mainAxisSpacing: ResponsiveUtils.spacing(8),
        crossAxisSpacing: ResponsiveUtils.spacing(16),
      ),
      itemBuilder: (context, index) {
        return CustomProductItem(
          product: products[index],
        );
      },
    );
  }
}
