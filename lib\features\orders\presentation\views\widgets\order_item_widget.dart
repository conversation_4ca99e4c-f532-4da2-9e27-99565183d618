import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../../../../core/utils/order_id_formatter.dart';
import '../../../../../core/utils/responsive_utils.dart';
import '../../../domain/entities/order_status_entity.dart';
import '../../../domain/entities/user_order_entity.dart';
import 'package:intl/intl.dart';

import '../order_details_view.dart';

class OrderItemWidget extends StatelessWidget {
  final UserOrderEntity order;
  final VoidCallback? onCancelOrder;

  const OrderItemWidget({
    super.key,
    required this.order,
    this.onCancelOrder,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: ResponsiveUtils.elevation(2),
      shape: RoundedRectangleBorder(
        borderRadius: RBorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () {
          Navigator.pushNamed(
            context,
            OrderDetailsView.routeName,
            arguments: order.id,
          );
        },
        borderRadius: RBorderRadius.circular(12),
        child: Padding(
          padding: REdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Order Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    OrderIdFormatter.getDisplayText(order.id),
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  _buildStatusChip(order.status, context),
                ],
              ),
              const RSizedBox.height(12),

              // Order Date and Total
              Row(
                children: [
                  Icon(
                    FontAwesomeIcons.calendar,
                    size: ResponsiveUtils.iconSize(14),
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                  const RSizedBox.width(8),
                  Text(
                    DateFormat('dd/MM/yyyy - hh:mm a', 'ar')
                        .format(order.orderDate),
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  const Spacer(),
                  Text(
                    '${order.totalAmount.toStringAsFixed(2)} جنيه',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: Theme.of(context).colorScheme.primary,
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                ],
              ),
              const RSizedBox.height(12),

              // Items Preview
              _buildItemsPreview(context),
              const RSizedBox.height(12),

              // Payment Method
              Row(
                children: [
                  Icon(
                    FontAwesomeIcons.creditCard,
                    size: ResponsiveUtils.iconSize(14),
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                  const RSizedBox.width(8),
                  Text(
                    order.paymentMethod,
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
              const RSizedBox.height(16),

              // Action Buttons
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () {
                        Navigator.pushNamed(
                          context,
                          OrderDetailsView.routeName,
                          arguments: order.id,
                        );
                      },
                      child: const Text('التفاصيل'),
                    ),
                  ),
                  if (_canCancelOrder(order.status)) ...[
                    const RSizedBox.width(12),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: onCancelOrder != null
                            ? () {
                                _showCancelOrderDialog(context);
                              }
                            : null,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('إلغاء'),
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(OrderStatus status, BuildContext context) {
    Color chipColor;
    String statusText;

    switch (status) {
      case OrderStatus.pending:
        chipColor = Colors.orange;
        statusText = 'في الانتظار';
        break;
      case OrderStatus.confirmed:
        chipColor = Colors.blue;
        statusText = 'مؤكد';
        break;
      case OrderStatus.processing:
        chipColor = Colors.purple;
        statusText = 'قيد التحضير';
        break;
      case OrderStatus.shipped:
        chipColor = Colors.indigo;
        statusText = 'تم الشحن';
        break;
      case OrderStatus.delivered:
        chipColor = Colors.green;
        statusText = 'تم التسليم';
        break;
      case OrderStatus.cancelled:
        chipColor = Colors.red;
        statusText = 'ملغي';
        break;
    }

    return Container(
      padding: REdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: chipColor.withValues(alpha: 0.1),
        borderRadius: RBorderRadius.circular(20),
        border: Border.all(color: chipColor.withValues(alpha: 0.3)),
      ),
      child: Text(
        statusText,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: chipColor,
              fontWeight: FontWeight.bold,
            ),
      ),
    );
  }

  Widget _buildItemsPreview(BuildContext context) {
    return Container(
      padding: REdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context)
            .colorScheme
            .surfaceContainerHighest
            .withValues(alpha: 0.3),
        borderRadius: RBorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            FontAwesomeIcons.box,
            size: ResponsiveUtils.iconSize(16),
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const RSizedBox.width(8),
          Text(
            '${order.items.length} منتج',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
          ),
          const Spacer(),
          if (order.items.isNotEmpty)
            SizedBox(
              height: ResponsiveUtils.spacing(40),
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                shrinkWrap: true,
                itemCount: order.items.length > 3 ? 3 : order.items.length,
                itemBuilder: (context, index) {
                  final item = order.items[index];
                  return Container(
                    width: ResponsiveUtils.spacing(40),
                    height: ResponsiveUtils.spacing(40),
                    margin: REdgeInsets.only(left: 4),
                    decoration: BoxDecoration(
                      borderRadius: RBorderRadius.circular(6),
                      border: Border.all(
                        color: Theme.of(context).dividerColor,
                        width: 1,
                      ),
                    ),
                    child: ClipRRect(
                      borderRadius: RBorderRadius.circular(6),
                      child: Image.network(
                        item.productImage,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            color: Theme.of(context)
                                .colorScheme
                                .surfaceContainerHighest,
                            child: Icon(
                              FontAwesomeIcons.image,
                              size: 16,
                              color: Theme.of(context)
                                  .colorScheme
                                  .onSurfaceVariant,
                            ),
                          );
                        },
                      ),
                    ),
                  );
                },
              ),
            ),
          if (order.items.length > 3)
            Container(
              width: ResponsiveUtils.spacing(40),
              height: ResponsiveUtils.spacing(40),
              margin: REdgeInsets.only(left: 4),
              decoration: BoxDecoration(
                color: Theme.of(context)
                    .colorScheme
                    .primary
                    .withValues(alpha: 0.1),
                borderRadius: RBorderRadius.circular(6),
                border: Border.all(
                  color: Theme.of(context)
                      .colorScheme
                      .primary
                      .withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Center(
                child: Text(
                  '+${order.items.length - 3}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  bool _canCancelOrder(OrderStatus status) {
    // Only allow cancellation for pending and confirmed orders
    return status == OrderStatus.pending || status == OrderStatus.confirmed;
  }

  void _showCancelOrderDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('إلغاء الطلب'),
          content: const Text('هل أنت متأكد من رغبتك في إلغاء هذا الطلب؟'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('لا'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                if (onCancelOrder != null) {
                  onCancelOrder!();
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('نعم، إلغاء'),
            ),
          ],
        );
      },
    );
  }
}
