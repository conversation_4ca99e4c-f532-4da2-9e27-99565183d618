part of 'favorites_cubit.dart';

abstract class GlobalFavoritesState extends Equatable {
  const GlobalFavoritesState();

  @override
  List<Object> get props => [];
}

class GlobalFavoritesInitial extends GlobalFavoritesState {}

class GlobalFavoritesLoading extends GlobalFavoritesState {}

class GlobalFavoritesLoaded extends GlobalFavoritesState {
  final Set<String> favoriteProductIds;

  const GlobalFavoritesLoaded({required this.favoriteProductIds});

  @override
  List<Object> get props => [favoriteProductIds];
}

class GlobalFavoritesError extends GlobalFavoritesState {
  final String message;

  const GlobalFavoritesError({required this.message});

  @override
  List<Object> get props => [message];
}
