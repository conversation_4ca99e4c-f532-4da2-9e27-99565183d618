import 'package:flutter/material.dart';
import '../../../../../core/entities/product_entity.dart';
import '../../../../../core/utils/responsive_utils.dart';

class ProductPriceSection extends StatelessWidget {
  const ProductPriceSection({
    super.key,
    required this.product,
  });

  final ProductEntity product;

  bool get hasDiscount => product.discount != null && product.discount! > 0;

  String get discountPercentage {
    if (!hasDiscount) return '';
    final discount =
        ((product.originalPrice - product.price) / product.originalPrice * 100);
    return '${discount.round()}%';
  }

  @override
  Widget build(BuildContext context) {
    context.initResponsive();

    return Container(
      padding: REdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainer,
        borderRadius: RBorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Price Row
          Row(
            children: [
              // Current Price
              Text(
                '${product.price.toInt()} جنيه',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.primary,
                    ),
              ),

              const RSizedBox.width(12),

              // Original Price (if discounted)
              if (hasDiscount) ...[
                Text(
                  '${product.originalPrice.toInt()} جنيه',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        decoration: TextDecoration.lineThrough,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                ),

                const RSizedBox.width(8),

                // Discount Badge
                Container(
                  padding: REdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.red,
                    borderRadius:
                        RBorderRadius.circular(6),
                  ),
                  child: Text(
                    'خصم $discountPercentage',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                ),
              ],
            ],
          ),

          // Savings Amount (if discounted)
          if (hasDiscount) ...[
            const RSizedBox.height(8),
            Row(
              children: [
                Icon(
                  Icons.savings_outlined,
                  size: ResponsiveUtils.iconSize(16),
                  color: Colors.green,
                ),
                const RSizedBox.width(6),
                Text(
                  'توفر ${(product.originalPrice - product.price).toInt()} جنيه',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.green,
                        fontWeight: FontWeight.w500,
                      ),
                ),
              ],
            ),
          ],

          const RSizedBox.height(12),

          // Payment Options
          Row(
            children: [
              Icon(
                Icons.credit_card_outlined,
                size: ResponsiveUtils.iconSize(16),
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              const RSizedBox.width(6),
              Text(
                'دفع عند الاستلام متاح',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
