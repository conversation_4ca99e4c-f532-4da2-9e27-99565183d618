{"buildFiles": ["C:\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\0-Mustafa\\1-Flutter\\MyApps\\hoot_e_commerce\\android\\app\\.cxx\\Debug\\3q5z2b65\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\0-Mustafa\\1-Flutter\\MyApps\\hoot_e_commerce\\android\\app\\.cxx\\Debug\\3q5z2b65\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}