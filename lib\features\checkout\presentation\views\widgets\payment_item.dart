import 'package:flutter/material.dart';
import '../../../../../core/utils/app_decorations.dart';
import '../../../../../core/utils/responsive_utils.dart';

class PaymentItem extends StatelessWidget {
  const PaymentItem({super.key, required this.tile, required this.child});

  final String tile;
  final Widget child;
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '$tile:',
          style: Theme.of(context).textTheme.bodyMedium,
        ),
        const RSizedBox.height(8),
        Container(
          padding: REdgeInsets.symmetric(vertical: 16, horizontal: 8),
          decoration: AppDecorations.greyBoxDecoration(context),
          child: child,
        ),
      ],
    );
  }
}
