import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:skeletonizer/skeletonizer.dart';
import '../../../../../core/helper/get_dummy_data.dart';
import '../../../../../core/utils/responsive_utils.dart';
import '../../../../../core/widgets/custom_error_widget.dart';
import '../../manager/companies_cubit/companies_cubit.dart';
import '../companies_view.dart';
import 'companies_list_view_widget.dart';
import 'section_header.dart';

class CompaniesSection extends StatelessWidget {
  const CompaniesSection({super.key});

  @override
  Widget build(BuildContext context) {
    context.initResponsive();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SectionHeader(
          title: 'أفضل العلامات التجارية',
          showSeeAll: true,
          onSeeAllTap: () {
            Navigator.pushNamed(context, CompaniesView.routeName);
          },
        ),
        const RSizedBox.height(12),
        BlocBuilder<CompaniesCubit, CompaniesState>(
          builder: (context, state) {
            if (state is CompaniesLoading) {
              return Skeletonizer(
                child: CompaniesListViewWidget(
                  companies: dummyCompanies,
                ),
              );
            } else if (state is CompaniesFailure) {
              return CustomErrorWidget(
                errMessage: state.errMessage,
                onRetry: () {
                  context.read<CompaniesCubit>().getCompanies();
                },
              );
            } else if (state is CompaniesSuccess) {
              return CompaniesListViewWidget(
                companies: state.companies,
              );
            }
            return const SizedBox.shrink();
          },
        ),
      ],
    );
  }
}
