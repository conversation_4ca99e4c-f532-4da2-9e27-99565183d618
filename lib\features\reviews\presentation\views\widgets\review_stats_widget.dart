import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../core/utils/responsive_utils.dart';
import '../../manager/reviews_cubit/reviews_cubit.dart';
import 'rating_stars_widget.dart';

class ReviewStatsWidget extends StatefulWidget {
  const ReviewStatsWidget({
    super.key,
    required this.productId,
  });

  final String productId;

  @override
  State<ReviewStatsWidget> createState() => _ReviewStatsWidgetState();
}

class _ReviewStatsWidgetState extends State<ReviewStatsWidget> {
  @override
  void initState() {
    super.initState();
    context
        .read<ReviewsCubit>()
        .getProductReviewStats(productId: widget.productId);
  }

  @override
  Widget build(BuildContext context) {
    context.initResponsive();

    return BlocBuilder<ReviewsCubit, ReviewsState>(
      builder: (context, state) {
        if (state is ReviewStatsLoaded) {
          final stats = state.stats;
          final averageRating = stats['averageRating'] as double;
          final totalReviews = stats['totalReviews'] as int;
          final ratingDistribution =
              stats['ratingDistribution'] as Map<String, int>;

          return Container(
            padding: REdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Theme.of(context).cardColor,
              borderRadius: BorderRadius.circular(ResponsiveUtils.radius(20)),
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).shadowColor.withValues(alpha: 0.1),
                  blurRadius: ResponsiveUtils.radius(16),
                  offset: Offset(0, ResponsiveUtils.height(4)),
                ),
                BoxShadow(
                  color: Theme.of(context).shadowColor.withValues(alpha: 0.05),
                  blurRadius: ResponsiveUtils.radius(8),
                  offset: Offset(0, ResponsiveUtils.height(2)),
                ),
              ],
              border: Border.all(
                color: Theme.of(context).dividerColor.withValues(alpha: 0.1),
                width: 1,
              ),
            ),
            child: Column(
              children: [
                // Header with icon
                Row(
                  children: [
                    Container(
                      padding: REdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Theme.of(context)
                            .colorScheme
                            .primary
                            .withValues(alpha: 0.1),
                        borderRadius:
                            BorderRadius.circular(ResponsiveUtils.radius(10)),
                      ),
                      child: Icon(
                        Icons.analytics_rounded,
                        size: ResponsiveUtils.iconSize(20),
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                    SizedBox(width: ResponsiveUtils.spacing(12)),
                    Text(
                      'إحصائيات التقييمات',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.w700,
                            fontSize: ResponsiveUtils.fontSize(18),
                            color: Theme.of(context).colorScheme.onSurface,
                          ),
                    ),
                  ],
                ),

                SizedBox(height: ResponsiveUtils.spacing(20)),

                // Overall Rating Section
                Row(
                  children: [
                    // Average Rating Display
                    Expanded(
                      flex: 2,
                      child: Container(
                        padding: REdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Theme.of(context)
                              .colorScheme
                              .surface
                              .withValues(alpha: 0.5),
                          borderRadius:
                              BorderRadius.circular(ResponsiveUtils.radius(16)),
                          border: Border.all(
                            color: Theme.of(context)
                                .dividerColor
                                .withValues(alpha: 0.2),
                            width: 1,
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Container(
                              padding: REdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: _getAverageRatingColor(averageRating)
                                    .withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(
                                    ResponsiveUtils.radius(12)),
                              ),
                              child: Text(
                                averageRating.toStringAsFixed(1),
                                style: Theme.of(context)
                                    .textTheme
                                    .headlineLarge
                                    ?.copyWith(
                                      fontSize: ResponsiveUtils.fontSize(40),
                                      fontWeight: FontWeight.w900,
                                      color:
                                          _getAverageRatingColor(averageRating),
                                    ),
                              ),
                            ),
                            SizedBox(height: ResponsiveUtils.spacing(8)),
                            RatingStarsWidget(
                              rating: averageRating,
                              size: ResponsiveUtils.iconSize(20),
                              showRatingText: false,
                            ),
                            SizedBox(height: ResponsiveUtils.spacing(8)),
                            Container(
                              padding: REdgeInsets.symmetric(
                                  horizontal: 12, vertical: 6),
                              decoration: BoxDecoration(
                                color: Theme.of(context)
                                    .colorScheme
                                    .secondary
                                    .withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(
                                    ResponsiveUtils.radius(8)),
                              ),
                              child: Text(
                                '$totalReviews تقييم',
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyMedium
                                    ?.copyWith(
                                      fontSize: ResponsiveUtils.fontSize(14),
                                      fontWeight: FontWeight.w600,
                                      color: Theme.of(context)
                                          .colorScheme
                                          .onSurface,
                                    ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    SizedBox(width: ResponsiveUtils.spacing(20)),

                    // Rating Distribution
                    Expanded(
                      flex: 3,
                      child: Container(
                        padding: REdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Theme.of(context)
                              .colorScheme
                              .surface
                              .withValues(alpha: 0.3),
                          borderRadius:
                              BorderRadius.circular(ResponsiveUtils.radius(16)),
                          border: Border.all(
                            color: Theme.of(context)
                                .dividerColor
                                .withValues(alpha: 0.2),
                            width: 1,
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'توزيع التقييمات',
                              style: Theme.of(context)
                                  .textTheme
                                  .titleMedium
                                  ?.copyWith(
                                    fontWeight: FontWeight.w600,
                                    fontSize: ResponsiveUtils.fontSize(14),
                                    color:
                                        Theme.of(context).colorScheme.onSurface,
                                  ),
                            ),
                            SizedBox(height: ResponsiveUtils.spacing(12)),
                            for (int i = 5; i >= 1; i--)
                              _buildRatingBar(
                                context,
                                rating: i,
                                count: ratingDistribution[i.toString()] ?? 0,
                                total: totalReviews,
                              ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          );
        }

        // Default empty state
        return Container(
          padding: REdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(ResponsiveUtils.radius(16)),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '0.0',
                          style: TextStyle(
                            fontSize: ResponsiveUtils.fontSize(36),
                            fontWeight: FontWeight.bold,
                            color: Colors.grey[400],
                          ),
                        ),
                        SizedBox(height: ResponsiveUtils.spacing(4)),
                        RatingStarsWidget(
                          rating: 0,
                          size: ResponsiveUtils.iconSize(20),
                        ),
                        SizedBox(height: ResponsiveUtils.spacing(4)),
                        Text(
                          'لا توجد تقييمات',
                          style: TextStyle(
                            fontSize: ResponsiveUtils.fontSize(14),
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(width: ResponsiveUtils.spacing(20)),
                  Expanded(
                    flex: 3,
                    child: Column(
                      children: [
                        for (int i = 5; i >= 1; i--)
                          _buildRatingBar(
                            context,
                            rating: i,
                            count: 0,
                            total: 0,
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Color _getAverageRatingColor(double rating) {
    if (rating >= 4.5) {
      return Colors.green;
    } else if (rating >= 3.5) {
      return Colors.amber;
    } else if (rating >= 2.5) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }

  Widget _buildRatingBar(
    BuildContext context, {
    required int rating,
    required int count,
    required int total,
  }) {
    final percentage = total > 0 ? count / total : 0.0;

    return Padding(
      padding: REdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Text(
            '$rating',
            style: TextStyle(
              fontSize: ResponsiveUtils.fontSize(12),
              color: Colors.grey[600],
            ),
          ),
          SizedBox(width: ResponsiveUtils.spacing(4)),
          Icon(
            Icons.star,
            size: ResponsiveUtils.iconSize(12),
            color: Colors.amber,
          ),
          SizedBox(width: ResponsiveUtils.spacing(8)),
          Expanded(
            child: Container(
              height: ResponsiveUtils.height(6),
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(ResponsiveUtils.radius(3)),
              ),
              child: FractionallySizedBox(
                alignment: Alignment.centerLeft,
                widthFactor: percentage,
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.amber,
                    borderRadius:
                        BorderRadius.circular(ResponsiveUtils.radius(3)),
                  ),
                ),
              ),
            ),
          ),
          SizedBox(width: ResponsiveUtils.spacing(8)),
          Text(
            '$count',
            style: TextStyle(
              fontSize: ResponsiveUtils.fontSize(12),
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }
}
