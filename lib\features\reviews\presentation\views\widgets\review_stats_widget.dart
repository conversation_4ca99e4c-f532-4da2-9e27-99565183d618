import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../core/utils/responsive_utils.dart';
import '../../manager/reviews_cubit/reviews_cubit.dart';
import 'rating_stars_widget.dart';

class ReviewStatsWidget extends StatefulWidget {
  const ReviewStatsWidget({
    super.key,
    required this.productId,
  });

  final String productId;

  @override
  State<ReviewStatsWidget> createState() => _ReviewStatsWidgetState();
}

class _ReviewStatsWidgetState extends State<ReviewStatsWidget> {
  @override
  void initState() {
    super.initState();
    context.read<ReviewsCubit>().getProductReviewStats(productId: widget.productId);
  }

  @override
  Widget build(BuildContext context) {
    context.initResponsive();

    return BlocBuilder<ReviewsCubit, ReviewsState>(
      builder: (context, state) {
        if (state is ReviewStatsLoaded) {
          final stats = state.stats;
          final averageRating = stats['averageRating'] as double;
          final totalReviews = stats['totalReviews'] as int;
          final ratingDistribution = stats['ratingDistribution'] as Map<String, int>;

          return Container(
            padding: REdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Theme.of(context).cardColor,
              borderRadius: BorderRadius.circular(ResponsiveUtils.radius(16)),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                // Overall Rating Section
                Row(
                  children: [
                    // Average Rating Display
                    Expanded(
                      flex: 2,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            averageRating.toStringAsFixed(1),
                            style: TextStyle(
                              fontSize: ResponsiveUtils.fontSize(36),
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).primaryColor,
                            ),
                          ),
                          SizedBox(height: ResponsiveUtils.spacing(4)),
                          RatingStarsWidget(
                            rating: averageRating,
                            size: ResponsiveUtils.iconSize(20),
                          ),
                          SizedBox(height: ResponsiveUtils.spacing(4)),
                          Text(
                            '$totalReviews تقييم',
                            style: TextStyle(
                              fontSize: ResponsiveUtils.fontSize(14),
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    SizedBox(width: ResponsiveUtils.spacing(20)),
                    
                    // Rating Distribution
                    Expanded(
                      flex: 3,
                      child: Column(
                        children: [
                          for (int i = 5; i >= 1; i--)
                            _buildRatingBar(
                              context,
                              rating: i,
                              count: ratingDistribution[i.toString()] ?? 0,
                              total: totalReviews,
                            ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          );
        }

        // Default empty state
        return Container(
          padding: REdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(ResponsiveUtils.radius(16)),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '0.0',
                          style: TextStyle(
                            fontSize: ResponsiveUtils.fontSize(36),
                            fontWeight: FontWeight.bold,
                            color: Colors.grey[400],
                          ),
                        ),
                        SizedBox(height: ResponsiveUtils.spacing(4)),
                        RatingStarsWidget(
                          rating: 0,
                          size: ResponsiveUtils.iconSize(20),
                        ),
                        SizedBox(height: ResponsiveUtils.spacing(4)),
                        Text(
                          'لا توجد تقييمات',
                          style: TextStyle(
                            fontSize: ResponsiveUtils.fontSize(14),
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  SizedBox(width: ResponsiveUtils.spacing(20)),
                  
                  Expanded(
                    flex: 3,
                    child: Column(
                      children: [
                        for (int i = 5; i >= 1; i--)
                          _buildRatingBar(
                            context,
                            rating: i,
                            count: 0,
                            total: 0,
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildRatingBar(
    BuildContext context, {
    required int rating,
    required int count,
    required int total,
  }) {
    final percentage = total > 0 ? count / total : 0.0;

    return Padding(
      padding: REdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Text(
            '$rating',
            style: TextStyle(
              fontSize: ResponsiveUtils.fontSize(12),
              color: Colors.grey[600],
            ),
          ),
          SizedBox(width: ResponsiveUtils.spacing(4)),
          Icon(
            Icons.star,
            size: ResponsiveUtils.iconSize(12),
            color: Colors.amber,
          ),
          SizedBox(width: ResponsiveUtils.spacing(8)),
          Expanded(
            child: Container(
              height: ResponsiveUtils.height(6),
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(ResponsiveUtils.radius(3)),
              ),
              child: FractionallySizedBox(
                alignment: Alignment.centerLeft,
                widthFactor: percentage,
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.amber,
                    borderRadius: BorderRadius.circular(ResponsiveUtils.radius(3)),
                  ),
                ),
              ),
            ),
          ),
          SizedBox(width: ResponsiveUtils.spacing(8)),
          Text(
            '$count',
            style: TextStyle(
              fontSize: ResponsiveUtils.fontSize(12),
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }
}
