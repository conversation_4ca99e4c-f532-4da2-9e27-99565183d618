import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/helper/build_app_bar.dart';
import '../../../../core/services/get_it_service.dart';
import '../../domain/repos/profile_repo.dart';
import '../manager/profile_cubit/profile_cubit.dart';
import 'widgets/profile_view_body.dart';

class ProfileView extends StatelessWidget {
  const ProfileView({super.key});
  static const String routeName = '/profile';
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => ProfileCubit(getIt<ProfileRepo>()),
      child: Scaffold(
        appBar: buildAppBarWithAlarmWidget(
          context,
          title: 'حسابي',
          isBack: false,
          isNotification: false,
        ),
        body: const ProfileViewBody(),
      ),
    );
  }
}
