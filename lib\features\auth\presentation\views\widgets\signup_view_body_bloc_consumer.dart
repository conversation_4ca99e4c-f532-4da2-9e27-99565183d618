import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../core/helper/build_custom_snack_bar.dart';
import '../../../../../core/widgets/custom_progress_hud.dart';
import 'sign_up_view_body.dart';
import '../../manager/signUp_cubit/signup_cubit.dart';

class SignUpViewBodyBlocConsumer extends StatelessWidget {
  const SignUpViewBodyBlocConsumer({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<SignupCubit, SignupState>(
      listener: (context, state) {
        if (state is SignupSuccess) {
          showSuccessSnackBar(context,
              message: 'يرجى تسجيل الدخول\nتم التسجيل بنجاح !');
          Navigator.pop(context);
        }

        if (state is SignupError) {
          showErrorSnackBar(context, message: state.message);
        }
      },
      builder: (context, state) {
        return CustomProgressHud(
          isLoading: state is SignupLoading ? true : false,
          child: const SignUpViewBody(),
        );
      },
    );
  }
}
