import 'package:flutter_bloc/flutter_bloc.dart';
import '../../services/shared_preferences_singleton.dart';
import '../../global/themes/theme_data_light.dart';
import '../../global/themes/theme_data_dark.dart';
import 'theme_state.dart';

class ThemeCubit extends Cubit<ThemeState> {
  static const String _themeKey = 'isDarkMode';

  ThemeCubit()
      : super(ThemeInitial(
          themeData: lightTheme,
          isDarkMode: false,
        )) {
    _loadTheme();
  }

  void _loadTheme() {
    final isDarkMode = SharedPreferencesSingleton.getBool(_themeKey);
    final themeData = isDarkMode ? darkTheme : lightTheme;

    emit(ThemeChanged(
      themeData: themeData,
      isDarkMode: isDarkMode,
    ));
  }

  Future<void> toggleTheme() async {
    final newIsDarkMode = !state.isDarkMode;
    final newThemeData = newIsDarkMode ? darkTheme : lightTheme;

    // Save to SharedPreferences
    await SharedPreferencesSingleton.setBool(_themeKey, newIsDarkMode);

    // Emit new state
    emit(ThemeChanged(
      themeData: newThemeData,
      isDarkMode: newIsDarkMode,
    ));
  }

  Future<void> setTheme(bool isDarkMode) async {
    if (state.isDarkMode == isDarkMode) return;

    final themeData = isDarkMode ? darkTheme : lightTheme;

    // Save to SharedPreferences
    await SharedPreferencesSingleton.setBool(_themeKey, isDarkMode);

    // Emit new state
    emit(ThemeChanged(
      themeData: themeData,
      isDarkMode: isDarkMode,
    ));
  }
}
