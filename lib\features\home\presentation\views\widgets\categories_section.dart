import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:skeletonizer/skeletonizer.dart';
import '../../../../../core/helper/get_dummy_data.dart';
import '../../../../../core/utils/responsive_utils.dart';
import '../../../../../core/widgets/custom_error_widget.dart';
import '../../manager/categories_cubit/categories_cubit.dart';
import '../categories_view.dart';
import 'categories_list_view_widget.dart';
import 'section_header.dart';

class CategoriesSection extends StatelessWidget {
  const CategoriesSection({super.key});

  @override
  Widget build(BuildContext context) {
    context.initResponsive();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
         SectionHeader(
          title: 'الفئات',
          showSeeAll: true,
            onSeeAllTap: () {
              Navigator.pushNamed(context, CategoriesView.routeName);
            },
        ),
        const RSizedBox.height(12),
        BlocBuilder<CategoriesCubit, CategoriesState>(
          builder: (context, state) {
            if (state is CategoriesLoading) {
              return Skeletonizer(
                child: CategoriesListViewWidget(
                  categories: dummyCategories,
                ),
              );
            } else if (state is CategoriesFailure) {
              return CustomErrorWidget(
                errMessage: state.errMessage,
                onRetry: () {
                  context.read<CategoriesCubit>().getCategories();
                },
              );
            } else if (state is CategoriesSuccess) {
              return CategoriesListViewWidget(
                categories: state.categories,
              );
            }
            return const SizedBox.shrink();
          },
        ),
      ],
    );
  }
}
