import '../../../../core/utils/app_assets.dart';

class BottomNavBarEntity {
  final String activeImage, inActiveImage;
  final String name;

  BottomNavBarEntity({
    required this.activeImage,
    required this.inActiveImage,
    required this.name,
  });
}

List<BottomNavBarEntity> get bottomNavBarItems => [
      BottomNavBarEntity(
        activeImage: Assets.assetsImagesBoldHome,
        inActiveImage: Assets.assetsImagesOutlineHome,
        name: 'الرئيسية',
      ),
      BottomNavBarEntity(
        activeImage: Assets.assetsImagesBoldProducts,
        inActiveImage: Assets.assetsImagesOutlineProducts,
        name: 'المنتجات',
      ),
      BottomNavBarEntity(
        activeImage: Assets.assetsImagesBoldShoppingCart,
        inActiveImage: Assets.assetsImagesOutlineShoppingCart,
        name: 'سلة التسوق',
      ),
      BottomNavBarEntity(
        activeImage: Assets.assetsImagesBoldUser,
        inActiveImage: Assets.assetsImagesOutlineUser,
        name: 'حسابي',
      ),
    ];
