import 'package:flutter/material.dart';
import '../../../../../core/utils/responsive_utils.dart';

import 'order_summry_widget.dart';
import 'shipping_address_widget.dart';

class PaymentSection extends StatelessWidget {
  const PaymentSection({super.key, required this.pageController});

  final PageController pageController;
  @override
  Widget build(BuildContext context) {
    context.initResponsive();
    return Column(
      children: [
        const RSizedBox.height(24),
        const OrderSummryWidget(),
        const RSizedBox.height(16),
        ShippingAddressWidget(
          pageController: pageController,
        ),
      ],
    );
  }
}
