import 'dart:developer';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../core/entities/product_entity.dart';
import '../../../domain/entities/category_entity.dart';
import '../../../domain/entities/company_entity.dart';

part 'search_filter_state.dart';

enum SortOption {
  featured,
  newest,
  oldest,
  bestSellers,
  priceLowToHigh,
  priceHighToLow,
  aToZ,
  zToA,
}

class SearchFilterCubit extends Cubit<SearchFilterState> {
  SearchFilterCubit() : super(SearchFilterInitial());

  String _searchQuery = '';
  SortOption _currentSort = SortOption.featured;
  List<ProductEntity> _originalProducts = [];

  String get searchQuery => _searchQuery;
  SortOption get currentSort => _currentSort;

  // Search Products
  void searchProducts(String query, List<ProductEntity> products) {
    log('🔍 [SearchFilter] Searching products: "$query"');
    _searchQuery = query;
    _originalProducts = products;

    final filteredProducts = _filterAndSortProducts(products, query);
    emit(ProductsFiltered(filteredProducts, query, _currentSort));
  }

  // Search Categories
  void searchCategories(String query, List<CategoryEntity> categories) {
    log('🔍 [SearchFilter] Searching categories: "$query"');
    _searchQuery = query;

    final filteredCategories = _filterCategories(categories, query);
    emit(CategoriesFiltered(filteredCategories, query));
  }

  // Search Companies
  void searchCompanies(String query, List<CompanyEntity> companies) {
    log('🔍 [SearchFilter] Searching companies: "$query"');
    _searchQuery = query;

    final filteredCompanies = _filterCompanies(companies, query);
    emit(CompaniesFiltered(filteredCompanies, query));
  }

  // Sort Products
  void sortProducts(SortOption sortOption) {
    log('📊 [SearchFilter] Sorting products: $sortOption');
    _currentSort = sortOption;

    // Use original products if available, otherwise use current filtered products
    List<ProductEntity> productsToSort = _originalProducts;

    // If we don't have original products, try to get them from current state
    if (productsToSort.isEmpty && state is ProductsFiltered) {
      final currentState = state as ProductsFiltered;
      productsToSort = currentState.products;
      log('📊 [SearchFilter] Using current filtered products for sorting');
    }

    final filteredProducts =
        _filterAndSortProducts(productsToSort, _searchQuery);
    log('📊 [SearchFilter] Emitting ${filteredProducts.length} sorted products');
    emit(ProductsFiltered(filteredProducts, _searchQuery, sortOption));
  }

  // Sort Products with external products (for category/company views)
  void sortProductsWithData(
      SortOption sortOption, List<ProductEntity> products) {
    log('📊 [SearchFilter] Sorting external products: $sortOption');
    _currentSort = sortOption;
    _originalProducts = products; // Update original products

    final filteredProducts = _filterAndSortProducts(products, _searchQuery);
    log('📊 [SearchFilter] Emitting ${filteredProducts.length} sorted external products');
    emit(ProductsFiltered(filteredProducts, _searchQuery, sortOption));
  }

  // Clear Search
  void clearSearch() {
    log('🧹 [SearchFilter] Clearing search');
    _searchQuery = '';
    emit(SearchFilterInitial());
  }

  // Filter and Sort Products
  List<ProductEntity> _filterAndSortProducts(
      List<ProductEntity> products, String query) {
    // Filter by search query
    List<ProductEntity> filtered = products;

    if (query.isNotEmpty) {
      filtered = products.where((product) {
        return product.name.toLowerCase().contains(query.toLowerCase()) ||
            product.category.toLowerCase().contains(query.toLowerCase()) ||
            product.company.toLowerCase().contains(query.toLowerCase());
      }).toList();
    }

    // Sort products
    switch (_currentSort) {
      case SortOption.featured:
        // Keep original order (products are already sorted by featured in backend)
        break;
      case SortOption.newest:
        // Sort by product code (assuming higher codes are newer)
        filtered.sort((a, b) => b.code.compareTo(a.code));
        break;
      case SortOption.oldest:
        // Sort by product code ascending (assuming lower codes are older)
        filtered.sort((a, b) => a.code.compareTo(b.code));
        break;
      case SortOption.bestSellers:
        // Sort by stock quantity (higher stock might indicate better sellers)
        filtered.sort(
            (a, b) => (b.stockQuantity ?? 0).compareTo(a.stockQuantity ?? 0));
        break;
      case SortOption.priceLowToHigh:
        filtered.sort((a, b) => a.price.compareTo(b.price));
        break;
      case SortOption.priceHighToLow:
        filtered.sort((a, b) => b.price.compareTo(a.price));
        break;
      case SortOption.aToZ:
        filtered.sort((a, b) => a.name.compareTo(b.name));
        break;
      case SortOption.zToA:
        filtered.sort((a, b) => b.name.compareTo(a.name));
        break;
    }

    log('📊 [SearchFilter] Filtered ${filtered.length} products from ${products.length}');
    return filtered;
  }

  // Filter Categories
  List<CategoryEntity> _filterCategories(
      List<CategoryEntity> categories, String query) {
    if (query.isEmpty) return categories;

    final filtered = categories.where((category) {
      return category.name.toLowerCase().contains(query.toLowerCase());
    }).toList();

    log('📊 [SearchFilter] Filtered ${filtered.length} categories from ${categories.length}');
    return filtered;
  }

  // Filter Companies
  List<CompanyEntity> _filterCompanies(
      List<CompanyEntity> companies, String query) {
    if (query.isEmpty) return companies;

    final filtered = companies.where((company) {
      return company.name.toLowerCase().contains(query.toLowerCase());
    }).toList();

    log('📊 [SearchFilter] Filtered ${filtered.length} companies from ${companies.length}');
    return filtered;
  }
}
