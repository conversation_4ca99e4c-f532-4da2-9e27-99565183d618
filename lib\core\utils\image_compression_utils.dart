import 'dart:developer';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/services.dart';
import 'package:image/image.dart' as img;

class ImageCompressionUtils {
  /// Compress image to reduce file size while maintaining quality
  static Future<File> compressImage({
    required File imageFile,
    int maxWidth = 1024,
    int maxHeight = 1024,
    int quality = 80,
    int maxSizeKB = 500, // Maximum size in KB
  }) async {
    try {
      log('Starting image compression...');
      log('Original file size: ${await imageFile.length()} bytes');

      // Read the image file
      final Uint8List imageBytes = await imageFile.readAsBytes();
      
      // Decode the image
      img.Image? image = img.decodeImage(imageBytes);
      if (image == null) {
        throw Exception('Failed to decode image');
      }

      log('Original image dimensions: ${image.width}x${image.height}');

      // Resize image if it's larger than max dimensions
      if (image.width > maxWidth || image.height > maxHeight) {
        image = img.copyResize(
          image,
          width: image.width > image.height ? maxWidth : null,
          height: image.height > image.width ? maxHeight : null,
          interpolation: img.Interpolation.linear,
        );
        log('Resized image dimensions: ${image.width}x${image.height}');
      }

      // Compress the image
      List<int> compressedBytes = img.encodeJpg(image, quality: quality);
      
      // Check if we need further compression
      int currentQuality = quality;
      while (compressedBytes.length > maxSizeKB * 1024 && currentQuality > 20) {
        currentQuality -= 10;
        compressedBytes = img.encodeJpg(image, quality: currentQuality);
        log('Recompressing with quality: $currentQuality, size: ${compressedBytes.length} bytes');
      }

      // Create a new file with compressed data
      final String originalPath = imageFile.path;
      final String directory = originalPath.substring(0, originalPath.lastIndexOf('/'));
      final String fileName = 'compressed_${DateTime.now().millisecondsSinceEpoch}.jpg';
      final String newPath = '$directory/$fileName';
      
      final File compressedFile = File(newPath);
      await compressedFile.writeAsBytes(compressedBytes);

      log('Compression completed!');
      log('Compressed file size: ${compressedBytes.length} bytes');
      log('Compression ratio: ${((1 - compressedBytes.length / imageBytes.length) * 100).toStringAsFixed(1)}%');
      log('Final quality: $currentQuality');

      return compressedFile;
    } catch (e) {
      log('Error compressing image: $e');
      // Return original file if compression fails
      return imageFile;
    }
  }

  /// Get optimal dimensions for image based on aspect ratio
  static Map<String, int> getOptimalDimensions({
    required int originalWidth,
    required int originalHeight,
    int maxWidth = 1024,
    int maxHeight = 1024,
  }) {
    final double aspectRatio = originalWidth / originalHeight;
    
    int newWidth, newHeight;
    
    if (originalWidth > originalHeight) {
      // Landscape
      newWidth = maxWidth;
      newHeight = (maxWidth / aspectRatio).round();
      
      if (newHeight > maxHeight) {
        newHeight = maxHeight;
        newWidth = (maxHeight * aspectRatio).round();
      }
    } else {
      // Portrait or square
      newHeight = maxHeight;
      newWidth = (maxHeight * aspectRatio).round();
      
      if (newWidth > maxWidth) {
        newWidth = maxWidth;
        newHeight = (maxWidth / aspectRatio).round();
      }
    }
    
    return {
      'width': newWidth,
      'height': newHeight,
    };
  }

  /// Check if image needs compression
  static Future<bool> needsCompression({
    required File imageFile,
    int maxSizeKB = 500,
  }) async {
    final int fileSizeBytes = await imageFile.length();
    return fileSizeBytes > maxSizeKB * 1024;
  }

  /// Get image file size in KB
  static Future<double> getImageSizeKB(File imageFile) async {
    final int fileSizeBytes = await imageFile.length();
    return fileSizeBytes / 1024;
  }

  /// Get image dimensions
  static Future<Map<String, int>?> getImageDimensions(File imageFile) async {
    try {
      final Uint8List imageBytes = await imageFile.readAsBytes();
      final img.Image? image = img.decodeImage(imageBytes);
      
      if (image != null) {
        return {
          'width': image.width,
          'height': image.height,
        };
      }
      return null;
    } catch (e) {
      log('Error getting image dimensions: $e');
      return null;
    }
  }
}
