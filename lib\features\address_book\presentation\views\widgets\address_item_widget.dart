import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../../../../core/global/themes/app_colors/app_colors_light.dart';
import '../../../../../core/utils/responsive_utils.dart';
import '../../../domain/entities/address_entity.dart';

class AddressItemWidget extends StatelessWidget {
  final AddressEntity address;
  final VoidCallback onEdit;
  final VoidCallback onDelete;
  final VoidCallback onSetDefault;

  const AddressItemWidget({
    super.key,
    required this.address,
    required this.onEdit,
    required this.onDelete,
    required this.onSetDefault,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: REdgeInsets.only(bottom: 16),
      padding: REdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: RBorderRadius.circular(12),
        border: address.isDefault
            ? Border.all(color: Theme.of(context).colorScheme.primary, width: 2)
            : Border.all(
                color: Theme.of(context).colorScheme.outline, width: 1),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).shadowColor.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Address Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Text(
                    address.title,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  if (address.isDefault) ...[
                    const RSizedBox.width(8),
                    Container(
                      padding:
                          REdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.primary,
                        borderRadius: RBorderRadius.circular(8),
                      ),
                      child: Text(
                        'افتراضي',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.white,
                            ),
                      ),
                    ),
                  ],
                ],
              ),
              PopupMenuButton<String>(
                onSelected: (value) {
                  switch (value) {
                    case 'edit':
                      onEdit();
                      break;
                    case 'delete':
                      onDelete();
                      break;
                    case 'default':
                      onSetDefault();
                      break;
                  }
                },
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        Icon(FontAwesomeIcons.penToSquare, size: 16),
                        SizedBox(width: 8),
                        Text('تعديل'),
                      ],
                    ),
                  ),
                  if (!address.isDefault)
                    const PopupMenuItem(
                      value: 'default',
                      child: Row(
                        children: [
                          Icon(FontAwesomeIcons.star, size: 16),
                          SizedBox(width: 8),
                          Text('جعل افتراضي'),
                        ],
                      ),
                    ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(FontAwesomeIcons.trash,
                            size: 16, color: Colors.red),
                        SizedBox(width: 8),
                        Text('حذف', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
          const RSizedBox.height(16),

          // Full Name
          Text(
            address.fullName,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const RSizedBox.height(8),

          // Phone Number
          Row(
            children: [
              Icon(
                FontAwesomeIcons.phone,
                size: ResponsiveUtils.iconSize(14),
                color: AppColorsLight.lightTextColor,
              ),
              const RSizedBox.width(8),
              Text(
                address.phoneNumber,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
          const RSizedBox.height(8),

          // Address
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(
                FontAwesomeIcons.locationDot,
                size: ResponsiveUtils.iconSize(14),
                color: AppColorsLight.lightTextColor,
              ),
              const RSizedBox.width(8),
              Expanded(
                child: Text(
                  address.fullAddress,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
