import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/services/get_it_service.dart';
import '../../../../core/utils/responsive_utils.dart';
import '../../domain/repos/orders_repo.dart';
import '../manager/orders_cubit/orders_cubit.dart';
import 'widgets/orders_view_body.dart';

enum OrderFilterType {
  current,
  completed,
  cancelled,
}

class OrdersView extends StatelessWidget {
  const OrdersView({super.key});
  static const String routeName = '/orders';

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => OrdersCubit(getIt<UserOrdersRepo>()),
      child: DefaultTabController(
        length: 3,
        child: Scaffold(
          appBar: AppBar(
            leading: IconButton(
              onPressed: () => Navigator.pop(context),
              icon: Icon(
                Icons.arrow_back_ios_new,
                size: ResponsiveUtils.iconSize(18),
              ),
            ),
            centerTitle: true,
            title: const Text('طلباتي'),
            bottom: TabBar(
              labelColor: Theme.of(context).colorScheme.inversePrimary,
              unselectedLabelColor: Theme.of(context).colorScheme.onPrimary,
              indicatorColor: Theme.of(context).colorScheme.onPrimary,
              tabs: const [
                Tab(
                  text: 'الحالية',
                ),
                Tab(text: 'مكتملة'),
                Tab(text: 'ملغية'),
              ],
            ),
          ),
          body: const TabBarView(
            children: [
              OrdersViewBody(filterType: OrderFilterType.current),
              OrdersViewBody(filterType: OrderFilterType.completed),
              OrdersViewBody(filterType: OrderFilterType.cancelled),
            ],
          ),
        ),
      ),
    );
  }
}
