import 'package:dots_indicator/dots_indicator.dart';
import 'package:flutter/material.dart';
import '../../../../../constants.dart';
import '../../../../../core/services/shared_preferences_singleton.dart';
import '../../../../../core/utils/responsive_utils.dart';
import '../../../../../core/widgets/custom_button.dart';
import '../../../../auth/presentation/views/login_view.dart';
import 'on_boarding_page_view.dart';

class OnBoardingViewBody extends StatefulWidget {
  const OnBoardingViewBody({super.key});

  @override
  State<OnBoardingViewBody> createState() => _OnBoardingViewBodyState();
}

class _OnBoardingViewBodyState extends State<OnBoardingViewBody> {
  late PageController pageController;
  var currentPage = 0;
  @override
  void initState() {
    super.initState();
    pageController = PageController();

    pageController.addListener(
      () {
        currentPage = pageController.page!.round();
        setState(() {});
      },
    );
  }

  @override
  void dispose() {
    pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.initResponsive();
    return Column(
      children: [
        Expanded(
          child: OnBoardingPageView(
            controller: pageController,
          ),
        ),
        DotsIndicator(
          dotsCount: 2,
          decorator: DotsDecorator(
            activeColor: Theme.of(context).colorScheme.primary,
            color: currentPage == 1
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context)
                    .colorScheme
                    .onSurface
                    .withValues(alpha: 0.5),
          ),
        ),
        const RSizedBox.height(29),
        Visibility(
          visible: currentPage == 1 ? true : false,
          maintainAnimation: true,
          maintainState: true,
          maintainSize: true,
          child: Padding(
            padding: REdgeInsets.symmetric(
              horizontal: kHorizontalPadding16,
            ),
            child: CustomButton(
              text: 'ابدأ الان',
              bgColor: Theme.of(context).colorScheme.primary,
              textColor: Colors.white,
              onPressed: () {
                SharedPreferencesSingleton.setBool(kIsOnBoardingViewSeen, true);
                Navigator.pushReplacementNamed(
                  context,
                  LoginView.routeName,
                );
              },
            ),
          ),
        ),
        const RSizedBox.height(kVerticalPaddingLarge43),
      ],
    );
  }
}
