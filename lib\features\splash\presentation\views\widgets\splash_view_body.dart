import 'package:flutter/material.dart';
import '../../../../../core/services/firebase_auth_service.dart';
import '../../../../home/<USER>/views/main_view.dart';
import '../../../../onBoarding/presentation/views/on_boarding_view.dart';

import '../../../../../constants.dart';
import '../../../../../core/services/shared_preferences_singleton.dart';
import '../../../../auth/presentation/views/login_view.dart';

class SplashViewBody extends StatefulWidget {
  const SplashViewBody({super.key});

  @override
  State<SplashViewBody> createState() => _SplashViewBodyState();
}

class _SplashViewBodyState extends State<SplashViewBody> {
  @override
  void initState() {
    super.initState();
    executeNavigation();
  }

  @override
  Widget build(BuildContext context) {
    return const Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Image(
          image: AssetImage(
            'assets/animations/logo.gif',
          ),
          colorBlendMode: BlendMode.screen,
        ),
      ],
    );
  }

  void executeNavigation() {
    final bool isOnBoardingViewSeen =
        SharedPreferencesSingleton.getBool(kIsOnBoardingViewSeen);
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        if (isOnBoardingViewSeen) {
          final isLogin = FirebaseAuthService().isLoggedIn();
          if (isLogin) {
            Navigator.pushReplacementNamed(context, MainView.routeName);
          } else {
            Navigator.pushReplacementNamed(context, LoginView.routeName);
          }
        } else {
          Navigator.pushReplacementNamed(context, OnBoardingView.routeName);
        }
      }
    });
  }
}
