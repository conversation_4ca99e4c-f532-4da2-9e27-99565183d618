/// Utility class for consistent Order ID formatting across the application
/// This ensures Order IDs are handled uniformly between different apps
class OrderIdFormatter {
  /// Generates a unique Order ID based on current timestamp
  /// Returns: String representation of milliseconds since epoch
  static String generateOrderId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }

  /// Formats Order ID for display purposes (shows last 6 digits with # prefix)
  /// Example: "1703123456789" -> "#456789"
  /// 
  /// [orderId] - The full order ID string
  /// Returns: Formatted string with # prefix and last 6 digits
  static String formatOrderIdForDisplay(String orderId) {
    if (orderId.isEmpty) return '#000000';
    
    if (orderId.length >= 6) {
      return '#${orderId.substring(orderId.length - 6)}';
    }
    
    // If order ID is less than 6 digits, pad with zeros
    return '#${orderId.padLeft(6, '0')}';
  }

  /// Formats Order ID with a standard prefix for API/Database operations
  /// Example: "1703123456789" -> "ORD-1703123456789"
  /// 
  /// [orderId] - The order ID string
  /// Returns: Formatted string with ORD- prefix
  static String formatFullOrderId(String orderId) {
    return 'ORD-$orderId';
  }

  /// Extracts clean Order ID from formatted strings
  /// Removes all non-numeric characters
  /// Example: "ORD-1703123456789" -> "1703123456789"
  /// Example: "#456789" -> "456789"
  /// 
  /// [formattedId] - The formatted order ID string
  /// Returns: Clean numeric order ID string
  static String extractOrderId(String formattedId) {
    return formattedId.replaceAll(RegExp(r'[^0-9]'), '');
  }

  /// Validates if an Order ID is in correct format
  /// Checks if the ID contains only numbers and has reasonable length
  /// 
  /// [orderId] - The order ID to validate
  /// Returns: true if valid, false otherwise
  static bool isValidOrderId(String orderId) {
    if (orderId.isEmpty) return false;
    
    // Check if contains only numbers
    if (!RegExp(r'^[0-9]+$').hasMatch(orderId)) return false;
    
    // Check reasonable length (between 6 and 15 digits)
    return orderId.length >= 6 && orderId.length <= 15;
  }

  /// Formats Order ID for search purposes
  /// Handles both full and partial order IDs
  /// 
  /// [searchText] - The search input text
  /// Returns: Clean order ID for database queries
  static String formatForSearch(String searchText) {
    final cleanId = extractOrderId(searchText);
    return cleanId.isEmpty ? searchText : cleanId;
  }

  /// Gets display text for order with Arabic prefix
  /// Example: "1703123456789" -> "طلب #456789"
  /// 
  /// [orderId] - The order ID string
  /// Returns: Arabic formatted display text
  static String getDisplayText(String orderId) {
    return 'طلب ${formatOrderIdForDisplay(orderId)}';
  }

  /// Gets short display format without Arabic text
  /// Useful for compact displays or English interfaces
  /// Example: "1703123456789" -> "#456789"
  /// 
  /// [orderId] - The order ID string
  /// Returns: Short formatted display text
  static String getShortDisplayText(String orderId) {
    return formatOrderIdForDisplay(orderId);
  }
}
