import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../core/helper/build_custom_snack_bar.dart';
import '../../../../../core/utils/responsive_utils.dart';
import '../../../../../core/widgets/custom_button.dart';
import '../../../../../core/widgets/custom_loading_indicator.dart';
import '../../../../../core/widgets/custom_text_field.dart';
import '../../../../auth/domain/entities/user_entity.dart';
import '../../../../auth/presentation/views/widgets/custom_phone_field.dart';
import '../../../../profile/presentation/manager/profile_cubit/profile_cubit.dart';

class AccountSettingsViewBody extends StatefulWidget {
  const AccountSettingsViewBody({super.key});

  @override
  State<AccountSettingsViewBody> createState() =>
      _AccountSettingsViewBodyState();
}

class _AccountSettingsViewBodyState extends State<AccountSettingsViewBody> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<ProfileCubit, ProfileState>(
      listener: (context, state) {
        if (state is ProfileUpdateSuccess) {
          showSuccessSnackBar(context, message: 'تم تحديث البيانات بنجاح');
          Navigator.pop(context);
        }
        if (state is ProfileError) {
          showErrorSnackBar(context, message: state.message);
        }
      },
      builder: (context, state) {
        if (state is ProfileLoading) {
          return Center(
              child: CustomLoadingIndicator(
            color: Theme.of(context).colorScheme.primary,
          ));
        }

        if (state is ProfileLoaded) {
          return _buildForm(context, state.userEntity);
        }

        // Load user profile
        context.read<ProfileCubit>().getUserProfile();
        return Center(
            child: CustomLoadingIndicator(
          color: Theme.of(context).colorScheme.primary,
        ));
      },
    );
  }

  Widget _buildForm(BuildContext context, UserEntity userEntity) {
    // Initialize controllers with current user data
    if (_nameController.text.isEmpty) {
      _nameController.text = userEntity.name;
      _phoneController.text = userEntity.phone;
      _emailController.text = userEntity.email;
    }

    return Padding(
      padding: REdgeInsets.symmetric(horizontal: 16),
      child: Form(
        key: _formKey,
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const RSizedBox.height(24),
                    CustomTextFormField(
                      label: 'الاسم',
                      keyboardType: TextInputType.name,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال الاسم';
                        }
                        return null;
                      },
                      onSaved: (value) => _nameController.text = value!,
                      initialValue: _nameController.text,
                    ),
                    const RSizedBox(height: 16),
                    CustomPhoneField(
                      initialValue: _phoneController.text,
                      onSaved: (value) => _phoneController.text = value!.number,
                      validator: (p0) {
                        if (p0 == null || p0.number.isEmpty) {
                          return 'يرجى إدخال رقم الهاتف';
                        }
                        return null;
                      },
                    ),
                    const RSizedBox(height: 16),
                    CustomTextFormField(
                      label: 'البريد الإلكتروني',
                      keyboardType: TextInputType.emailAddress,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال البريد الإلكتروني';
                        }
                        if (!value.contains('@')) {
                          return 'يرجى إدخال بريد إلكتروني صحيح';
                        }
                        return null;
                      },
                      onSaved: (value) => _emailController.text = value!,
                      initialValue: _emailController.text,
                    ),
                  ],
                ),
              ),
            ),
            const RSizedBox(height: 16),
            CustomButton(
              text: 'حفظ التغييرات',
              textColor: Colors.white,
              bgColor: Theme.of(context).colorScheme.primary,
              onPressed: () => _saveChanges(context, userEntity),
            ),
            const RSizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  void _saveChanges(BuildContext context, UserEntity currentUser) {
    if (_formKey.currentState!.validate()) {
      _formKey.currentState!.save();

      final updatedUser = UserEntity(
        uId: currentUser.uId,
        name: _nameController.text,
        phone: _phoneController.text,
        email: _emailController.text,
      );

      context.read<ProfileCubit>().updateUserProfile(userEntity: updatedUser);
    }
  }
}
