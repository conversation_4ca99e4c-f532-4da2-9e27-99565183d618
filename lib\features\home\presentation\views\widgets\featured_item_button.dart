import 'package:flutter/material.dart';

import '../../../../../core/global/themes/app_colors/app_colors_light.dart';

class FeaturedItemButton extends StatelessWidget {
  const FeaturedItemButton({super.key, required this.onPressed});
  final VoidCallback onPressed;
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 32,
      child: TextButton(
        style: TextButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(4),
          ),
          backgroundColor: Colors.white,
        ),
        onPressed: onPressed,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: FittedBox(
            child: Text(
              'تسوق الان',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColorsLight.primaryColor,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
