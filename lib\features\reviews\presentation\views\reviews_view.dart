import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/services/get_it_service.dart';
import '../../../../core/helper/build_app_bar.dart';
import '../../domain/repos/reviews_repo.dart';
import '../manager/reviews_cubit/reviews_cubit.dart';
import 'widgets/reviews_view_body.dart';

class ReviewsView extends StatelessWidget {
  const ReviewsView({
    super.key,
    required this.productId,
    this.productName,
  });

  final String productId;
  final String? productName;
  static const String routeName = '/reviews';

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => ReviewsCubit(getIt<ReviewsRepo>())
        ..getProductReviews(productId: productId),
      child: Scaffold(
        appBar: buildAppBarWithAlarmWidget(
          context,
          title: productName != null ? 'تقييمات $productName' : 'التقييمات',
          isBack: true,
          isNotification: false,
        ),
        body: ReviewsViewBody(productId: productId),
      ),
    );
  }
}
