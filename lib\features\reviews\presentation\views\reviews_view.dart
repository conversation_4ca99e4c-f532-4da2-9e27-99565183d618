import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/services/get_it_service.dart';
import '../../../../core/utils/responsive_utils.dart';
import '../../domain/repos/reviews_repo.dart';
import '../manager/reviews_cubit/reviews_cubit.dart';
import 'widgets/reviews_view_body.dart';

class ReviewsView extends StatelessWidget {
  const ReviewsView({
    super.key,
    required this.productId,
    this.productName,
  });

  final String productId;
  final String? productName;
  static const String routeName = '/reviews';

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return BlocProvider(
      create: (context) => ReviewsCubit(getIt<ReviewsRepo>())
        ..getProductReviews(productId: productId),
      child: Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        appBar: _buildEnhancedAppBar(context, theme),
        body: ReviewsViewBody(productId: productId),
      ),
    );
  }

  PreferredSizeWidget _buildEnhancedAppBar(
      BuildContext context, ThemeData theme) {
    return AppBar(
      backgroundColor: theme.appBarTheme.backgroundColor,
      foregroundColor: theme.appBarTheme.foregroundColor,
      elevation: 0,
      centerTitle: true,
      leading: IconButton(
        icon: Icon(
          Icons.arrow_back_ios_rounded,
          size: ResponsiveUtils.iconSize(20),
          color: theme.appBarTheme.foregroundColor,
        ),
        onPressed: () => Navigator.of(context).pop(),
      ),
      title: Text(
        productName != null ? 'تقييمات $productName' : 'التقييمات',
        style: theme.appBarTheme.titleTextStyle?.copyWith(
          fontSize: ResponsiveUtils.fontSize(18),
          fontWeight: FontWeight.w600,
        ),
      ),
      bottom: PreferredSize(
        preferredSize: Size.fromHeight(ResponsiveUtils.height(1)),
        child: Container(
          height: ResponsiveUtils.height(0.2),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                theme.dividerColor.withValues(alpha: 0.1),
                theme.dividerColor.withValues(alpha: 0.3),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
