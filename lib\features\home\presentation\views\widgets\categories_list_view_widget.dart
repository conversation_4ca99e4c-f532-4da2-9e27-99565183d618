import 'package:flutter/material.dart';

import '../../../../../core/utils/responsive_utils.dart';
import '../../../domain/entities/category_entity.dart';
import '../category_products_view.dart';
import 'category_item.dart';

class CategoriesListViewWidget extends StatelessWidget {
  const CategoriesListViewWidget({
    super.key,
    required this.categories,
  });
  final List<CategoryEntity> categories;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: ResponsiveUtils.spacing(80),
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        itemCount: categories.length,
        separatorBuilder: (context, index) => const RSizedBox.width(12),
        itemBuilder: (context, index) {
          return CategoryItem(
            category: categories[index],
            hasImage: false,
            onTap: () {
              debugPrint('🏷️ Category tapped: ${categories[index].name}');
              Navigator.pushNamed(
                context,
                CategoryProductsView.routeName,
                arguments: categories[index].name,
              );
            },
          );
        },
      ),
    );
  }
}
