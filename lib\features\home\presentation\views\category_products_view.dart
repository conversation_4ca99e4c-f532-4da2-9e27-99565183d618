import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/helper/build_app_bar.dart';
import '../../../../core/services/get_it_service.dart';
import '../../domain/repos/categories_repo.dart';
import '../manager/categories_cubit/categories_cubit.dart';
import '../manager/cart_cubit/cart_cubit.dart';
import '../manager/search_filter_cubit/search_filter_cubit.dart';
import 'widgets/category_products_view_body.dart';

class CategoryProductsView extends StatelessWidget {
  const CategoryProductsView({
    super.key,
    required this.categoryName,
  });

  final String categoryName;
  static const String routeName = '/category-products';

  @override
  Widget build(BuildContext context) {
    debugPrint(
        '🏗️ [CategoryProductsView] Building view for category: "$categoryName"');

    return Scaffold(
      appBar: buildAppBarWithAlarmWidget(
        context,
        title: categoryName,
        isBack: true,
        isNotification: false,
      ),
      body: MultiBlocProvider(
        providers: [
          BlocProvider(
            create: (context) => CategoriesCubit(
              getIt.get<CategoriesRepo>(),
            )..getCategoryProducts(
                category: categoryName,
              ),
          ),
          BlocProvider(
            create: (context) => SearchFilterCubit(),
          ),
          //? [.value] to use the same instance of the cubit
          BlocProvider.value(
            value: getIt<CartCubit>(),
          ),
        ],
        child: CategoryProductsViewBody(categoryName: categoryName),
      ),
    );
  }
}
