import 'package:equatable/equatable.dart';

class ReviewEntity extends Equatable {
  final String id;
  final String userId;
  final String productId;
  final String userName;
  final String userImage;
  final num rating;
  final String reviewText;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final bool isVerifiedPurchase;

  const ReviewEntity({
    required this.id,
    required this.userId,
    required this.productId,
    required this.userName,
    required this.userImage,
    required this.rating,
    required this.reviewText,
    required this.createdAt,
    this.updatedAt,
    this.isVerifiedPurchase = false,
  });

  @override
  List<Object?> get props => [
        id,
        userId,
        productId,
        userName,
        userImage,
        rating,
        reviewText,
        createdAt,
        updatedAt,
        isVerifiedPurchase,
      ];

  ReviewEntity copyWith({
    String? id,
    String? userId,
    String? productId,
    String? userName,
    String? userImage,
    num? rating,
    String? reviewText,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isVerifiedPurchase,
  }) {
    return ReviewEntity(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      productId: productId ?? this.productId,
      userName: userName ?? this.userName,
      userImage: userImage ?? this.userImage,
      rating: rating ?? this.rating,
      reviewText: reviewText ?? this.reviewText,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isVerifiedPurchase: isVerifiedPurchase ?? this.isVerifiedPurchase,
    );
  }
}
