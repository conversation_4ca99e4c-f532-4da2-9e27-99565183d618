import 'package:flutter/material.dart';

abstract class ThemeState {
  final ThemeData themeData;
  final bool isDarkMode;
  
  const ThemeState({
    required this.themeData,
    required this.isDarkMode,
  });
}

class ThemeInitial extends ThemeState {
  const ThemeInitial({
    required super.themeData,
    required super.isDarkMode,
  });
}

class ThemeChanged extends ThemeState {
  const ThemeChanged({
    required super.themeData,
    required super.isDarkMode,
  });
}
