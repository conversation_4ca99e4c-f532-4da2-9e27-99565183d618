import 'package:dartz/dartz.dart';
import '../../../../core/errors/failure.dart';
import '../entities/user_order_entity.dart';

abstract class UserOrdersRepo {
  Future<Either<Failure, List<UserOrderEntity>>> getUserOrders(
      {required String userId});
  Future<Either<Failure, UserOrderEntity>> getOrderById(
      {required String orderId});
  Future<Either<Failure, void>> cancelOrder({required String orderId});
  Future<Either<Failure, void>> updateOrderStatus(
      {required String orderId, required String status});
}
