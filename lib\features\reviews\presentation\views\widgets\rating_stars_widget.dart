import 'package:flutter/material.dart';
import '../../../../../core/utils/responsive_utils.dart';

class RatingStarsWidget extends StatelessWidget {
  const RatingStarsWidget({
    super.key,
    required this.rating,
    this.size = 16,
    this.color,
    this.unratedColor,
    this.allowHalfRating = true,
    this.showRatingText = false,
    this.spacing = 2,
  });

  final double rating;
  final double size;
  final Color? color;
  final Color? unratedColor;
  final bool allowHalfRating;
  final bool showRatingText;
  final double spacing;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final starColor = color ?? _getStarColor(rating);
    final emptyColor =
        unratedColor ?? theme.colorScheme.onSurface.withValues(alpha: 0.2);

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        ...List.generate(5, (index) {
          final starIndex = index + 1;

          Widget star;
          if (rating >= starIndex) {
            // Full star with enhanced styling
            star = Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(ResponsiveUtils.radius(2)),
                boxShadow: [
                  BoxShadow(
                    color: starColor.withValues(alpha: 0.3),
                    blurRadius: ResponsiveUtils.radius(2),
                    offset: Offset(0, ResponsiveUtils.height(1)),
                  ),
                ],
              ),
              child: Icon(
                Icons.star_rounded,
                size: ResponsiveUtils.iconSize(size),
                color: starColor,
              ),
            );
          } else if (allowHalfRating && rating >= starIndex - 0.5) {
            // Half star with enhanced styling
            star = Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(ResponsiveUtils.radius(2)),
                boxShadow: [
                  BoxShadow(
                    color: starColor.withValues(alpha: 0.2),
                    blurRadius: ResponsiveUtils.radius(1),
                    offset: Offset(0, ResponsiveUtils.height(0.5)),
                  ),
                ],
              ),
              child: Icon(
                Icons.star_half_rounded,
                size: ResponsiveUtils.iconSize(size),
                color: starColor,
              ),
            );
          } else {
            // Empty star with subtle styling
            star = Icon(
              Icons.star_outline_rounded,
              size: ResponsiveUtils.iconSize(size),
              color: emptyColor,
            );
          }

          return Padding(
            padding: EdgeInsets.only(
              right: index < 4 ? ResponsiveUtils.width(spacing) : 0,
            ),
            child: star,
          );
        }),
        if (showRatingText) ...[
          SizedBox(width: ResponsiveUtils.width(6)),
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: ResponsiveUtils.width(6),
              vertical: ResponsiveUtils.height(2),
            ),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface.withValues(alpha: 0.8),
              borderRadius: BorderRadius.circular(ResponsiveUtils.radius(8)),
              border: Border.all(
                color: theme.dividerColor.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: Text(
              rating.toStringAsFixed(1),
              style: theme.textTheme.bodySmall?.copyWith(
                fontSize: ResponsiveUtils.fontSize(11),
                fontWeight: FontWeight.w600,
                color: theme.colorScheme.onSurface,
              ),
            ),
          ),
        ],
      ],
    );
  }

  Color _getStarColor(double rating) {
    if (rating >= 4.5) {
      return Colors.green;
    } else if (rating >= 3.5) {
      return Colors.amber;
    } else if (rating >= 2.5) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }
}
