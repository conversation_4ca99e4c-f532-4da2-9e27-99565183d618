import 'package:flutter/material.dart';

class RatingStarsWidget extends StatelessWidget {
  const RatingStarsWidget({
    super.key,
    required this.rating,
    this.size = 16,
    this.color,
    this.unratedColor,
    this.allowHalfRating = true,
  });

  final double rating;
  final double size;
  final Color? color;
  final Color? unratedColor;
  final bool allowHalfRating;

  @override
  Widget build(BuildContext context) {
    final starColor = color ?? Colors.amber;
    final emptyColor = unratedColor ?? Colors.grey[300]!;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(5, (index) {
        final starIndex = index + 1;
        
        if (rating >= starIndex) {
          // Full star
          return Icon(
            Icons.star,
            size: size,
            color: starColor,
          );
        } else if (allowHalfRating && rating >= starIndex - 0.5) {
          // Half star
          return Icon(
            Icons.star_half,
            size: size,
            color: starColor,
          );
        } else {
          // Empty star
          return Icon(
            Icons.star_border,
            size: size,
            color: emptyColor,
          );
        }
      }),
    );
  }
}
