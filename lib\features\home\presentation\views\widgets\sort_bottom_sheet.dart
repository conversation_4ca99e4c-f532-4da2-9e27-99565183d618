import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../../../../constants.dart';
import '../../../../../core/utils/responsive_utils.dart';
import '../../manager/search_filter_cubit/search_filter_cubit.dart';

class SortBottomSheet extends StatelessWidget {
  const SortBottomSheet({super.key, this.products});

  final List<dynamic>?
      products; // Products to sort (for category/company views)

  @override
  Widget build(BuildContext context) {
    context.initResponsive();

    return Container(
      padding: REdgeInsets.all(kHorizontalPadding16),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: const BorderRadius.vertical(
          top: Radius.circular(20),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.only(bottom: 20),
            decoration: BoxDecoration(
              color: Theme.of(context).dividerColor,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'ترتيب حسب',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              IconButton(
                onPressed: () => Navigator.pop(context),
                icon: const Icon(Icons.close),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Sort Options
          BlocBuilder<SearchFilterCubit, SearchFilterState>(
            builder: (context, state) {
              // Get current sort from cubit
              final searchFilterCubit = context.read<SearchFilterCubit>();
              final currentSort = searchFilterCubit.currentSort;

              return Column(
                children: [
                  _buildSortOption(
                    context,
                    'مميز',
                    SortOption.featured,
                    currentSort,
                  ),
                  _buildSortOption(
                    context,
                    'الأحدث',
                    SortOption.newest,
                    currentSort,
                  ),
                  _buildSortOption(
                    context,
                    'الأقدم',
                    SortOption.oldest,
                    currentSort,
                  ),
                  _buildSortOption(
                    context,
                    'الأكثر مبيعاً',
                    SortOption.bestSellers,
                    currentSort,
                  ),
                  _buildSortOption(
                    context,
                    'السعر: من الأقل للأعلى',
                    SortOption.priceLowToHigh,
                    currentSort,
                  ),
                  _buildSortOption(
                    context,
                    'السعر: من الأعلى للأقل',
                    SortOption.priceHighToLow,
                    currentSort,
                  ),
                  _buildSortOption(
                    context,
                    'أ إلى ي',
                    SortOption.aToZ,
                    currentSort,
                  ),
                  _buildSortOption(
                    context,
                    'ي إلى أ',
                    SortOption.zToA,
                    currentSort,
                  ),
                ],
              );
            },
          ),
          const RSizedBox.height(16),
        ],
      ),
    );
  }

  Widget _buildSortOption(
    BuildContext context,
    String title,
    SortOption option,
    SortOption currentSort,
  ) {
    final isSelected = option == currentSort;

    return InkWell(
      onTap: () {
        // Apply the sort option
        final searchFilterCubit = context.read<SearchFilterCubit>();

        if (products != null && products!.isNotEmpty) {
          // Use external products for sorting (category/company views)
          searchFilterCubit.sortProductsWithData(option, products!.cast());
        } else {
          // Use internal products for sorting (main products view)
          searchFilterCubit.sortProducts(option);
        }

        Navigator.pop(context);
      },
      child: Container(
        width: double.infinity,
        padding: REdgeInsets.symmetric(vertical: 16, horizontal: 8),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: Theme.of(context).dividerColor.withValues(alpha: 0.3),
              width: 0.5,
            ),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: isSelected
                        ? Theme.of(context).colorScheme.primary
                        : Theme.of(context).colorScheme.onSurface,
                    fontWeight:
                        isSelected ? FontWeight.w600 : FontWeight.normal,
                  ),
            ),
            if (isSelected)
              Icon(
                FontAwesomeIcons.check,
                color: Theme.of(context).colorScheme.onPrimary,
                size: 20,
              ),
          ],
        ),
      ),
    );
  }
}
