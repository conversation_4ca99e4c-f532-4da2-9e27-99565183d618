import 'dart:developer';
import 'package:bloc/bloc.dart';
import '../../../../../core/entities/product_entity.dart';
import '../../../domain/entities/cart_entity.dart';
import '../../../domain/entities/cart_item_entity.dart';
import 'package:meta/meta.dart';

part 'cart_state.dart';

class CartCubit extends Cubit<CartState> {
  CartCubit() : super(CartInitial());
  CartEntity cartEntity = CartEntity(cartItems: []);

  void _logCartState(String operation) {
    log('📊 [CartCubit] Cart state after $operation:');
    log('📊 [CartCubit] Total items: ${cartEntity.cartItems.length}');
    for (int i = 0; i < cartEntity.cartItems.length; i++) {
      final item = cartEntity.cartItems[i];
      log('📊   [$i] ${item.productEntity.name} - Qty: ${item.quantity} - Price: ${item.calculateTotalPrice()}');
    }
    log('📊 [CartCubit] Total cart value: ${cartEntity.getTotalPrice()}');
    log('📊 [CartCubit] ═══════════════════════════════════════');
  }

  void addProduct(ProductEntity productEntity) {
    log('🛒 [CartCubit] Adding product: ${productEntity.name} (${productEntity.code})');
    log('🛒 [CartCubit] Current cart items count: ${cartEntity.cartItems.length}');

    //? product exist in cart or not ??
    final bool isProductExist = cartEntity.isExistProduct(productEntity);
    log('🛒 [CartCubit] Product exists in cart: $isProductExist');

    if (isProductExist) {
      // Find the existing item and update its quantity
      final index = cartEntity.cartItems.indexWhere(
        (item) => item.productEntity.code == productEntity.code,
      );
      log('🛒 [CartCubit] Found existing item at index: $index');

      if (index != -1) {
        final existingItem = cartEntity.cartItems[index];
        log('🛒 [CartCubit] Existing item quantity: ${existingItem.quantity}');

        final updatedItem = existingItem.increaseQuantity();
        log('🛒 [CartCubit] Updated item quantity: ${updatedItem.quantity}');

        cartEntity.cartItems[index] = updatedItem;
        log('🛒 [CartCubit] Item updated at index $index');
      }
    } else {
      // Add new item
      final newItem = CartItemEntity(productEntity: productEntity, quantity: 1);
      log('🛒 [CartCubit] Creating new item with quantity: ${newItem.quantity}');

      cartEntity.addCartItem(newItem);
      log('🛒 [CartCubit] New item added. Total items: ${cartEntity.cartItems.length}');
    }

    // Log final cart state
    log('🛒 [CartCubit] Final cart state:');
    for (int i = 0; i < cartEntity.cartItems.length; i++) {
      final item = cartEntity.cartItems[i];
      log('  [$i] ${item.productEntity.name} - Qty: ${item.quantity}');
    }

    _logCartState('addProduct');
    emit(CartItemAdded());
    log('🛒 [CartCubit] CartItemAdded event emitted');
  }

  void deleteCartItemByIndex(int index) {
    log('🗑️ [CartCubit] Deleting item at index: $index');
    log('🗑️ [CartCubit] Current cart items count: ${cartEntity.cartItems.length}');

    // Log current cart state before deletion
    log('🗑️ [CartCubit] Cart state before deletion:');
    for (int i = 0; i < cartEntity.cartItems.length; i++) {
      final item = cartEntity.cartItems[i];
      log('  [$i] ${item.productEntity.name} - Qty: ${item.quantity} ${i == index ? '← TO DELETE' : ''}');
    }

    if (index >= 0 && index < cartEntity.cartItems.length) {
      final itemToDelete = cartEntity.cartItems[index];
      log('🗑️ [CartCubit] Deleting: ${itemToDelete.productEntity.name} at index $index');

      cartEntity.cartItems.removeAt(index);
      log('🗑️ [CartCubit] Item deleted. New cart count: ${cartEntity.cartItems.length}');

      // Log cart state after deletion
      log('🗑️ [CartCubit] Cart state after deletion:');
      for (int i = 0; i < cartEntity.cartItems.length; i++) {
        final item = cartEntity.cartItems[i];
        log('  [$i] ${item.productEntity.name} - Qty: ${item.quantity}');
      }

      _logCartState('deleteCartItemByIndex');
      emit(CartItemRemoved());
      log('🗑️ [CartCubit] CartItemRemoved event emitted');
    } else {
      log('🗑️ [CartCubit] ❌ Invalid index: $index (cart size: ${cartEntity.cartItems.length})');
    }
  }

  void deleteCartItem(CartItemEntity cartItem) {
    final index = cartEntity.cartItems.indexWhere(
      (item) => item.productEntity.code == cartItem.productEntity.code,
    );

    if (index != -1) {
      cartEntity.cartItems.removeAt(index);
      emit(CartItemRemoved());
    }
  }

  void updateCartItem(CartItemEntity oldItem, CartItemEntity newItem) {
    log('🔄 [CartCubit] Updating cart item: ${oldItem.productEntity.name}');
    log('🔄 [CartCubit] Old quantity: ${oldItem.quantity} → New quantity: ${newItem.quantity}');

    final index = cartEntity.cartItems.indexWhere(
      (item) => item.productEntity.code == oldItem.productEntity.code,
    );
    log('🔄 [CartCubit] Found item at index: $index');

    if (index != -1) {
      log('🔄 [CartCubit] Before update at index $index: ${cartEntity.cartItems[index].quantity}');
      cartEntity.cartItems[index] = newItem;
      log('🔄 [CartCubit] After update at index $index: ${cartEntity.cartItems[index].quantity}');

      // Log final cart state
      log('🔄 [CartCubit] Updated cart state:');
      for (int i = 0; i < cartEntity.cartItems.length; i++) {
        final item = cartEntity.cartItems[i];
        log('  [$i] ${item.productEntity.name} - Qty: ${item.quantity} ${i == index ? '← UPDATED' : ''}');
      }

      _logCartState('updateCartItem');
      emit(CartItemAdded()); // Reuse existing state to trigger rebuild
      log('🔄 [CartCubit] CartItemAdded event emitted for update');
    } else {
      log('🔄 [CartCubit] ❌ Item not found for update: ${oldItem.productEntity.name}');
    }
  }
}
