import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/cubits/favorites_cubit/favorites_cubit.dart';
import '../../../../core/services/get_it_service.dart';
import '../manager/cart_cubit/cart_cubit.dart';
import 'widgets/custom_bottom_nav_bar.dart';
import 'widgets/main_view_body_bloc_listener.dart';

class MainView extends StatefulWidget {
  const MainView({super.key});
  static const String routeName = '/home';

  @override
  State<MainView> createState() => _MainViewState();
}

class _MainViewState extends State<MainView> {
  int currentViewIndex = 0;

  @override
  void initState() {
    super.initState();
    // Initialize favorites when the main view loads with error handling
    try {
      getIt<GlobalFavoritesCubit>().loadFavorites();
    } catch (e) {
      log('Error loading favorites in MainView: $e');
      // Don't crash the app, just log the error
    }
  }

  void _changeTab(int index) {
    setState(() {
      currentViewIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: getIt<CartCubit>(),
      child: Scaffold(
        bottomNavigationBar: CustomBottomNavBar(
          onItemTapped: _changeTab,
          currentIndex: currentViewIndex,
        ),
        body: SafeArea(
          child: MainViewBodyBlocListener(
            currentViewIndex: currentViewIndex,
            onTabChange: _changeTab,
          ),
        ),
      ),
    );
  }
}
