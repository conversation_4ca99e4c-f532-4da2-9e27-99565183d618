import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../../../../core/helper/build_custom_snack_bar.dart';
import '../../../../../core/helper/get_user.dart';
import '../../../../../core/utils/app_animations_lottie.dart';
import '../../../../../core/utils/responsive_utils.dart';
import '../../../../../core/widgets/custom_empty_state_widget.dart';
import '../../../../../core/widgets/custom_loading_indicator.dart';
import '../../../domain/entities/order_status_entity.dart';
import '../../manager/orders_cubit/orders_cubit.dart';
import '../orders_view.dart';
import 'order_item_widget.dart';

class OrdersViewBody extends StatelessWidget {
  const OrdersViewBody({super.key, required this.filterType});

  final OrderFilterType filterType;

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<OrdersCubit, OrdersState>(
      listener: (context, state) {
        if (state is OrdersError) {
          showErrorSnackBar(context, message: state.message);
        }
        if (state is OrderCancelled) {
          showSuccessSnackBar(context, message: 'تم إلغاء الطلب بنجاح');
        }
        if (state is OrderStatusUpdated) {
          showSuccessSnackBar(context, message: 'تم تحديث حالة الطلب بنجاح');
        }
      },
      builder: (context, state) {
        if (state is OrdersLoading) {
          return Center(
              child: CustomLoadingIndicator(
            color: Theme.of(context).colorScheme.primary,
          ));
        }

        if (state is OrdersLoaded) {
          final filteredOrders = _filterOrders(state.orders);
          if (filteredOrders.isEmpty) {
            return _buildEmptyState(context);
          }
          return _buildOrdersList(context, filteredOrders);
        }

        // Load orders on initial state
        final user = getUser();
        context.read<OrdersCubit>().getUserOrders(userId: user.uId);
        return Center(
            child: CustomLoadingIndicator(
          color: Theme.of(context).colorScheme.primary,
        ));
      },
    );
  }

  List<dynamic> _filterOrders(List<dynamic> orders) {
    return orders.where((order) {
      switch (filterType) {
        case OrderFilterType.current:
          return order.status == OrderStatus.pending ||
              order.status == OrderStatus.confirmed ||
              order.status == OrderStatus.processing ||
              order.status == OrderStatus.shipped;
        case OrderFilterType.completed:
          return order.status == OrderStatus.delivered;
        case OrderFilterType.cancelled:
          return order.status == OrderStatus.cancelled;
      }
    }).toList();
  }

  String _getEmptyStateTitle() {
    switch (filterType) {
      case OrderFilterType.current:
        return 'لا توجد طلبات حالية';
      case OrderFilterType.completed:
        return 'لا توجد طلبات مكتملة';
      case OrderFilterType.cancelled:
        return 'لا توجد طلبات ملغية';
    }
  }

  String _getEmptyStateSubtitle() {
    switch (filterType) {
      case OrderFilterType.current:
        return 'ستظهر طلباتك الحالية هنا';
      case OrderFilterType.completed:
        return 'ستظهر طلباتك المكتملة هنا';
      case OrderFilterType.cancelled:
        return 'ستظهر طلباتك الملغية هنا';
    }
  }

  Widget _buildEmptyState(BuildContext context) {
    return RefreshIndicator(
      onRefresh: () async {
        final user = getUser();
        context.read<OrdersCubit>().getUserOrders(userId: user.uId);
      },
      child: CustomScrollView(
        physics: const AlwaysScrollableScrollPhysics(
          parent: BouncingScrollPhysics(),
        ),
        slivers: [
          SliverFillRemaining(
            hasScrollBody: false,
            child: Padding(
              padding: REdgeInsets.all(16),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CustomEmptyStateWidget(
                    lottieAsset: AppAnimationsLottie.emptyOrders,
                    title: _getEmptyStateTitle(),
                    subtitle: _getEmptyStateSubtitle(),
                  ),
                  const RSizedBox.height(32),
                  ElevatedButton.icon(
                    onPressed: () {
                      final user = getUser();
                      context
                          .read<OrdersCubit>()
                          .getUserOrders(userId: user.uId);
                    },
                    icon: const Icon(FontAwesomeIcons.arrowsRotate, size: 16),
                    label: const Text('تحديث'),
                    style: ElevatedButton.styleFrom(
                      padding: REdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 12,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: RBorderRadius.circular(8),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrdersList(BuildContext context, orders) {
    return RefreshIndicator(
      onRefresh: () async {
        final user = getUser();
        context.read<OrdersCubit>().getUserOrders(userId: user.uId);
      },
      child: CustomScrollView(
        physics: const AlwaysScrollableScrollPhysics(
          parent: BouncingScrollPhysics(),
        ),
        slivers: [
          SliverPadding(
            padding: const EdgeInsets.all(16),
            sliver: SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  return Padding(
                    padding: EdgeInsets.only(
                      bottom: index < orders.length - 1 ? 16 : 0,
                    ),
                    child: OrderItemWidget(
                      order: orders[index],
                      onCancelOrder: () {
                        final user = getUser();
                        context.read<OrdersCubit>().cancelOrder(
                              orderId: orders[index].id,
                              userId: user.uId,
                            );
                      },
                    ),
                  );
                },
                childCount: orders.length,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
