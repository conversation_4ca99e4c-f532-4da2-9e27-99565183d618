import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../constants.dart';
import '../../manager/categories_cubit/categories_cubit.dart';
import '../../manager/search_filter_cubit/search_filter_cubit.dart';
import 'category_products_gridview_blocbuilder.dart';
import 'products_result_header.dart';
import 'search_and_sort_header.dart';

class CategoryProductsViewBody extends StatelessWidget {
  const CategoryProductsViewBody({
    super.key,
    required this.categoryName,
  });

  final String categoryName;

  @override
  Widget build(BuildContext context) {
    return CustomScrollView(
      slivers: [
        SliverToBoxAdapter(
          child: Column(
            children: [
              const SizedBox(height: kTopPadding16),
              // Search and Sort Header
              BlocBuilder<CategoriesCubit, CategoriesState>(
                builder: (context, categoriesState) {
                  List<dynamic>? products;
                  if (categoriesState is CategoryProductsSuccess) {
                    products = categoriesState.products;
                  }

                  return SearchAndSortHeader(
                    hintText: 'ابحث في منتجات $categoryName...',
                    products: products,
                    onSearchChanged: (query) {
                      if (categoriesState is CategoryProductsSuccess) {
                        context.read<SearchFilterCubit>().searchProducts(
                              query,
                              categoriesState.products,
                            );
                      }
                    },
                  );
                },
              ),
              const SizedBox(height: kVerticalPaddingSmall16),
              // Products Count Header
              BlocBuilder<SearchFilterCubit, SearchFilterState>(
                builder: (context, searchState) {
                  return BlocBuilder<CategoriesCubit, CategoriesState>(
                    builder: (context, categoriesState) {
                      int productsLength = 0;

                      if (searchState is ProductsFiltered) {
                        productsLength = searchState.products.length;
                      } else if (categoriesState is CategoryProductsSuccess) {
                        productsLength = categoriesState.products.length;
                      }

                      return Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: kHorizontalPadding16),
                        child: ProductsResultHeader(
                          productsLength: productsLength,
                        ),
                      );
                    },
                  );
                },
              ),
              const SizedBox(height: kVerticalPaddingSmall16),
            ],
          ),
        ),
        CategoryProductsGridViewBlocBuilder(
          categoryName: categoryName,
        ),
      ],
    );
  }
}
