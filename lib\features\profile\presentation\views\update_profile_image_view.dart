import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/helper/build_custom_snack_bar.dart';
import '../../../../core/helper/get_user.dart';
import '../../../../core/services/get_it_service.dart';
import '../../../../core/utils/responsive_utils.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/custom_progress_hud.dart';
import '../../../auth/presentation/views/widgets/custom_app_bar.dart';
import '../../../auth/presentation/views/widgets/image_picker_widget.dart';
import '../../../auth/domain/entities/user_entity.dart';
import '../../domain/repos/profile_repo.dart';
import '../manager/update_profile_image_cubit/update_profile_image_cubit.dart';
import 'widgets/profile_image_display.dart';

class UpdateProfileImageView extends StatefulWidget {
  const UpdateProfileImageView({super.key});
  static const routeName = '/update-profile-image';

  @override
  State<UpdateProfileImageView> createState() => _UpdateProfileImageViewState();
}

class _UpdateProfileImageViewState extends State<UpdateProfileImageView> {
  File? _selectedImage;
  late UserEntity _currentUser;

  @override
  void initState() {
    super.initState();
    _currentUser = getUser();
  }

  @override
  Widget build(BuildContext context) {
    context.initResponsive();

    return BlocProvider(
      create: (context) => UpdateProfileImageCubit(getIt<ProfileRepo>()),
      child: Scaffold(
        appBar: buildCustomAppBar(
          context,
          title: 'تحديث الصورة الشخصية',
          onTap: () => Navigator.pop(context),
        ),
        body: BlocConsumer<UpdateProfileImageCubit, UpdateProfileImageState>(
          listener: (context, state) {
            if (state is UpdateProfileImageSuccess) {
              showSuccessSnackBar(
                context,
                message: 'تم تحديث الصورة الشخصية بنجاح',
              );
              Navigator.pop(context, state.updatedUser);
            } else if (state is UpdateProfileImageError) {
              showErrorSnackBar(context, message: state.message);
            }
          },
          builder: (context, state) {
            return CustomProgressHud(
              isLoading: state is UpdateProfileImageLoading,
              child: SingleChildScrollView(
                padding: REdgeInsets.all(ResponsiveUtils.spacing(20)),
                child: Column(
                  children: [
                    RSizedBox.height(ResponsiveUtils.spacing(20)),

                    // Current Profile Image
                    Text(
                      'الصورة الحالية',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    RSizedBox.height(ResponsiveUtils.spacing(16)),
                    ProfileImageDisplay(
                      imageUrl: _currentUser.imageUrl,
                      size: 120,
                    ),

                    RSizedBox.height(ResponsiveUtils.spacing(40)),

                    // New Image Picker
                    Text(
                      'اختر صورة جديدة',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    RSizedBox.height(ResponsiveUtils.spacing(16)),
                    ImagePickerWidget(
                      onImageSelected: (image) {
                        setState(() {
                          _selectedImage = image;
                        });
                      },
                      size: 120,
                    ),

                    RSizedBox.height(ResponsiveUtils.spacing(40)),

                    // Update Button
                    CustomButton(
                      text: 'تحديث الصورة',
                      bgColor: Theme.of(context).colorScheme.primary,
                      textColor: Colors.white,
                      onPressed: _selectedImage != null
                          ? () {
                              context
                                  .read<UpdateProfileImageCubit>()
                                  .updateProfileImage(
                                    imageFile: _selectedImage!,
                                    currentUser: _currentUser,
                                  );
                            }
                          : null,
                    ),

                    RSizedBox.height(ResponsiveUtils.spacing(20)),

                    // Info Text
                    Container(
                      padding: REdgeInsets.all(ResponsiveUtils.spacing(16)),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surfaceContainer,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Column(
                        children: [
                          Icon(
                            Icons.info_outline,
                            color: Theme.of(context).colorScheme.primary,
                            size: ResponsiveUtils.spacing(24),
                          ),
                          RSizedBox.height(ResponsiveUtils.spacing(8)),
                          Text(
                            'نصائح لأفضل صورة شخصية:',
                            style: Theme.of(context).textTheme.titleSmall,
                            textAlign: TextAlign.center,
                          ),
                          RSizedBox.height(ResponsiveUtils.spacing(8)),
                          Text(
                            '• استخدم صورة واضحة ومضيئة\n'
                            '• تأكد من ظهور وجهك بوضوح\n'
                            '• الحد الأقصى لحجم الصورة 5 ميجابايت\n'
                            '• الصيغ المدعومة: JPG, PNG',
                            style: Theme.of(context).textTheme.bodySmall,
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
