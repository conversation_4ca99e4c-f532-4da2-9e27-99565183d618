import 'package:flutter/material.dart';
import '../../../../../core/utils/responsive_utils.dart';

class ActiveStepItem extends StatelessWidget {
  const ActiveStepItem({super.key, required this.text});
  final String text;
  @override
  Widget build(BuildContext context) {
    context.initResponsive();
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        CircleAvatar(
          radius: ResponsiveUtils.spacing(11.5),
          backgroundColor: Theme.of(context).colorScheme.primary,
          child: Icon(
            Icons.check,
            size: ResponsiveUtils.iconSize(18),
            color: Theme.of(context).colorScheme.onPrimary,
          ),
        ),
        const RSizedBox.width(4),
        Text(
          text,
          style: Theme.of(context).textTheme.bodyMedium,
        )
      ],
    );
  }
}
