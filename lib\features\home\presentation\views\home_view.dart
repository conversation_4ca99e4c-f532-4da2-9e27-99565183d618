import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/cubits/products_cubit/products_cubit.dart';
import '../../../../core/repos/products_repos/products_repo.dart';
import '../../../../core/services/get_it_service.dart';
import '../../domain/repos/categories_repo.dart';
import '../../domain/repos/companies_repo.dart';
import '../manager/categories_cubit/categories_cubit.dart';
import '../manager/companies_cubit/companies_cubit.dart';
import '../manager/search_filter_cubit/search_filter_cubit.dart';
import 'widgets/home_view_body.dart';

class HomeView extends StatelessWidget {
  const HomeView({super.key, this.onTabChange});

  final ValueChanged<int>? onTabChange;

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => ProductsCubit(
            getIt.get<ProductsRepo>(),
          ),
        ),
        BlocProvider(
          create: (context) => CategoriesCubit(
            getIt.get<CategoriesRepo>(),
          ),
        ),
        BlocProvider(
          create: (context) => CompaniesCubit(
            getIt.get<CompaniesRepo>(),
          ),
        ),
        BlocProvider(
          create: (context) => SearchFilterCubit(),
        ),
      ],
      child: HomeViewBody(onTabChange: onTabChange),
    );
  }
}
