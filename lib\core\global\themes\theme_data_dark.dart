import 'package:flutter/material.dart';
import 'app_colors/app_colors_dark.dart';

ThemeData get darkTheme {
  return ThemeData(
    useMaterial3: true,
    brightness: Brightness.dark,
    fontFamily: 'Cairo',

    // Color Scheme
    colorScheme: ColorScheme.fromSeed(
      seedColor: AppColorsDark.primaryColor,
      brightness: Brightness.dark,
      primary: AppColorsDark.primaryColor,
      secondary: AppColorsDark.accentColor,
      surface: AppColorsDark.surfaceColor,
      error: AppColorsDark.errorColor,
      onPrimary: Colors.white,
      onSecondary: Colors.white,
      onSurface: AppColorsDark.textColor,
      onError: Colors.white,
    ),

    // Scaffold
    scaffoldBackgroundColor: AppColorsDark.backgroundColor,

    // AppBar Theme
    appBarTheme: const AppBarTheme(
      backgroundColor: AppColorsDark.surfaceColor,
      foregroundColor: AppColorsDark.textColor,
      elevation: 0,
      centerTitle: true,
      titleTextStyle: TextStyle(
        color: AppColorsDark.textColor,
        fontSize: 18,
        fontWeight: FontWeight.w600,
        fontFamily: 'Cairo',
      ),
      iconTheme: IconThemeData(
        color: AppColorsDark.iconColor,
      ),
      actionsIconTheme: IconThemeData(
        color: AppColorsDark.iconColor,
      ),
    ),

    // Card Theme
    cardTheme: CardThemeData(
      color: AppColorsDark.cardColor,
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
    ),

    // Elevated Button Theme
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColorsDark.primaryColor,
        foregroundColor: Colors.white,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        textStyle: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          fontFamily: 'Cairo',
        ),
      ),
    ),

    // Text Theme
    textTheme: const TextTheme(
      displayLarge: TextStyle(
        color: AppColorsDark.textColor,
        fontSize: 32,
        fontWeight: FontWeight.bold,
        fontFamily: 'Cairo',
      ),
      displayMedium: TextStyle(
        color: AppColorsDark.textColor,
        fontSize: 28,
        fontWeight: FontWeight.bold,
        fontFamily: 'Cairo',
      ),
      displaySmall: TextStyle(
        color: AppColorsDark.textColor,
        fontSize: 24,
        fontWeight: FontWeight.bold,
        fontFamily: 'Cairo',
      ),
      headlineLarge: TextStyle(
        color: AppColorsDark.textColor,
        fontSize: 22,
        fontWeight: FontWeight.w600,
        fontFamily: 'Cairo',
      ),
      headlineMedium: TextStyle(
        color: AppColorsDark.textColor,
        fontSize: 20,
        fontWeight: FontWeight.w600,
        fontFamily: 'Cairo',
      ),
      headlineSmall: TextStyle(
        color: AppColorsDark.textColor,
        fontSize: 18,
        fontWeight: FontWeight.w600,
        fontFamily: 'Cairo',
      ),
      titleLarge: TextStyle(
        color: AppColorsDark.textColor,
        fontSize: 16,
        fontWeight: FontWeight.w600,
        fontFamily: 'Cairo',
      ),
      titleMedium: TextStyle(
        color: AppColorsDark.textColor,
        fontSize: 14,
        fontWeight: FontWeight.w500,
        fontFamily: 'Cairo',
      ),
      titleSmall: TextStyle(
        color: AppColorsDark.lightTextColor,
        fontSize: 12,
        fontWeight: FontWeight.w500,
        fontFamily: 'Cairo',
      ),
      bodyLarge: TextStyle(
        color: AppColorsDark.textColor,
        fontSize: 16,
        fontWeight: FontWeight.normal,
        fontFamily: 'Cairo',
      ),
      bodyMedium: TextStyle(
        color: AppColorsDark.textColor,
        fontSize: 14,
        fontWeight: FontWeight.normal,
        fontFamily: 'Cairo',
      ),
      bodySmall: TextStyle(
        color: AppColorsDark.lightTextColor,
        fontSize: 12,
        fontWeight: FontWeight.normal,
        fontFamily: 'Cairo',
      ),
    ),
    // Icon Button Theme
    iconButtonTheme: IconButtonThemeData(
      style: IconButton.styleFrom(
        foregroundColor: AppColorsDark.iconColor,
      ),
    ),
    // Input Decoration Theme
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: AppColorsDark.surfaceColor,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: AppColorsDark.secondaryColor),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: AppColorsDark.secondaryColor),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide:
            const BorderSide(color: AppColorsDark.primaryColor, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: AppColorsDark.errorColor),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: AppColorsDark.errorColor, width: 2),
      ),
      labelStyle: const TextStyle(
        color: AppColorsDark.lightTextColor,
        fontFamily: 'Cairo',
      ),
      hintStyle: const TextStyle(
        color: AppColorsDark.lightTextColor,
        fontFamily: 'Cairo',
      ),
    ),

    // Bottom Navigation Bar Theme
    bottomNavigationBarTheme: const BottomNavigationBarThemeData(
      backgroundColor: AppColorsDark.surfaceColor,
      selectedItemColor: AppColorsDark.primaryColor,
      unselectedItemColor: AppColorsDark.lightTextColor,
      type: BottomNavigationBarType.fixed,
      elevation: 8,
    ),

    // Divider Theme
    dividerTheme: const DividerThemeData(
      color: AppColorsDark.dividerColor,
      thickness: 1,
    ),

    // Icon Theme
    iconTheme: const IconThemeData(
      color: AppColorsDark.iconColor,
    ),

    // Primary Icon Theme
    primaryIconTheme: const IconThemeData(
      color: Colors.white,
    ),

    // Switch Theme
    switchTheme: SwitchThemeData(
      thumbColor: WidgetStateProperty.resolveWith<Color>((states) {
        if (states.contains(WidgetState.selected)) {
          return AppColorsDark.primaryColor;
        }
        return AppColorsDark.lightTextColor;
      }),
      trackColor: WidgetStateProperty.resolveWith<Color>((states) {
        if (states.contains(WidgetState.selected)) {
          return AppColorsDark.primaryColor.withValues(alpha: 0.5);
        }
        return AppColorsDark.secondaryColor;
      }),
    ),

    // List Tile Theme
    listTileTheme: const ListTileThemeData(
      textColor: AppColorsDark.textColor,
      iconColor: AppColorsDark.iconColor,
      tileColor: AppColorsDark.surfaceColor,
    ),

    // Floating Action Button Theme
    floatingActionButtonTheme: const FloatingActionButtonThemeData(
      backgroundColor: AppColorsDark.primaryColor,
      foregroundColor: Colors.white,
    ),

    // Snack Bar Theme
    snackBarTheme: const SnackBarThemeData(
      backgroundColor: AppColorsDark.cardColor,
      contentTextStyle: TextStyle(
        color: AppColorsDark.textColor,
        fontFamily: 'Cairo',
      ),
    ),

    // Dialog Theme
    dialogTheme: const DialogThemeData(
      backgroundColor: AppColorsDark.surfaceColor,
      titleTextStyle: TextStyle(
        color: AppColorsDark.textColor,
        fontSize: 18,
        fontWeight: FontWeight.w600,
        fontFamily: 'Cairo',
      ),
      contentTextStyle: TextStyle(
        color: AppColorsDark.textColor,
        fontSize: 16,
        fontWeight: FontWeight.normal,
        fontFamily: 'Cairo',
      ),
    ),

    // Chip Theme
    chipTheme: const ChipThemeData(
      backgroundColor: AppColorsDark.cardColor,
      selectedColor: AppColorsDark.primaryColor,
      disabledColor: AppColorsDark.lightTextColor,
      labelStyle: TextStyle(
        color: AppColorsDark.textColor,
        fontFamily: 'Cairo',
      ),
      secondaryLabelStyle: TextStyle(
        color: Colors.white,
        fontFamily: 'Cairo',
      ),
      brightness: Brightness.dark,
    ),

    // Tab Bar Theme
    tabBarTheme: const TabBarThemeData(
      labelColor: AppColorsDark.primaryColor,
      unselectedLabelColor: AppColorsDark.lightTextColor,
      indicatorColor: AppColorsDark.primaryColor,
      labelStyle: TextStyle(
        fontWeight: FontWeight.w600,
        fontFamily: 'Cairo',
      ),
      unselectedLabelStyle: TextStyle(
        fontWeight: FontWeight.normal,
        fontFamily: 'Cairo',
      ),
    ),

    // Progress Indicator Theme
    progressIndicatorTheme: const ProgressIndicatorThemeData(
      color: AppColorsDark.primaryColor,
      linearTrackColor: AppColorsDark.cardColor,
      circularTrackColor: AppColorsDark.cardColor,
    ),

    // Slider Theme
    sliderTheme: SliderThemeData(
      activeTrackColor: AppColorsDark.primaryColor,
      inactiveTrackColor: AppColorsDark.cardColor,
      thumbColor: AppColorsDark.primaryColor,
      overlayColor: AppColorsDark.primaryColor.withValues(alpha: 0.2),
      valueIndicatorColor: AppColorsDark.primaryColor,
      valueIndicatorTextStyle: const TextStyle(
        color: Colors.white,
        fontFamily: 'Cairo',
      ),
    ),

    // Tooltip Theme
    tooltipTheme: const TooltipThemeData(
      decoration: BoxDecoration(
        color: AppColorsDark.cardColor,
        borderRadius: BorderRadius.all(Radius.circular(8)),
      ),
      textStyle: TextStyle(
        color: AppColorsDark.textColor,
        fontFamily: 'Cairo',
      ),
    ),

    // Banner Theme
    bannerTheme: const MaterialBannerThemeData(
      backgroundColor: AppColorsDark.primaryColor,
      contentTextStyle: TextStyle(
        color: Colors.white,
        fontFamily: 'Cairo',
      ),
    ),

    // Navigation Rail Theme
    navigationRailTheme: const NavigationRailThemeData(
      backgroundColor: AppColorsDark.surfaceColor,
      selectedIconTheme: IconThemeData(
        color: AppColorsDark.primaryColor,
      ),
      unselectedIconTheme: IconThemeData(
        color: AppColorsDark.lightTextColor,
      ),
      selectedLabelTextStyle: TextStyle(
        color: AppColorsDark.primaryColor,
        fontFamily: 'Cairo',
      ),
      unselectedLabelTextStyle: TextStyle(
        color: AppColorsDark.lightTextColor,
        fontFamily: 'Cairo',
      ),
    ),
  );
}
