import 'dart:developer';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../core/entities/product_entity.dart';
import '../../../domain/entities/category_entity.dart';
import '../../../domain/repos/categories_repo.dart';

part 'categories_state.dart';

class CategoriesCubit extends Cubit<CategoriesState> {
  CategoriesCubit(this.categoriesRepo) : super(CategoriesInitial());

  final CategoriesRepo categoriesRepo;

  Future<void> getCategories() async {
    emit(CategoriesLoading());

    final result = await categoriesRepo.getCategories();

    result.fold(
      (failure) => emit(CategoriesFailure(failure.message)),
      (categories) => emit(CategoriesSuccess(categories)),
    );
  }

  Future<void> getCategoryProducts({required String category}) async {
    log('🏷️ [CategoriesCubit] Getting products for category: $category');
    emit(CategoryProductsLoading());

    final result = await categoriesRepo.getCategoryProducts(category: category);

    result.fold(
      (failure) {
        log('❌ [CategoriesCubit] Error getting category products: ${failure.message}');
        emit(CategoryProductsFailure(failure.message));
      },
      (products) {
        log('✅ [CategoriesCubit] Got ${products.length} products for category: $category');
        emit(CategoryProductsSuccess(products));
      },
    );
  }
}
