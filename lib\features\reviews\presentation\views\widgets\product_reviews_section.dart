import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../core/utils/responsive_utils.dart';
import '../../../../../core/services/get_it_service.dart';
import '../../../domain/repos/reviews_repo.dart';
import '../../manager/reviews_cubit/reviews_cubit.dart';
import '../reviews_view.dart';
import 'rating_stars_widget.dart';
import 'review_item_widget.dart';

class ProductReviewsSection extends StatelessWidget {
  const ProductReviewsSection({
    super.key,
    required this.productId,
    this.productName,
  });

  final String productId;
  final String? productName;

  @override
  Widget build(BuildContext context) {
    context.initResponsive();

    return BlocProvider(
      create: (context) => ReviewsCubit(getIt<ReviewsRepo>())
        ..getProductReviews(productId: productId)
        ..getProductReviewStats(productId: productId),
      child: Container(
        margin: REdgeInsets.symmetric(horizontal: 16),
        padding: REdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(ResponsiveUtils.radius(12)),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Section Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'التقييمات والمراجعات',
                  style: TextStyle(
                    fontSize: ResponsiveUtils.fontSize(18),
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton(
                  onPressed: () => _navigateToAllReviews(context),
                  child: Text(
                    'عرض الكل',
                    style: TextStyle(
                      fontSize: ResponsiveUtils.fontSize(14),
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                ),
              ],
            ),

            SizedBox(height: ResponsiveUtils.spacing(16)),

            // Review Stats Summary
            BlocBuilder<ReviewsCubit, ReviewsState>(
              builder: (context, state) {
                if (state is ReviewStatsLoaded) {
                  final stats = state.stats;
                  final averageRating = stats['averageRating'] as double;
                  final totalReviews = stats['totalReviews'] as int;

                  return Row(
                    children: [
                      // Average Rating
                      Text(
                        averageRating.toStringAsFixed(1),
                        style: TextStyle(
                          fontSize: ResponsiveUtils.fontSize(24),
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                      SizedBox(width: ResponsiveUtils.spacing(8)),

                      // Stars
                      RatingStarsWidget(
                        rating: averageRating,
                        size: ResponsiveUtils.iconSize(18),
                      ),
                      SizedBox(width: ResponsiveUtils.spacing(8)),

                      // Review Count
                      Text(
                        '($totalReviews تقييم)',
                        style: TextStyle(
                          fontSize: ResponsiveUtils.fontSize(14),
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  );
                }

                return Row(
                  children: [
                    Text(
                      '0.0',
                      style: TextStyle(
                        fontSize: ResponsiveUtils.fontSize(24),
                        fontWeight: FontWeight.bold,
                        color: Colors.grey[400],
                      ),
                    ),
                    SizedBox(width: ResponsiveUtils.spacing(8)),
                    RatingStarsWidget(
                      rating: 0,
                      size: ResponsiveUtils.iconSize(18),
                    ),
                    SizedBox(width: ResponsiveUtils.spacing(8)),
                    Text(
                      '(لا توجد تقييمات)',
                      style: TextStyle(
                        fontSize: ResponsiveUtils.fontSize(14),
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                );
              },
            ),

            SizedBox(height: ResponsiveUtils.spacing(16)),

            // Recent Reviews Preview
            BlocBuilder<ReviewsCubit, ReviewsState>(
              builder: (context, state) {
                if (state is ReviewsLoaded) {
                  if (state.reviews.isEmpty) {
                    return Container(
                      padding: REdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: Colors.grey[50],
                        borderRadius:
                            BorderRadius.circular(ResponsiveUtils.radius(8)),
                      ),
                      child: Column(
                        children: [
                          Icon(
                            Icons.rate_review_outlined,
                            size: ResponsiveUtils.iconSize(32),
                            color: Colors.grey[400],
                          ),
                          SizedBox(height: ResponsiveUtils.spacing(8)),
                          Text(
                            'لا توجد تقييمات بعد',
                            style: TextStyle(
                              fontSize: ResponsiveUtils.fontSize(14),
                              color: Colors.grey[600],
                            ),
                          ),
                          SizedBox(height: ResponsiveUtils.spacing(4)),
                          Text(
                            'كن أول من يقيم هذا المنتج',
                            style: TextStyle(
                              fontSize: ResponsiveUtils.fontSize(12),
                              color: Colors.grey[500],
                            ),
                          ),
                        ],
                      ),
                    );
                  }

                  // Show first 2 reviews as preview
                  final previewReviews = state.reviews.take(2).toList();

                  return Column(
                    children: [
                      for (int i = 0; i < previewReviews.length; i++) ...[
                        ReviewItemWidget(
                          review: previewReviews[i],
                          productId: productId,
                        ),
                        if (i < previewReviews.length - 1)
                          SizedBox(height: ResponsiveUtils.spacing(12)),
                      ],
                      if (state.reviews.length > 2) ...[
                        SizedBox(height: ResponsiveUtils.spacing(16)),
                        Container(
                          width: double.infinity,
                          padding: REdgeInsets.symmetric(vertical: 12),
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            borderRadius: BorderRadius.circular(
                                ResponsiveUtils.radius(8)),
                          ),
                          child: Center(
                            child: Text(
                              'و ${state.reviews.length - 2} تقييمات أخرى',
                              style: TextStyle(
                                fontSize: ResponsiveUtils.fontSize(14),
                                color: Colors.grey[700],
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ],
                  );
                }

                return const SizedBox.shrink();
              },
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToAllReviews(BuildContext context) {
    Navigator.pushNamed(
      context,
      ReviewsView.routeName,
      arguments: {
        'productId': productId,
        'productName': productName,
      },
    );
  }
}
