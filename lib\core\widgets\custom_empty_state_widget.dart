import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:lottie/lottie.dart';
import '../utils/responsive_utils.dart';

class CustomEmptyStateWidget extends StatelessWidget {
  const CustomEmptyStateWidget({
    super.key,
    this.lottieAsset,
    this.title,
    this.subtitle,
    this.actionButton,
    this.animationSize,
    this.titleStyle,
    this.subtitleStyle,
    this.spacing,
    this.backgroundColor,
    this.padding,
  });

  /// Path to the Lottie animation asset
  final String? lottieAsset;

  /// Main title text
  final String? title;

  /// Subtitle/description text
  final String? subtitle;

  /// Optional action button (e.g., "Try Again", "Add Items")
  final Widget? actionButton;

  /// Size of the Lottie animation
  final double? animationSize;

  /// Custom style for title text
  final TextStyle? titleStyle;

  /// Custom style for subtitle text
  final TextStyle? subtitleStyle;

  /// Spacing between elements
  final double? spacing;

  /// Background color of the widget
  final Color? backgroundColor;

  /// Padding around the content
  final EdgeInsetsGeometry? padding;

  @override
  Widget build(BuildContext context) {
    context.initResponsive();

    return Container(
      width: double.infinity,
      color: backgroundColor,
      padding: padding ?? REdgeInsets.all(24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Lottie Animation
          if (lottieAsset != null) ...[
            SizedBox(
              width: animationSize ?? ResponsiveUtils.spacing(200),
              height: animationSize ?? ResponsiveUtils.spacing(200),
              child: Lottie.asset(
                lottieAsset!,
                fit: BoxFit.contain,
                repeat: true,
                animate: true,
                errorBuilder: (context, error, stackTrace) {
                  // Fallback to icon when Lottie fails
                  return Icon(
                    FontAwesomeIcons.boxOpen,
                    size: animationSize ?? ResponsiveUtils.spacing(200),
                    color: Theme.of(context).colorScheme.outline,
                  );
                },
              ),
            ),
            RSizedBox.height(spacing ?? 24),
          ],

          // Title
          if (title != null) ...[
            Text(
              title!,
              style: titleStyle ??
                  Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
              textAlign: TextAlign.center,
            ),
            RSizedBox.height(spacing != null ? spacing! / 2 : 12),
          ],

          // Subtitle
          if (subtitle != null) ...[
            Text(
              subtitle!,
              style: subtitleStyle ??
                  Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                        height: 1.5,
                      ),
              textAlign: TextAlign.center,
            ),
            RSizedBox.height(spacing ?? 24),
          ],

          // Action Button
          if (actionButton != null) actionButton!,
        ],
      ),
    );
  }
}
