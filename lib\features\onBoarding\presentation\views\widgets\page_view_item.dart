import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../../../constants.dart';
import '../../../../../core/utils/responsive_utils.dart';

class PageViewItem extends StatelessWidget {
  const PageViewItem({
    super.key,
    required this.image,
    required this.backgroundImage,
    required this.title,
    required this.subTitle,
    required this.isVisibleSkip,
    this.onTap,
  });
  final String image, backgroundImage;
  final String subTitle;
  final Widget title;
  final bool isVisibleSkip;
  final Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          height: ResponsiveUtils.screenHeight * 0.4,
          child: Stack(
            children: [
              Positioned.fill(
                child: SvgPicture.asset(
                  backgroundImage,
                  fit: BoxFit.fill,
                ),
              ),
              Positioned(
                bottom: ResponsiveUtils.spacing(-80),
                right: 0,
                left: 0,
                child: Image.asset(
                  image,
                ),
              ),
              Visibility(
                visible: isVisibleSkip,
                child: Padding(
                  padding: REdgeInsets.all(16),
                  child: InkWell(
                    onTap: onTap,
                    child: Text(
                      'تخط',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        const RSizedBox.height(50),
        title,
        const RSizedBox.height(24),
        Padding(
          padding: REdgeInsets.symmetric(
            horizontal: kHorizontalPadding16,
          ),
          child: Text(
            subTitle,
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                  height: 1.5,
                ),
          ),
        ),
      ],
    );
  }
}
