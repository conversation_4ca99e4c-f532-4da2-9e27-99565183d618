import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'custom_dialog.dart';

/// Examples of how to use the CustomDialog widget
class DialogExamples {
  
  /// Show a simple confirmation dialog
  static void showSimpleConfirmation(BuildContext context) {
    showCustomDialog(
      context: context,
      title: 'تأكيد العملية',
      content: 'هل أنت متأكد من رغبتك في المتابعة؟',
      icon: FontAwesomeIcons.circleQuestion,
      primaryButtonText: 'نعم',
      secondaryButtonText: 'لا',
      onPrimaryPressed: () {
        Navigator.of(context).pop();
        // Handle confirmation
      },
    );
  }

  /// Show a warning dialog
  static void showWarning(BuildContext context) {
    DialogTypes.showWarningDialog(
      context: context,
      title: 'تحذير',
      content: 'هذا الإجراء قد يؤثر على بياناتك. هل تريد المتابعة؟',
      primaryButtonText: 'متابعة',
      secondaryButtonText: 'إلغاء',
      onConfirm: () {
        Navigator.of(context).pop();
        // Handle warning confirmation
      },
    );
  }

  /// Show an error dialog
  static void showError(BuildContext context) {
    DialogTypes.showErrorDialog(
      context: context,
      title: 'حدث خطأ',
      content: 'عذراً، حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.',
      onConfirm: () {
        Navigator.of(context).pop();
        // Handle error acknowledgment
      },
    );
  }

  /// Show a success dialog
  static void showSuccess(BuildContext context) {
    DialogTypes.showSuccessDialog(
      context: context,
      title: 'تم بنجاح',
      content: 'تمت العملية بنجاح!',
      onConfirm: () {
        Navigator.of(context).pop();
        // Handle success acknowledgment
      },
    );
  }

  /// Show a dangerous action confirmation
  static void showDangerousAction(BuildContext context) {
    DialogTypes.showConfirmationDialog(
      context: context,
      title: 'حذف البيانات',
      content: 'سيتم حذف جميع البيانات نهائياً. لا يمكن التراجع عن هذا الإجراء.',
      confirmButtonText: 'حذف',
      cancelButtonText: 'إلغاء',
      isDanger: true,
      onConfirm: () {
        Navigator.of(context).pop();
        // Handle dangerous action
      },
    );
  }

  /// Show a custom styled dialog
  static void showCustomStyled(BuildContext context) {
    showCustomDialog(
      context: context,
      title: 'إشعار مخصص',
      content: 'هذا مثال على dialog مخصص بألوان مختلفة.',
      icon: FontAwesomeIcons.bell,
      iconColor: Colors.purple,
      primaryButtonColor: Colors.purple,
      primaryButtonText: 'فهمت',
      showSecondaryButton: false,
      onPrimaryPressed: () {
        Navigator.of(context).pop();
      },
    );
  }

  /// Show a dialog without icon
  static void showWithoutIcon(BuildContext context) {
    showCustomDialog(
      context: context,
      title: 'رسالة بسيطة',
      content: 'هذا مثال على dialog بدون أيقونة.',
      primaryButtonText: 'موافق',
      showSecondaryButton: false,
      onPrimaryPressed: () {
        Navigator.of(context).pop();
      },
    );
  }
}

/// Widget to demonstrate all dialog types
class DialogDemoPage extends StatelessWidget {
  const DialogDemoPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Custom Dialog Examples'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            ElevatedButton(
              onPressed: () => DialogExamples.showSimpleConfirmation(context),
              child: const Text('Simple Confirmation'),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: () => DialogExamples.showWarning(context),
              child: const Text('Warning Dialog'),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: () => DialogExamples.showError(context),
              child: const Text('Error Dialog'),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: () => DialogExamples.showSuccess(context),
              child: const Text('Success Dialog'),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: () => DialogExamples.showDangerousAction(context),
              child: const Text('Dangerous Action'),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: () => DialogExamples.showCustomStyled(context),
              child: const Text('Custom Styled'),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: () => DialogExamples.showWithoutIcon(context),
              child: const Text('Without Icon'),
            ),
          ],
        ),
      ),
    );
  }
}
