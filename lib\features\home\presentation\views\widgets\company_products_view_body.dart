import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../constants.dart';
import '../../manager/companies_cubit/companies_cubit.dart';
import '../../manager/search_filter_cubit/search_filter_cubit.dart';
import 'company_products_gridview_blocbuilder.dart';
import 'products_result_header.dart';
import 'search_and_sort_header.dart';

class CompanyProductsViewBody extends StatelessWidget {
  const CompanyProductsViewBody({
    super.key,
    required this.companyName,
  });

  final String companyName;

  @override
  Widget build(BuildContext context) {
    return CustomScrollView(
      slivers: [
        SliverToBoxAdapter(
          child: Column(
            children: [
              const SizedBox(height: kTopPadding16),
              // Search and Sort Header
              BlocBuilder<CompaniesCubit, CompaniesState>(
                builder: (context, companiesState) {
                  List<dynamic>? products;
                  if (companiesState is CompanyProductsSuccess) {
                    products = companiesState.products;
                  }

                  return SearchAndSortHeader(
                    hintText: 'ابحث في منتجات $companyName...',
                    products: products,
                    onSearchChanged: (query) {
                      if (companiesState is CompanyProductsSuccess) {
                        context.read<SearchFilterCubit>().searchProducts(
                              query,
                              companiesState.products,
                            );
                      }
                    },
                  );
                },
              ),
              const SizedBox(height: kVerticalPaddingSmall16),
              // Products Count Header
              BlocBuilder<SearchFilterCubit, SearchFilterState>(
                builder: (context, searchState) {
                  return BlocBuilder<CompaniesCubit, CompaniesState>(
                    builder: (context, companiesState) {
                      int productsLength = 0;

                      if (searchState is ProductsFiltered) {
                        productsLength = searchState.products.length;
                      } else if (companiesState is CompanyProductsSuccess) {
                        productsLength = companiesState.products.length;
                      }

                      return Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: kHorizontalPadding16),
                        child: ProductsResultHeader(
                          productsLength: productsLength,
                        ),
                      );
                    },
                  );
                },
              ),
              const SizedBox(height: kVerticalPaddingSmall16),
            ],
          ),
        ),
        CompanyProductsGridViewBlocBuilder(
          companyName: companyName,
        ),
      ],
    );
  }
}
