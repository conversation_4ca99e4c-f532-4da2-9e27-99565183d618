import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../core/global/themes/app_colors/app_colors_light.dart';
import '../../../../../core/utils/responsive_utils.dart';

import '../../../../../constants.dart';
import '../../../../../core/widgets/custom_button.dart';
import '../../../../../core/widgets/custom_text_field.dart';
import '../../manager/forget_password_cubit/forget_password_cubit.dart';

class ForgetPasswordViewBody extends StatefulWidget {
  const ForgetPasswordViewBody({super.key});

  @override
  State<ForgetPasswordViewBody> createState() => _ForgetPasswordViewBodyState();
}

class _ForgetPasswordViewBodyState extends State<ForgetPasswordViewBody> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  AutovalidateMode _autovalidateMode = AutovalidateMode.disabled;
  String _email = '';

  @override
  Widget build(BuildContext context) {
    context.initResponsive();
    return SingleChildScrollView(
      physics: const BouncingScrollPhysics(),
      child: Padding(
        padding: REdgeInsets.symmetric(horizontal: kHorizontalPadding16),
        child: Form(
          key: _formKey,
          autovalidateMode: _autovalidateMode,
          child: Column(
            children: [
              const RSizedBox.height(24),
              Text(
                'لا تقلق ، ما عليك سوى كتابة البريد الإلكتروني وسنرسل رابط إعادة تعيين كلمة المرور.',
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
              const RSizedBox.height(32),
              CustomTextFormField(
                onSaved: (value) {
                  _email = value!;
                },
                label: 'البريد الإلكتروني',
                keyboardType: TextInputType.emailAddress,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'برجاء ادخال البريد الإلكتروني';
                  }
                  if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                      .hasMatch(value)) {
                    return 'برجاء ادخال بريد إلكتروني صحيح';
                  }
                  return null;
                },
              ),
              const RSizedBox.height(32),
              BlocBuilder<ForgetPasswordCubit, ForgetPasswordState>(
                builder: (context, state) {
                  return CustomButton(
                    onPressed: state is ForgetPasswordLoading
                        ? null
                        : () {
                            if (_formKey.currentState!.validate()) {
                              _formKey.currentState!.save();
                              context
                                  .read<ForgetPasswordCubit>()
                                  .sendPasswordResetEmail(
                                    email: _email.trim(),
                                  );
                            } else {
                              setState(() {
                                _autovalidateMode = AutovalidateMode.always;
                              });
                            }
                          },
                    text: state is ForgetPasswordLoading
                        ? 'جاري الإرسال...'
                        : 'إرسال رابط إعادة التعيين',
                    bgColor: AppColorsLight.primaryColor,
                    textColor: Colors.white,
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
