import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../../auth/domain/entities/user_entity.dart';
import '../../../domain/repos/profile_repo.dart';

part 'profile_state.dart';

class ProfileCubit extends Cubit<ProfileState> {
  ProfileCubit(this._profileRepo) : super(ProfileInitial());

  final ProfileRepo _profileRepo;

  Future<void> getUserProfile() async {
    emit(ProfileLoading());
    final result = await _profileRepo.getUserProfile();
    result.fold(
      (failure) => emit(ProfileError(message: failure.message)),
      (userEntity) => emit(ProfileLoaded(userEntity: userEntity)),
    );
  }

  Future<void> updateUserProfile({required UserEntity userEntity}) async {
    emit(ProfileLoading());
    final result = await _profileRepo.updateUserProfile(userEntity: userEntity);
    result.fold(
      (failure) => emit(ProfileError(message: failure.message)),
      (_) {
        emit(ProfileUpdateSuccess());
        getUserProfile(); // Reload user data
      },
    );
  }

  Future<void> deleteUserAccount() async {
    emit(ProfileLoading());
    final result = await _profileRepo.deleteUserAccount();
    result.fold(
      (failure) => emit(ProfileError(message: failure.message)),
      (_) => emit(ProfileAccountDeleted()),
    );
  }

  Future<void> logout() async {
    emit(ProfileLoading());
    final result = await _profileRepo.logout();
    result.fold(
      (failure) => emit(ProfileError(message: failure.message)),
      (_) => emit(ProfileLoggedOut()),
    );
  }
}
