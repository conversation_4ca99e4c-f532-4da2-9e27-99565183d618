import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../../../../core/utils/responsive_utils.dart';

class ProfileImageDisplay extends StatelessWidget {
  const ProfileImageDisplay({
    super.key,
    required this.imageUrl,
    this.size = 100,
    this.showBorder = true,
    this.onTap,
  });

  final String? imageUrl;
  final double size;
  final bool showBorder;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: ResponsiveUtils.spacing(size),
        height: ResponsiveUtils.spacing(size),
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: showBorder
              ? Border.all(
                  color: Theme.of(context).colorScheme.outlineVariant,
                  width: 2,
                )
              : null,
          color: Theme.of(context).colorScheme.surface,
        ),
        child: ClipOval(
          child: (imageUrl == null || imageUrl!.isEmpty)
              ? _buildPlaceholder(context)
              : CachedNetworkImage(
                  imageUrl: imageUrl!,
                  fit: BoxFit.cover,
                  width: ResponsiveUtils.spacing(size),
                  height: ResponsiveUtils.spacing(size),
                  placeholder: (context, url) => Center(
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                  errorWidget: (context, url, error) {
                    log('Error loading image: $error, URL: $url');
                    return _buildPlaceholder(context);
                  },
                ),
        ),
      ),
    );
  }

  Widget _buildPlaceholder(BuildContext context) {
    return Container(
      width: ResponsiveUtils.spacing(size),
      height: ResponsiveUtils.spacing(size),
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Theme.of(context).colorScheme.primaryContainer,
      ),
      child: Icon(
        FontAwesomeIcons.user,
        size: ResponsiveUtils.spacing(size * 0.5),
        color: Theme.of(context).colorScheme.onPrimaryContainer,
      ),
    );
  }
}
