import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../domain/entities/address_entity.dart';
import '../../../domain/repos/address_book_repo.dart';

part 'address_book_state.dart';

class AddressBookCubit extends Cubit<AddressBookState> {
  AddressBookCubit(this._addressBookRepo) : super(AddressBookInitial());

  final AddressBookRepo _addressBookRepo;

  Future<void> getAddresses({required String userId}) async {
    emit(AddressBookLoading());
    final result = await _addressBookRepo.getAddresses(userId: userId);
    result.fold(
      (failure) => emit(AddressBookError(message: failure.message)),
      (addresses) => emit(AddressBookLoaded(addresses: addresses)),
    );
  }

  Future<void> addAddress({required AddressEntity address}) async {
    emit(AddressBookLoading());
    final result = await _addressBookRepo.addAddress(address: address);
    result.fold(
      (failure) => emit(AddressBookError(message: failure.message)),
      (_) {
        emit(AddressAdded());
        getAddresses(userId: address.userId); // Reload addresses
      },
    );
  }

  Future<void> updateAddress({required AddressEntity address}) async {
    emit(AddressBookLoading());
    final result = await _addressBookRepo.updateAddress(address: address);
    result.fold(
      (failure) => emit(AddressBookError(message: failure.message)),
      (_) {
        emit(AddressUpdated());
        getAddresses(userId: address.userId); // Reload addresses
      },
    );
  }

  Future<void> deleteAddress({required String addressId, required String userId}) async {
    emit(AddressBookLoading());
    final result = await _addressBookRepo.deleteAddress(addressId: addressId);
    result.fold(
      (failure) => emit(AddressBookError(message: failure.message)),
      (_) {
        emit(AddressDeleted());
        getAddresses(userId: userId); // Reload addresses
      },
    );
  }

  Future<void> setDefaultAddress({required String userId, required String addressId}) async {
    final result = await _addressBookRepo.setDefaultAddress(userId: userId, addressId: addressId);
    result.fold(
      (failure) => emit(AddressBookError(message: failure.message)),
      (_) {
        emit(DefaultAddressSet());
        getAddresses(userId: userId); // Reload addresses
      },
    );
  }
}
