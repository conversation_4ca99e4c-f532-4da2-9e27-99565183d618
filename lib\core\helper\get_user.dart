import 'dart:convert';
import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

import '../../constants.dart';
import '../services/shared_preferences_singleton.dart';
import '../../features/auth/data/models/user_model.dart';
import '../../features/auth/domain/entities/user_entity.dart';
import 'data_cleanup_helper.dart';

UserEntity getUser() {
  try {
    final jsonString = SharedPreferencesSingleton.getString(kUserData);

    // Check if jsonString is empty (getString returns '' if not found)
    if (jsonString.isEmpty) {
      log('Error: User data is empty in SharedPreferences');
      throw Exception('User data not found. Please login again.');
    }

    // Validate JSON format
    if (!DataCleanupHelper.isValidJson(jsonString)) {
      log('Error: Invalid JSON format in user data');
      SharedPreferencesSingleton.remove(kUserData);
      throw Exception('Corrupted user data. Please login again.');
    }

    // Try to decode JSON
    final Map<String, dynamic> jsonData = jsonDecode(jsonString);

    // Check if decoded data is valid
    if (jsonData.isEmpty) {
      log('Error: Decoded user data is empty');
      throw Exception('Invalid user data. Please login again.');
    }

    final userEntity = UserModel.fromJson(jsonData);
    log('Successfully loaded user: ${userEntity.name}');
    return userEntity;
  } catch (e) {
    log('Error in getUser(): ${e.toString()}');
    // Clear invalid user data
    SharedPreferencesSingleton.remove(kUserData);
    rethrow;
  }
}

//getuser data fro firebase auth

Future<UserEntity?> getUserFromAuth() async {
  try {
    final user = FirebaseAuth.instance.currentUser;
    if (user != null) {
      final userData = await FirebaseFirestore.instance.collection('users').doc(user.uid).get();
      if (userData.exists) {
        final userEntity = UserModel.fromFirebaseUser(user);
        log('Successfully loaded user from Firebase Auth: ${userEntity.name}');
        return userEntity;
      } else {
        log('Error: User document does not exist in Firestore for UID: ${user.uid}');
        throw Exception('User data not found. Please login again.');
      }
    } else {
      log('Error: No user is currently logged in');
      return null;
    }
  } catch (e) {
    log('Error in getUserFromAuth(): ${e.toString()}');
    rethrow;
  }
}
