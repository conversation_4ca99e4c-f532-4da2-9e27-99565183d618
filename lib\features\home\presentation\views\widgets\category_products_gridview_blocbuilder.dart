import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../core/helper/get_dummy_products.dart';
import '../../../../../core/utils/app_animations_lottie.dart';
import '../../../../../core/widgets/custom_err_widget.dart';
import '../../../../../core/widgets/custom_empty_state_widget.dart';
import '../../manager/categories_cubit/categories_cubit.dart';
import '../../manager/search_filter_cubit/search_filter_cubit.dart';
import 'products_grid_view.dart';
import 'package:skeletonizer/skeletonizer.dart';

class CategoryProductsGridViewBlocBuilder extends StatelessWidget {
  const CategoryProductsGridViewBlocBuilder({
    super.key,
    required this.categoryName,
  });

  final String categoryName;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SearchFilterCubit, SearchFilterState>(
      builder: (context, searchState) {
        // If we have search results, show them
        if (searchState is ProductsFiltered) {
          if (searchState.products.isEmpty) {
            return SliverToBoxAdapter(
              child: CustomEmptyStateWidget(
                lottieAsset: AppAnimationsLottie.emptyBox,
                title: 'لا توجد نتائج',
                subtitle: searchState.searchQuery.isNotEmpty
                    ? 'لم نجد منتجات تطابق "${searchState.searchQuery}" في فئة "$categoryName"'
                    : 'لا توجد منتجات في فئة "$categoryName" حاليا.',
              ),
            );
          }

          return ProductsGridView(
            products: searchState.products,
          );
        }

        // Otherwise, show original category products
        return BlocBuilder<CategoriesCubit, CategoriesState>(
          builder: (context, state) {
            if (state is CategoryProductsLoading) {
              return Skeletonizer.sliver(
                child: ProductsGridView(
                  products: getDummyProducts(),
                ),
              );
            }

            if (state is CategoryProductsSuccess) {
              // Show empty state if no products found
              if (state.products.isEmpty) {
                return SliverToBoxAdapter(
                  child: CustomEmptyStateWidget(
                    lottieAsset: AppAnimationsLottie.emptyBox,
                    title: 'لا توجد منتجات',
                    subtitle:
                        'لا توجد منتجات في فئة "$categoryName" حاليا.\nجرب فئة أخرى أو تحقق لاحق.',
                  ),
                );
              }

              return ProductsGridView(
                products: state.products,
              );
            }

            if (state is CategoryProductsFailure) {
              return SliverToBoxAdapter(
                child: CustomErrorWidget(
                  errorMessage: state.errMessage,
                  onRetry: () {
                    context
                        .read<CategoriesCubit>()
                        .getCategoryProducts(category: categoryName);
                  },
                ),
              );
            }

            // Initial state - show empty or loading
            return const SliverToBoxAdapter(
              child: SizedBox.shrink(),
            );
          },
        );
      },
    );
  }
}
