import 'package:flutter/material.dart';
import '../../features/address_book/domain/entities/address_entity.dart';
import '../../features/address_book/presentation/views/add_address_view.dart';
import '../../features/address_book/presentation/views/edit_address_view.dart';
import '../../features/auth/presentation/views/login_view.dart';
import '../../features/auth/presentation/views/sign_up_view.dart';
import '../../features/home/<USER>/views/best_selling_view.dart';
import '../../features/checkout/presentation/views/cart_view.dart';
import '../../features/checkout/presentation/views/checkout_view.dart';
import '../../features/home/<USER>/entities/cart_entity.dart';
import '../../features/home/<USER>/views/featured_products_view.dart';
import '../../features/home/<USER>/views/main_view.dart';
import '../../features/home/<USER>/views/products_view.dart';
import '../../features/home/<USER>/views/category_products_view.dart';
import '../../features/home/<USER>/views/company_products_view.dart';
import '../../features/home/<USER>/views/categories_view.dart';
import '../../features/home/<USER>/views/companies_view.dart';
import '../../features/orders/presentation/views/orders_view.dart';
import '../../features/orders/presentation/views/order_details_view.dart';
import '../../features/favorites/presentation/views/favorites_view.dart';
import '../../features/account_settings/presentation/views/account_settings_view.dart';
import '../../features/address_book/presentation/views/address_book_view.dart';
import '../../features/profile/presentation/views/profile_view.dart';
import '../../features/profile/presentation/views/update_profile_image_view.dart';
import '../../features/reviews/presentation/views/reviews_view.dart';
import '../../features/reviews/presentation/views/add_review_view.dart';
import '../../features/splash/presentation/views/splash_view.dart';

import '../../features/auth/presentation/views/forget_password_view.dart';
import '../../features/onBoarding/presentation/views/on_boarding_view.dart';

Route<dynamic> onGenerateRoute(RouteSettings settings) {
  switch (settings.name) {
    case SplashView.routeName:
      return MaterialPageRoute(builder: (context) => const SplashView());

    case OnBoardingView.routeName:
      return MaterialPageRoute(builder: (context) => const OnBoardingView());

    case LoginView.routeName:
      return MaterialPageRoute(builder: (context) => const LoginView());

    case SignUpView.routeName:
      return MaterialPageRoute(builder: (context) => const SignUpView());

    case ForgetPasswordView.routeName:
      return MaterialPageRoute(
          builder: (context) => const ForgetPasswordView());
    case MainView.routeName:
      return MaterialPageRoute(builder: (context) => const MainView());

    case BestSellingView.routeName:
      return MaterialPageRoute(builder: (context) => const BestSellingView());

    case ProductsView.routeName:
      return MaterialPageRoute(builder: (context) => const ProductsView());

    case CategoryProductsView.routeName:
      return MaterialPageRoute(
        builder: (context) => CategoryProductsView(
          categoryName: settings.arguments as String,
        ),
      );

    case CompanyProductsView.routeName:
      return MaterialPageRoute(
        builder: (context) => CompanyProductsView(
          companyName: settings.arguments as String,
        ),
      );

    case CheckoutView.routeName:
      return MaterialPageRoute(
        builder: (context) => CheckoutView(
          cartEntity: settings.arguments as CartEntity,
        ),
      );
    case ProfileView.routeName:
      return MaterialPageRoute(builder: (context) => const ProfileView());
    case CartView.routeName:
      return MaterialPageRoute(builder: (context) => const CartView());

    // Profile related routes
    case OrdersView.routeName:
      return MaterialPageRoute(builder: (context) => const OrdersView());
    case OrderDetailsView.routeName:
      return MaterialPageRoute(
        builder: (context) => OrderDetailsView(
          orderId: settings.arguments as String,
        ),
      );
    case FavoritesView.routeName:
      return MaterialPageRoute(builder: (context) => const FavoritesView());
    case AccountSettingsView.routeName:
      return MaterialPageRoute(
          builder: (context) => const AccountSettingsView());
    case AddressBookView.routeName:
      return MaterialPageRoute(builder: (context) => const AddressBookView());
    case UpdateProfileImageView.routeName:
      return MaterialPageRoute(
          builder: (context) => const UpdateProfileImageView());

    case FeaturedProductsView.routeName:
      return MaterialPageRoute(
          builder: (context) => const FeaturedProductsView());

    case CategoriesView.routeName:
      return MaterialPageRoute(builder: (context) => const CategoriesView());

    case CompaniesView.routeName:
      return MaterialPageRoute(builder: (context) => const CompaniesView());
    case AddAddressView.routeName:
      return MaterialPageRoute(builder: (context) => const AddAddressView());
    case EditAddressScreen.routeName:
      return MaterialPageRoute(
        builder: (context) => EditAddressScreen(
          address: settings.arguments as AddressEntity,
        ),
      );

    case ReviewsView.routeName:
      final args = settings.arguments as Map<String, dynamic>;
      return MaterialPageRoute(
        builder: (context) => ReviewsView(
          productId: args['productId'] as String,
          productName: args['productName'] as String?,
        ),
      );

    case AddReviewView.routeName:
      final args = settings.arguments as Map<String, dynamic>;
      return MaterialPageRoute(
        builder: (context) => AddReviewView(
          productId: args['productId'] as String,
          productName: args['productName'] as String?,
        ),
      );

    default:
      return MaterialPageRoute(builder: (context) => const Scaffold());
  }
}
