import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../core/helper/build_custom_snack_bar.dart';
import 'login_view_body.dart';
import '../../../../home/<USER>/views/main_view.dart';

import '../../../../../core/widgets/custom_progress_hud.dart';
import '../../manager/login_cubit/login_cubit.dart';

class LogInViewBodyBlocConsumer extends StatelessWidget {
  const LogInViewBodyBlocConsumer({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<LoginCubit, LoginState>(
      listener: (context, state) {
        if (state is LoginSuccess) {
          showSuccessSnackBar(context, message: 'تم تسجيل الدخول بنجاح');
          Navigator.pushReplacementNamed(context, MainView.routeName);
        }

        if (state is LoginError) {
          showErrorSnackBar(context, message: state.message);
        }
      },
      builder: (context, state) {
        return CustomProgressHud(
          isLoading: state is LoginLoading,
          child: const LoginViewBody(),
        );
      },
    );
  }
}
