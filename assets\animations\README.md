# Lottie Animations for Empty States

This directory contains Lottie animation files used for empty state widgets in the app.

## Required Animation Files

Add the following Lottie JSON files to this directory:

### 1. `empty_cart.json`
- **Usage**: Empty shopping cart state
- **Recommended**: Shopping cart, bag, or basket animation
- **Sources**: 
  - [LottieFiles - Empty Cart](https://lottiefiles.com/search?q=empty%20cart)
  - [LottieFiles - Shopping Bag](https://lottiefiles.com/search?q=shopping%20bag)

### 2. `empty_favorites.json`
- **Usage**: Empty favorites/wishlist state
- **Recommended**: Heart, star, or bookmark animation
- **Sources**:
  - [LottieFiles - Heart](https://lottiefiles.com/search?q=heart)
  - [LottieFiles - Favorite](https://lottiefiles.com/search?q=favorite)

### 3. `empty_orders.json`
- **Usage**: Empty orders history state
- **Recommended**: Package, delivery, or order animation
- **Sources**:
  - [LottieFiles - Package](https://lottiefiles.com/search?q=package)
  - [LottieFiles - Delivery](https://lottiefiles.com/search?q=delivery)

### 4. `empty_search.json`
- **Usage**: No search results state
- **Recommended**: Magnifying glass, search, or "not found" animation
- **Sources**:
  - [LottieFiles - Search](https://lottiefiles.com/search?q=search)
  - [LottieFiles - Not Found](https://lottiefiles.com/search?q=not%20found)

### 5. `empty_products.json`
- **Usage**: No products in category/company state
- **Recommended**: Empty box, no items, or placeholder animation
- **Sources**:
  - [LottieFiles - Empty Box](https://lottiefiles.com/search?q=empty%20box)
  - [LottieFiles - No Items](https://lottiefiles.com/search?q=no%20items)

## How to Add Animations

1. **Download**: Go to [LottieFiles.com](https://lottiefiles.com)
2. **Search**: Find suitable animations using the links above
3. **Download**: Download as JSON format
4. **Rename**: Rename the file to match the required name
5. **Place**: Put the file in this `assets/animations/` directory
6. **Update pubspec.yaml**: Make sure the assets folder is included:

```yaml
flutter:
  assets:
    - assets/animations/
```

## Animation Guidelines

- **Size**: Keep animations under 100KB for better performance
- **Duration**: 2-4 seconds loop duration works best
- **Style**: Choose animations that match your app's design language
- **Color**: Prefer animations that work with both light and dark themes
- **Quality**: Use high-quality animations from verified creators

## Testing Animations

You can preview animations before downloading using:
- [LottieFiles Preview](https://lottiefiles.com)
- [Lottie Editor](https://edit.lottiefiles.com)

## Fallback

If you don't have Lottie files yet, the empty state widgets will still work without animations, showing only text and buttons.
