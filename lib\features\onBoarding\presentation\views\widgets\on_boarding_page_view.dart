import 'package:flutter/material.dart';
import '../../../../../core/utils/app_assets.dart';
import '../../../../auth/presentation/views/login_view.dart';
import 'page_view_item.dart';

import '../../../../../constants.dart';
import '../../../../../core/services/shared_preferences_singleton.dart';

class OnBoardingPageView extends StatelessWidget {
  const OnBoardingPageView({super.key, required this.controller});
  final PageController controller;
  @override
  Widget build(BuildContext context) {
    return PageView(
      controller: controller,
      children: [
        //? First Page
        PageViewItem(
          isVisibleSkip:
              (controller.hasClients ? controller.page!.round() : 0) != 1,
          onTap: () {
            SharedPreferencesSingleton.setBool(kIsOnBoardingViewSeen, true);
            Navigator.pushReplacementNamed(context, LoginView.routeName);
          },
          backgroundImage: Assets.assetsImagesOnboardingBg,
          image: Assets.assetsImagesOnBoarding2Pic1,
          title: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            
            children: [
              Text(
                'مَرْحَبًا بِكَ فِي\t\t',
                style: Theme.of(context).textTheme.titleMedium,
                
              ),
              Text(
                'شَطْبِهَا',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
              ),
            ],
          ),
          subTitle:
              'استعرض آلاف المنتجات اختيارك المثالي يبدأ هنا مع تشكيلة واسعة من السيراميك والأدوات الصحية لتناسب كل الأذواق.',
        ),
        //? Second Page
        PageViewItem(
            isVisibleSkip:
                (controller.hasClients ? controller.page!.round() : 0) != 1,
            backgroundImage: Assets.assetsImagesOnboardingBg,
            image: Assets.assetsImagesOnBoarding2Pic2,
            title: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'ابحث\t\t',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                ),
                Text(
                  'وتسوق',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            subTitle: 'سهولة التسوق والتوصيل \n'
                'تصفح المنتجات، أضف إلى السلة، ودعنا نتولى الباقي!'),
      ],
    );
  }
}
