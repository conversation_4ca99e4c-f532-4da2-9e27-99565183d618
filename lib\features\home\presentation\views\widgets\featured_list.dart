import 'package:flutter/material.dart';
import 'featured_item.dart';

class FeaturedList extends StatelessWidget {
  const FeaturedList({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: List.generate(
          10,
          (index) {
            return const Padding(
              padding: EdgeInsets.only(left: 8.0),
              child: FeaturedItem(),
            );
          },
        ),
      ),
    );
  }
}
