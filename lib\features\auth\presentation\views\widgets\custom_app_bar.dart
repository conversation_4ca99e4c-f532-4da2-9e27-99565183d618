import 'package:flutter/material.dart';

import '../../../../../core/utils/responsive_utils.dart';

AppBar buildCustomAppBar(context,
    {required String title, VoidCallback? onTap}) {
  return AppBar(
    leading: IconButton(
      onPressed: onTap,
      icon: Icon(
        Icons.arrow_back_ios_new,
        size: ResponsiveUtils.iconSize(18),
      ),
    ),
    centerTitle: true,
    title: Text(
      title,
    ),
  );
}
