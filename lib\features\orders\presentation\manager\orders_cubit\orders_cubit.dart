import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../domain/entities/user_order_entity.dart';
import '../../../domain/repos/orders_repo.dart';

part 'orders_state.dart';

class OrdersCubit extends Cubit<OrdersState> {
  OrdersCubit(this._ordersRepo) : super(OrdersInitial());

  final UserOrdersRepo _ordersRepo;

  Future<void> getUserOrders({required String userId}) async {
    emit(OrdersLoading());
    final result = await _ordersRepo.getUserOrders(userId: userId);
    result.fold(
      (failure) => emit(OrdersError(message: failure.message)),
      (orders) => emit(OrdersLoaded(orders: orders)),
    );
  }

  Future<void> getOrderById({required String orderId}) async {
    emit(OrdersLoading());
    final result = await _ordersRepo.getOrderById(orderId: orderId);
    result.fold(
      (failure) => emit(OrdersError(message: failure.message)),
      (order) => emit(OrderDetailsLoaded(order: order)),
    );
  }

  Future<void> cancelOrder(
      {required String orderId, required String userId}) async {
    emit(OrdersLoading());
    final result = await _ordersRepo.cancelOrder(orderId: orderId);
    result.fold(
      (failure) => emit(OrdersError(message: failure.message)),
      (_) {
        emit(OrderCancelled());
        // Reload orders after cancellation
        getUserOrders(userId: userId);
      },
    );
  }

  Future<void> updateOrderStatus({
    required String orderId,
    required String status,
    required String userId,
  }) async {
    emit(OrdersLoading());
    final result = await _ordersRepo.updateOrderStatus(
      orderId: orderId,
      status: status,
    );
    result.fold(
      (failure) => emit(OrdersError(message: failure.message)),
      (_) {
        emit(OrderStatusUpdated());
        // Reload orders after status update
        getUserOrders(userId: userId);
      },
    );
  }
}
