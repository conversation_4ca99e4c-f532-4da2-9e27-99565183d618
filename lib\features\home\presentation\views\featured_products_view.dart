import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/cubits/products_cubit/products_cubit.dart';
import '../../../../core/repos/products_repos/products_repo.dart';
import '../../../../core/services/get_it_service.dart';
import '../../../auth/presentation/views/widgets/custom_app_bar.dart';
import 'widgets/featured_products_view_body.dart';

class FeaturedProductsView extends StatelessWidget {
  const FeaturedProductsView({super.key});
  static const String routeName = '/featured-products';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: buildCustomAppBar(
        context,
        title: 'المنتجات المميزة',
        onTap: () => Navigator.pop(context),
      ),
      body: BlocProvider(
         create: (context) => ProductsCubit(
            getIt.get<ProductsRepo>(),
          )..getFeaturedProducts(),
        child: const FeaturedProductsViewBody()),
    );
  }
}