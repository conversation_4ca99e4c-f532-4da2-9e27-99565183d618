import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../core/utils/responsive_utils.dart';
import '../../../domain/entities/order_entity.dart';

import 'payment_item.dart';

class OrderSummryWidget extends StatelessWidget {
  const OrderSummryWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return PaymentItem(
      tile: 'ملخص الطلب',
      child: Column(
        children: [
          Row(
            children: [
              Text(
                'المجموع الفرعي :',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const Spacer(),
              Text(
                '${context.read<OrderEntity>().cartEntity.getTotalPrice()}جنيه',
                textAlign: TextAlign.right,
                style: Theme.of(context).textTheme.bodyMedium,
              )
            ],
          ),
          const RSizedBox.height(8),
          Row(
            children: [
              Text(
                'التوصيل  :',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const Spacer(),
              Text(
                '30جنية',
                textAlign: TextAlign.right,
                style: Theme.of(context).textTheme.bodyMedium,
              )
            ],
          ),
          const RSizedBox.height(9),
          Divider(
            thickness: ResponsiveUtils.spacing(0.5),
            color: Theme.of(context).dividerColor,
          ),
          const RSizedBox.height(9),
          Row(
            children: [
              Text(
                'الكلي',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const Spacer(),
              Text(
                '${context.read<OrderEntity>().cartEntity.getTotalPrice() + 30} جنيه',
                style: Theme.of(context).textTheme.bodyMedium,
              )
            ],
          ),
        ],
      ),
    );
  }
}
