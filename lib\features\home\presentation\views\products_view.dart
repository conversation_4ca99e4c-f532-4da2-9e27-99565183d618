import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/cubits/products_cubit/products_cubit.dart';
import '../../../../core/helper/build_app_bar.dart';
import '../../../../core/repos/products_repos/products_repo.dart';
import '../../../../core/services/get_it_service.dart';
import '../manager/search_filter_cubit/search_filter_cubit.dart';
import 'widgets/products_view_body.dart';

class ProductsView extends StatelessWidget {
  const ProductsView({super.key});
  static const String routeName = '/products';
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: buildAppBarWithAlarmWidget(
        context,
        title: 'المنتجات',
        isBack: false,
        isNotification: false,
      ),
      body: MultiBlocProvider(
        providers: [
          BlocProvider(
            create: (context) => ProductsCubit(
              getIt.get<ProductsRepo>(),
            ),
          ),
          BlocProvider(
            create: (context) => SearchFilterCubit(),
          ),
        ],
        child: const ProductsViewBody(),
      ),
    );
  }
}
