import 'dart:developer';
import 'package:supabase_flutter/supabase_flutter.dart';

class SupabaseTest {
  static Future<void> testConnection() async {
    try {
      log('Testing Supabase connection...');
      
      // Initialize if not already done
      // if (!Supabase.instance.isInitialized) {
      //   await Supabase.initialize(
      //     url: kSupabaseUrl,
      //     anonKey: kSupabaseKey,
      //   );
      // }
      
      final client = Supabase.instance.client;
      log('Supabase client initialized successfully');
      
      // Test storage access
      try {
        final buckets = await client.storage.listBuckets();
        log('Available buckets: ${buckets.map((b) => b.name).toList()}');
        
        // Check if users_images bucket exists
        final usersBucket = buckets.where((b) => b.name == 'users_images').firstOrNull;
        if (usersBucket != null) {
          log('users_images bucket exists: ${usersBucket.public}');
        } else {
          log('users_images bucket does not exist, creating...');
          await client.storage.createBucket(
            'users_images',
            const BucketOptions(public: true),
          );
          log('users_images bucket created successfully');
        }
      } catch (e) {
        log('Storage test failed: $e');
      }
      
    } catch (e) {
      log('Supabase test failed: $e');
    }
  }
}
