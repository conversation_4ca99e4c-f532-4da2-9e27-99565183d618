import 'package:flutter/material.dart';
import '../../../../../core/utils/responsive_utils.dart';

class InActiveStepItem extends StatelessWidget {
  const InActiveStepItem({super.key, required this.index, required this.text});

  final String index;
  final String text;
  @override
  Widget build(BuildContext context) {
    context.initResponsive();
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        CircleAvatar(
          radius: ResponsiveUtils.spacing(10),
          backgroundColor: Theme.of(context).colorScheme.surface,
          child: Text(
            index,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ),
        const RSizedBox.width(4),
        Text(
          text,
          style: Theme.of(context).textTheme.bodySmall,
        )
      ],
    );
  }
}
