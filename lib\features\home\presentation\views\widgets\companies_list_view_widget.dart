import 'package:flutter/material.dart';

import '../../../../../core/utils/responsive_utils.dart';
import '../../../domain/entities/company_entity.dart';
import '../company_products_view.dart';
import 'company_item.dart';

class CompaniesListViewWidget extends StatelessWidget {
  const CompaniesListViewWidget({
    super.key,
    required this.companies,
  });
  final List<CompanyEntity> companies;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: ResponsiveUtils.spacing(120),
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        itemCount: companies.length,
        separatorBuilder: (context, index) => const RSizedBox.width(12),
        itemBuilder: (context, index) {
          return CompanyItem(
            company: companies[index],
            onTap: () {
              debugPrint('🏢 Company tapped: ${companies[index].name}');
              Navigator.pushNamed(
                context,
                CompanyProductsView.routeName,
                arguments: companies[index].name,
              );
            },
          );
        },
      ),
    );
  }
}
