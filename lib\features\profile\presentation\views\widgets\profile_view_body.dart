import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../../../../core/helper/build_custom_snack_bar.dart';
import '../../../../../core/utils/responsive_utils.dart';
import '../../../../../core/widgets/custom_loading_indicator.dart';
import '../../../../../core/widgets/custom_dialog.dart';
import '../../../../address_book/presentation/views/address_book_view.dart';
import '../../../../auth/presentation/views/login_view.dart';
import '../../manager/profile_cubit/profile_cubit.dart';
import 'profile_header_with_image.dart';
import 'profile_menu_item.dart';
import 'theme_switch_item.dart';

class ProfileViewBody extends StatelessWidget {
  const ProfileViewBody({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<ProfileCubit, ProfileState>(
      listener: (context, state) {
        if (state is ProfileLoggedOut || state is ProfileAccountDeleted) {
          Navigator.pushNamedAndRemoveUntil(
            context,
            LoginView.routeName,
            (route) => false,
          );
        }
        if (state is ProfileError) {
          showErrorSnackBar(context, message: state.message);
        }
      },
      builder: (context, state) {
        if (state is ProfileLoading) {
          return Center(
              child: CustomLoadingIndicator(
            color: Theme.of(context).colorScheme.primary,
          ));
        }

        if (state is ProfileLoaded) {
          return _buildProfileContent(context, state);
        }

        // Initial state - load user profile
        context.read<ProfileCubit>().getUserProfile();
        return Center(
            child: CustomLoadingIndicator(
          color: Theme.of(context).colorScheme.primary,
        ));
      },
    );
  }

  Widget _buildProfileContent(BuildContext context, ProfileLoaded state) {
    return CustomScrollView(
      physics: const BouncingScrollPhysics(),
      slivers: [
        // Profile Header
        const SliverToBoxAdapter(child: ProfileHeaderWithImage()),

        // Theme Switch
        const SliverToBoxAdapter(child: ThemeSwitchItem()),

        // Menu Items
        SliverList(
          delegate: SliverChildListDelegate([
            ProfileMenuItem(
              title: 'طلباتي',
              subtitle: 'حالة الطلب والتاريخ والتتبع',
              icon: FontAwesomeIcons.boxOpen,
              onTap: () {
                Navigator.pushNamed(context, '/orders');
              },
            ),
            ProfileMenuItem(
              title: 'المفضلة',
              subtitle: 'إدارة وعرض القائمة المفضلة',
              icon: FontAwesomeIcons.heart,
              onTap: () {
                Navigator.pushNamed(context, '/favorites');
              },
            ),
            ProfileMenuItem(
              title: 'الحساب',
              subtitle: 'إدارة الاسم وترقيم الهاتف',
              icon: FontAwesomeIcons.user,
              onTap: () {
                Navigator.pushNamed(context, '/account-settings');
              },
            ),
            ProfileMenuItem(
              title: 'دفتر العناوين',
              subtitle: 'إدارة عنوانك',
              icon: FontAwesomeIcons.locationDot,
              onTap: () {
                Navigator.pushNamed(context, AddressBookView.routeName);
              },
            ),
            ProfileMenuItem(
              title: 'حذف الحساب',
              subtitle: 'طلب حذف الحساب نهائياً',
              icon: FontAwesomeIcons.trash,
              onTap: () {
                _showDeleteAccountDialog(context);
              },
            ),
          ]),
        ),

        // Logout Button
        SliverToBoxAdapter(
          child: Padding(
            padding: REdgeInsets.all(16),
            child: OutlinedButton(
              onPressed: () => _showLogoutDialog(context),
              style: OutlinedButton.styleFrom(
                padding: REdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: RBorderRadius.circular(8),
                ),
              ),
              child: Text(
                'الخروج',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _showLogoutDialog(BuildContext context) {
    DialogTypes.showConfirmationDialog(
      context: context,
      title: 'تسجيل الخروج',
      content: 'هل أنت متأكد من رغبتك في تسجيل الخروج من التطبيق؟',
      confirmButtonText: 'تسجيل الخروج',
      cancelButtonText: 'إلغاء',
      isDanger: true,
      onConfirm: () {
        Navigator.pop(context);
        context.read<ProfileCubit>().logout();
      },
      onCancel: () => Navigator.pop(context),
    );
  }

  void _showDeleteAccountDialog(BuildContext context) {
    DialogTypes.showWarningDialog(
      context: context,
      title: 'حذف الحساب',
      content:
          'سيتم حذف حسابك بشكل دائم. لا يمكن التراجع عن هذا الإجراء.\n\nهل أنت متأكد من رغبتك في المتابعة؟',
      primaryButtonText: 'حذف الحساب',
      secondaryButtonText: 'إلغاء',
      onConfirm: () {
        Navigator.pop(context);
        context.read<ProfileCubit>().deleteUserAccount();
      },
      onCancel: () => Navigator.pop(context),
    );
  }
}
