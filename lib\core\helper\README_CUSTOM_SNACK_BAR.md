# Custom SnackBar Helper

A modern, responsive, and theme-aware SnackBar implementation for the Hoot E-Commerce app.

## Features

- 🎨 **Theme Integration**: Automatically adapts to app theme colors
- 📱 **Responsive Design**: Uses ResponsiveUtils for consistent sizing
- 🎯 **Multiple Types**: Success, Error, Warning, and Info variants
- 💫 **Modern UI**: Floating design with rounded corners and shadows
- 🔧 **Customizable**: Support for actions and close buttons
- ⚡ **Easy to Use**: Simple convenience methods

## Usage

### Basic Usage

```dart
import 'package:your_app/core/helper/build_custom_snack_bar.dart';

// Basic info snackbar
buildCustomSnackBar(
  context,
  message: 'تم حفظ البيانات بنجاح',
);
```

### Using Convenience Methods

```dart
// Success message
showSuccessSnackBar(
  context,
  message: 'تم إضافة المنتج إلى السلة',
);

// Error message
showErrorSnackBar(
  context,
  message: 'حدث خطأ أثناء تحميل البيانات',
);

// Warning message
showWarningSnackBar(
  context,
  message: 'يرجى التحقق من اتصال الإنترنت',
);

// Info message
showInfoSnackBar(
  context,
  message: 'تم تحديث التطبيق إلى أحدث إصدار',
);
```

### Advanced Usage

```dart
// With action button
buildCustomSnackBar(
  context,
  message: 'تم حذف المنتج من السلة',
  type: SnackBarType.warning,
  actionLabel: 'تراجع',
  onActionPressed: () {
    // Undo action
  },
);

// Custom duration and no close button
buildCustomSnackBar(
  context,
  message: 'جاري تحميل البيانات...',
  type: SnackBarType.info,
  duration: Duration(seconds: 2),
  showCloseButton: false,
);
```

## SnackBar Types

### Success (SnackBarType.success)
- **Color**: Green
- **Icon**: Check circle
- **Use for**: Successful operations, confirmations

### Error (SnackBarType.error)
- **Color**: Red
- **Icon**: X circle
- **Use for**: Errors, failures, validation issues

### Warning (SnackBarType.warning)
- **Color**: Orange
- **Icon**: Triangle exclamation
- **Use for**: Warnings, cautions, important notices

### Info (SnackBarType.info)
- **Color**: Primary theme color
- **Icon**: Info circle
- **Use for**: General information, tips, updates

## Parameters

### buildCustomSnackBar Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `context` | `BuildContext` | Required | Build context |
| `message` | `String` | Required | Message to display |
| `type` | `SnackBarType` | `info` | Type of snackbar |
| `duration` | `Duration` | `4 seconds` | How long to show |
| `actionLabel` | `String?` | `null` | Action button text |
| `onActionPressed` | `VoidCallback?` | `null` | Action button callback |
| `showCloseButton` | `bool` | `true` | Show close button |

## Design Features

### Visual Elements
- **Floating Behavior**: Appears above content with margins
- **Rounded Corners**: 12px border radius for modern look
- **Elevation**: Subtle shadow for depth
- **Icon Container**: Colored background with transparency
- **Typography**: Uses theme's bodyMedium with medium weight

### Responsive Design
- **Spacing**: Uses ResponsiveUtils for consistent spacing
- **Icons**: Responsive icon sizes
- **Margins**: Adaptive margins based on screen size

### Theme Integration
- **Colors**: Automatically uses theme's color scheme
- **Typography**: Inherits from theme's text styles
- **Dark Mode**: Automatically adapts to dark/light themes

## Migration from Old SnackBar

### Before
```dart
buildCustomSnackBar(
  context,
  message: 'Success message',
  backgroundColor: Colors.green,
  textColor: Colors.white,
);
```

### After
```dart
showSuccessSnackBar(
  context,
  message: 'Success message',
);
```

## Best Practices

1. **Use Appropriate Types**: Choose the right type for your message
2. **Keep Messages Short**: Aim for concise, clear messages
3. **Use Actions Sparingly**: Only add actions when necessary
4. **Consider Duration**: Longer messages need longer durations
5. **Test in Both Themes**: Ensure it looks good in light and dark modes

## Examples in Context

### Cart Operations
```dart
// Adding to cart
showSuccessSnackBar(context, message: 'تم إضافة المنتج إلى السلة');

// Cart is empty
showWarningSnackBar(context, message: 'السلة فارغة');

// Network error
showErrorSnackBar(context, message: 'تعذر الاتصال بالخادم');
```

### User Authentication
```dart
// Login success
showSuccessSnackBar(context, message: 'تم تسجيل الدخول بنجاح');

// Invalid credentials
showErrorSnackBar(context, message: 'بيانات الدخول غير صحيحة');

// Account locked
showWarningSnackBar(context, message: 'تم قفل الحساب مؤقتاً');
```
