import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../core/utils/responsive_utils.dart';
import '../../../../../core/helper/get_user.dart';
import '../../../../../core/helper/build_custom_snack_bar.dart';
import '../../../domain/entities/review_entity.dart';
import '../../manager/reviews_cubit/reviews_cubit.dart';
import 'rating_input_widget.dart';

class AddReviewForm extends StatefulWidget {
  const AddReviewForm({
    super.key,
    required this.productId,
    this.productName,
  });

  final String productId;
  final String? productName;

  @override
  State<AddReviewForm> createState() => _AddReviewFormState();
}

class _AddReviewFormState extends State<AddReviewForm> {
  final _formKey = GlobalKey<FormState>();
  final _reviewController = TextEditingController();
  double _rating = 0;
  bool _isSubmitting = false;

  @override
  void dispose() {
    _reviewController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.initResponsive();

    return BlocListener<ReviewsCubit, ReviewsState>(
      listener: (context, state) {
        if (state is ReviewActionLoading) {
          setState(() {
            _isSubmitting = true;
          });
        } else if (state is ReviewActionSuccess) {
          setState(() {
            _isSubmitting = false;
          });
          showSuccessSnackBar(context, message: state.message);
          Navigator.of(context).pop(true); // Return true to indicate success
        } else if (state is ReviewsError) {
          setState(() {
            _isSubmitting = false;
          });
          showErrorSnackBar(context, message: state.message);
        }
      },
      child: SingleChildScrollView(
        padding: REdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Product Info (if available)
              if (widget.productName != null) ...[
                Container(
                  width: double.infinity,
                  padding: REdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Theme.of(context).cardColor,
                    borderRadius: BorderRadius.circular(ResponsiveUtils.radius(12)),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'تقييم المنتج',
                        style: TextStyle(
                          fontSize: ResponsiveUtils.fontSize(16),
                          fontWeight: FontWeight.w600,
                          color: Colors.grey[700],
                        ),
                      ),
                      SizedBox(height: ResponsiveUtils.spacing(8)),
                      Text(
                        widget.productName!,
                        style: TextStyle(
                          fontSize: ResponsiveUtils.fontSize(18),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: ResponsiveUtils.spacing(24)),
              ],

              // Rating Section
              Text(
                'التقييم *',
                style: TextStyle(
                  fontSize: ResponsiveUtils.fontSize(16),
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: ResponsiveUtils.spacing(12)),
              
              RatingInputWidget(
                initialRating: _rating,
                onRatingChanged: (rating) {
                  setState(() {
                    _rating = rating;
                  });
                },
              ),
              
              if (_rating == 0)
                Padding(
                  padding: REdgeInsets.only(top: 8),
                  child: Text(
                    'يرجى اختيار تقييم',
                    style: TextStyle(
                      fontSize: ResponsiveUtils.fontSize(12),
                      color: Colors.red,
                    ),
                  ),
                ),

              SizedBox(height: ResponsiveUtils.spacing(24)),

              // Review Text Section
              Text(
                'التقييم النصي *',
                style: TextStyle(
                  fontSize: ResponsiveUtils.fontSize(16),
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: ResponsiveUtils.spacing(12)),
              
              TextFormField(
                controller: _reviewController,
                maxLines: 5,
                maxLength: 500,
                decoration: InputDecoration(
                  hintText: 'شاركنا رأيك في هذا المنتج...',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(ResponsiveUtils.radius(12)),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(ResponsiveUtils.radius(12)),
                    borderSide: BorderSide(
                      color: Theme.of(context).primaryColor,
                      width: 2,
                    ),
                  ),
                  contentPadding: REdgeInsets.all(16),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى كتابة تقييمك';
                  }
                  if (value.trim().length < 10) {
                    return 'يجب أن يكون التقييم 10 أحرف على الأقل';
                  }
                  return null;
                },
              ),

              SizedBox(height: ResponsiveUtils.spacing(32)),

              // Submit Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isSubmitting ? null : _submitReview,
                  style: ElevatedButton.styleFrom(
                    padding: REdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(ResponsiveUtils.radius(12)),
                    ),
                    elevation: 2,
                  ),
                  child: _isSubmitting
                      ? SizedBox(
                          height: ResponsiveUtils.height(20),
                          width: ResponsiveUtils.width(20),
                          child: const CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : Text(
                          'إرسال التقييم',
                          style: TextStyle(
                            fontSize: ResponsiveUtils.fontSize(16),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _submitReview() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_rating == 0) {
      showWarningSnackBar(context, message: 'يرجى اختيار تقييم');
      return;
    }

    final user = getUser();
    final reviewId = '${user.uId}_${widget.productId}_${DateTime.now().millisecondsSinceEpoch}';
    
    final review = ReviewEntity(
      id: reviewId,
      userId: user.uId,
      productId: widget.productId,
      userName: user.name,
      userImage: user.imageUrl ?? '',
      rating: _rating,
      reviewText: _reviewController.text.trim(),
      createdAt: DateTime.now(),
      isVerifiedPurchase: false, // TODO: Check if user has purchased this product
    );

    context.read<ReviewsCubit>().addReview(review: review);
  }
}
