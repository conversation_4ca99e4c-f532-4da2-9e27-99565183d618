import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../core/utils/responsive_utils.dart';
import '../../../../../core/helper/get_user.dart';
import '../../../../../core/helper/build_custom_snack_bar.dart';
import '../../../domain/entities/review_entity.dart';
import '../../manager/reviews_cubit/reviews_cubit.dart';
import 'rating_input_widget.dart';

class AddReviewForm extends StatefulWidget {
  const AddReviewForm({
    super.key,
    required this.productId,
    this.productName,
  });

  final String productId;
  final String? productName;

  @override
  State<AddReviewForm> createState() => _AddReviewFormState();
}

class _AddReviewFormState extends State<AddReviewForm> {
  final _formKey = GlobalKey<FormState>();
  final _reviewController = TextEditingController();
  double _rating = 0;
  bool _isSubmitting = false;

  @override
  void dispose() {
    _reviewController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.initResponsive();

    return BlocListener<ReviewsCubit, ReviewsState>(
      listener: (context, state) {
        if (state is ReviewActionLoading) {
          setState(() {
            _isSubmitting = true;
          });
        } else if (state is ReviewActionSuccess) {
          setState(() {
            _isSubmitting = false;
          });
          showSuccessSnackBar(context, message: state.message);
          Navigator.of(context).pop(true); // Return true to indicate success
        } else if (state is ReviewsError) {
          setState(() {
            _isSubmitting = false;
          });
          showErrorSnackBar(context, message: state.message);
        }
      },
      child: SingleChildScrollView(
        padding: REdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Enhanced Product Info (if available)
              if (widget.productName != null) ...[
                Container(
                  width: double.infinity,
                  padding: REdgeInsets.all(20),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Theme.of(context)
                            .colorScheme
                            .primary
                            .withValues(alpha: 0.1),
                        Theme.of(context)
                            .colorScheme
                            .secondary
                            .withValues(alpha: 0.05),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius:
                        BorderRadius.circular(ResponsiveUtils.radius(16)),
                    border: Border.all(
                      color: Theme.of(context)
                          .colorScheme
                          .primary
                          .withValues(alpha: 0.2),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      Container(
                        padding: REdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Theme.of(context)
                              .colorScheme
                              .primary
                              .withValues(alpha: 0.1),
                          borderRadius:
                              BorderRadius.circular(ResponsiveUtils.radius(12)),
                        ),
                        child: Icon(
                          Icons.shopping_bag_rounded,
                          size: ResponsiveUtils.iconSize(24),
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                      SizedBox(width: ResponsiveUtils.spacing(16)),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'تقييم المنتج',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium
                                  ?.copyWith(
                                    fontSize: ResponsiveUtils.fontSize(14),
                                    fontWeight: FontWeight.w500,
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onSurface
                                        .withValues(alpha: 0.7),
                                  ),
                            ),
                            SizedBox(height: ResponsiveUtils.spacing(4)),
                            Text(
                              widget.productName!,
                              style: Theme.of(context)
                                  .textTheme
                                  .titleMedium
                                  ?.copyWith(
                                    fontSize: ResponsiveUtils.fontSize(16),
                                    fontWeight: FontWeight.w700,
                                    color:
                                        Theme.of(context).colorScheme.onSurface,
                                  ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: ResponsiveUtils.spacing(24)),
              ],

              RatingInputWidget(
                initialRating: _rating,
                onRatingChanged: (rating) {
                  setState(() {
                    _rating = rating;
                  });
                },
              ),

              if (_rating == 0)
                Padding(
                  padding: REdgeInsets.only(top: 8),
                  child: Text(
                    'يرجى اختيار تقييم',
                    style: TextStyle(
                      fontSize: ResponsiveUtils.fontSize(12),
                      color: Colors.red,
                    ),
                  ),
                ),

              SizedBox(height: ResponsiveUtils.spacing(24)),

              // Enhanced Review Text Section
              Container(
                padding: REdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Theme.of(context)
                      .colorScheme
                      .surface
                      .withValues(alpha: 0.5),
                  borderRadius:
                      BorderRadius.circular(ResponsiveUtils.radius(16)),
                  border: Border.all(
                    color:
                        Theme.of(context).dividerColor.withValues(alpha: 0.2),
                    width: 1,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.edit_note_rounded,
                          size: ResponsiveUtils.iconSize(20),
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        SizedBox(width: ResponsiveUtils.spacing(8)),
                        Text(
                          'التقييم النصي *',
                          style: Theme.of(context)
                              .textTheme
                              .titleMedium
                              ?.copyWith(
                                fontSize: ResponsiveUtils.fontSize(16),
                                fontWeight: FontWeight.w700,
                                color: Theme.of(context).colorScheme.onSurface,
                              ),
                        ),
                      ],
                    ),
                    SizedBox(height: ResponsiveUtils.spacing(16)),
                    Container(
                      decoration: BoxDecoration(
                        color: Theme.of(context).cardColor,
                        borderRadius:
                            BorderRadius.circular(ResponsiveUtils.radius(12)),
                        boxShadow: [
                          BoxShadow(
                            color: Theme.of(context)
                                .shadowColor
                                .withValues(alpha: 0.05),
                            blurRadius: ResponsiveUtils.radius(8),
                            offset: Offset(0, ResponsiveUtils.height(2)),
                          ),
                        ],
                      ),
                      child: TextFormField(
                        controller: _reviewController,
                        maxLines: 6,
                        maxLength: 500,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              fontSize: ResponsiveUtils.fontSize(14),
                              height: 1.5,
                            ),
                        decoration: InputDecoration(
                          hintText:
                              'شاركنا رأيك في هذا المنتج... ما الذي أعجبك؟ ما الذي يمكن تحسينه؟',
                          hintStyle:
                              Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onSurface
                                        .withValues(alpha: 0.5),
                                    fontSize: ResponsiveUtils.fontSize(14),
                                  ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(
                                ResponsiveUtils.radius(12)),
                            borderSide: BorderSide(
                              color: Theme.of(context)
                                  .dividerColor
                                  .withValues(alpha: 0.3),
                              width: 1,
                            ),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(
                                ResponsiveUtils.radius(12)),
                            borderSide: BorderSide(
                              color: Theme.of(context)
                                  .dividerColor
                                  .withValues(alpha: 0.3),
                              width: 1,
                            ),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(
                                ResponsiveUtils.radius(12)),
                            borderSide: BorderSide(
                              color: Theme.of(context).colorScheme.primary,
                              width: 2,
                            ),
                          ),
                          errorBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(
                                ResponsiveUtils.radius(12)),
                            borderSide: BorderSide(
                              color: Theme.of(context).colorScheme.error,
                              width: 1,
                            ),
                          ),
                          focusedErrorBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(
                                ResponsiveUtils.radius(12)),
                            borderSide: BorderSide(
                              color: Theme.of(context).colorScheme.error,
                              width: 2,
                            ),
                          ),
                          contentPadding: REdgeInsets.all(16),
                          counterStyle:
                              Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onSurface
                                        .withValues(alpha: 0.6),
                                    fontSize: ResponsiveUtils.fontSize(12),
                                  ),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'يرجى كتابة تقييمك';
                          }
                          if (value.trim().length < 10) {
                            return 'يجب أن يكون التقييم 10 أحرف على الأقل';
                          }
                          return null;
                        },
                      ),
                    ),
                  ],
                ),
              ),

              SizedBox(height: ResponsiveUtils.spacing(32)),

              // Submit Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isSubmitting ? null : _submitReview,
                  style: ElevatedButton.styleFrom(
                    padding: REdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius:
                          BorderRadius.circular(ResponsiveUtils.radius(12)),
                    ),
                    elevation: 2,
                  ),
                  child: _isSubmitting
                      ? SizedBox(
                          height: ResponsiveUtils.height(20),
                          width: ResponsiveUtils.width(20),
                          child: const CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : Text(
                          'إرسال التقييم',
                          style: TextStyle(
                            fontSize: ResponsiveUtils.fontSize(16),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _submitReview() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_rating == 0) {
      showWarningSnackBar(context, message: 'يرجى اختيار تقييم');
      return;
    }

    final user = getUser();
    final reviewId =
        '${user.uId}_${widget.productId}_${DateTime.now().millisecondsSinceEpoch}';

    final review = ReviewEntity(
      id: reviewId,
      userId: user.uId,
      productId: widget.productId,
      userName: user.name,
      userImage: user.imageUrl ?? '',
      rating: _rating,
      reviewText: _reviewController.text.trim(),
      createdAt: DateTime.now(),
      isVerifiedPurchase:
          false, // TODO: Check if user has purchased this product
    );

    context.read<ReviewsCubit>().addReview(review: review);
  }
}
