import 'package:flutter/material.dart';

/// A comprehensive responsive utility class that provides better responsive design
/// solutions than ScreenUtil, using MediaQuery and proper responsive principles
class ResponsiveUtils {
  static MediaQueryData? _mediaQueryData;
  static double _screenWidth = 375.0; // Default fallback
  static double _screenHeight = 812.0; // Default fallback
  static double _blockSizeHorizontal = 3.75;
  static double _blockSizeVertical = 8.12;
  static double _safeAreaHorizontal = 0.0;
  static double _safeAreaVertical = 0.0;
  static double _safeBlockHorizontal = 3.75;
  static double _safeBlockVertical = 8.12;
  static bool _isInitialized = false;

  /// Initialize responsive utils with context
  static void init(BuildContext context) {
    _mediaQueryData = MediaQuery.of(context);
    _screenWidth = _mediaQueryData!.size.width;
    _screenHeight = _mediaQueryData!.size.height;
    _blockSizeHorizontal = _screenWidth / 100;
    _blockSizeVertical = _screenHeight / 100;

    _safeAreaHorizontal =
        _mediaQueryData!.padding.left + _mediaQueryData!.padding.right;
    _safeAreaVertical =
        _mediaQueryData!.padding.top + _mediaQueryData!.padding.bottom;
    _safeBlockHorizontal = (_screenWidth - _safeAreaHorizontal) / 100;
    _safeBlockVertical = (_screenHeight - _safeAreaVertical) / 100;
    _isInitialized = true;
  }

  /// Get screen width
  static double get screenWidth => _screenWidth;

  /// Get screen height
  static double get screenHeight => _screenHeight;

  /// Get responsive width based on percentage of screen width
  static double width(double percentage) => _blockSizeHorizontal * percentage;

  /// Get responsive height based on percentage of screen height
  static double height(double percentage) => _blockSizeVertical * percentage;

  /// Get safe responsive width (excluding system UI)
  static double safeWidth(double percentage) =>
      _safeBlockHorizontal * percentage;

  /// Get safe responsive height (excluding system UI)
  static double safeHeight(double percentage) =>
      _safeBlockVertical * percentage;

  /// Get responsive font size based on screen size
  static double fontSize(double size) {
    // Base font size calculation using screen width
    // Assumes 375px as base width (iPhone 6/7/8 width)
    return size * (_screenWidth / 375);
  }

  /// Get responsive padding/margin
  static double spacing(double size) {
    return size * (_screenWidth / 375);
  }

  /// Get responsive border radius
  static double radius(double size) {
    return size * (_screenWidth / 375);
  }

  /// Check if device is tablet (width > 600)
  static bool get isTablet => _screenWidth > 600;

  /// Check if device is mobile
  static bool get isMobile => _screenWidth <= 600;

  /// Check if device is in landscape mode
  static bool get isLandscape => _screenWidth > _screenHeight;

  /// Check if device is in portrait mode
  static bool get isPortrait => _screenHeight > _screenWidth;

  /// Get responsive icon size
  static double iconSize(double size) {
    return size * (_screenWidth / 375);
  }

  /// Get responsive elevation
  static double elevation(double size) {
    return size * (_screenWidth / 375);
  }

  /// Check if ResponsiveUtils is initialized
  static bool get isInitialized => _isInitialized;

  /// Get adaptive grid count based on screen width
  static int getGridCount({int mobileCount = 2, int tabletCount = 3}) {
    return isTablet ? tabletCount : mobileCount;
  }

  /// Get adaptive aspect ratio based on screen size
  static double getAspectRatio(
      {double mobileRatio = 1.0, double tabletRatio = 1.2}) {
    return isTablet ? tabletRatio : mobileRatio;
  }

  /// Get responsive value based on screen width breakpoints
  static T responsive<T>({
    required T mobile,
    T? tablet,
    T? desktop,
  }) {
    if (_screenWidth >= 1200) return desktop ?? tablet ?? mobile;
    if (_screenWidth >= 600) return tablet ?? mobile;
    return mobile;
  }

  /// Get responsive padding for different screen sizes
  static EdgeInsets responsivePadding({
    double mobile = 16.0,
    double? tablet,
    double? desktop,
  }) {
    final value = responsive(
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );
    return EdgeInsets.all(spacing(value));
  }
}

/// Extension on BuildContext for easy access to responsive utils
extension ResponsiveContext on BuildContext {
  /// Initialize responsive utils
  void initResponsive() => ResponsiveUtils.init(this);

  /// Get responsive width
  double rWidth(double percentage) => ResponsiveUtils.width(percentage);

  /// Get responsive height
  double rHeight(double percentage) => ResponsiveUtils.height(percentage);

  /// Get responsive font size
  double rFontSize(double size) => ResponsiveUtils.fontSize(size);

  /// Get responsive spacing
  double rSpacing(double size) => ResponsiveUtils.spacing(size);

  /// Get responsive radius
  double rRadius(double size) => ResponsiveUtils.radius(size);

  /// Get responsive icon size
  double rIconSize(double size) => ResponsiveUtils.iconSize(size);

  /// Check if tablet
  bool get isTablet => ResponsiveUtils.isTablet;

  /// Check if mobile
  bool get isMobile => ResponsiveUtils.isMobile;

  /// Get responsive value
  T responsive<T>({
    required T mobile,
    T? tablet,
    T? desktop,
  }) =>
      ResponsiveUtils.responsive(
        mobile: mobile,
        tablet: tablet,
        desktop: desktop,
      );
}

/// Responsive widget that rebuilds when screen size changes
class ResponsiveBuilder extends StatelessWidget {
  final Widget Function(BuildContext context) builder;

  const ResponsiveBuilder({
    super.key,
    required this.builder,
  });

  @override
  Widget build(BuildContext context) {
    context.initResponsive();
    return builder(context);
  }
}

/// Responsive SizedBox alternatives
class RSizedBox extends StatelessWidget {
  final double? width;
  final double? height;
  final Widget? child;

  const RSizedBox({
    super.key,
    this.width,
    this.height,
    this.child,
  });

  const RSizedBox.width(this.width, {super.key, this.child}) : height = null;

  const RSizedBox.height(this.height, {super.key, this.child}) : width = null;

  @override
  Widget build(BuildContext context) {
    context.initResponsive();
    return SizedBox(
      width: width != null ? ResponsiveUtils.spacing(width!) : null,
      height: height != null ? ResponsiveUtils.spacing(height!) : null,
      child: child,
    );
  }
}

/// Responsive EdgeInsets
class REdgeInsets {
  static EdgeInsets all(double value) {
    return EdgeInsets.all(ResponsiveUtils.spacing(value));
  }

  static EdgeInsets symmetric({double vertical = 0, double horizontal = 0}) {
    return EdgeInsets.symmetric(
      vertical: ResponsiveUtils.spacing(vertical),
      horizontal: ResponsiveUtils.spacing(horizontal),
    );
  }

  static EdgeInsets only({
    double left = 0,
    double top = 0,
    double right = 0,
    double bottom = 0,
  }) {
    return EdgeInsets.only(
      left: ResponsiveUtils.spacing(left),
      top: ResponsiveUtils.spacing(top),
      right: ResponsiveUtils.spacing(right),
      bottom: ResponsiveUtils.spacing(bottom),
    );
  }
}

/// Responsive BorderRadius
class RBorderRadius {
  static BorderRadius circular(double radius) {
    return BorderRadius.circular(ResponsiveUtils.radius(radius));
  }

  static BorderRadius all(Radius radius) {
    return BorderRadius.all(
      Radius.circular(ResponsiveUtils.radius(radius.x)),
    );
  }

  static BorderRadius only({
    double topLeft = 0,
    double topRight = 0,
    double bottomLeft = 0,
    double bottomRight = 0,
  }) {
    return BorderRadius.only(
      topLeft: Radius.circular(ResponsiveUtils.radius(topLeft)),
      topRight: Radius.circular(ResponsiveUtils.radius(topRight)),
      bottomLeft: Radius.circular(ResponsiveUtils.radius(bottomLeft)),
      bottomRight: Radius.circular(ResponsiveUtils.radius(bottomRight)),
    );
  }
}

/// Device type enum
enum DeviceType { mobile, tablet, desktop }

/// Get device type based on screen width
DeviceType getDeviceType(BuildContext context) {
  final width = MediaQuery.of(context).size.width;
  if (width < 600) return DeviceType.mobile;
  if (width < 1200) return DeviceType.tablet;
  return DeviceType.desktop;
}

/// Responsive layout builder
class ResponsiveLayout extends StatelessWidget {
  final Widget mobile;
  final Widget? tablet;
  final Widget? desktop;

  const ResponsiveLayout({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
  });

  @override
  Widget build(BuildContext context) {
    final deviceType = getDeviceType(context);

    switch (deviceType) {
      case DeviceType.mobile:
        return mobile;
      case DeviceType.tablet:
        return tablet ?? mobile;
      case DeviceType.desktop:
        return desktop ?? tablet ?? mobile;
    }
  }
}

/// Responsive wrapper that works with LayoutBuilder
class ResponsiveWrapper extends StatelessWidget {
  final Widget Function(BuildContext context, BoxConstraints constraints)
      builder;

  const ResponsiveWrapper({
    super.key,
    required this.builder,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        context.initResponsive();
        return builder(context, constraints);
      },
    );
  }
}

/// Responsive Grid that adapts to screen size
class ResponsiveGrid extends StatelessWidget {
  final List<Widget> children;
  final int? mobileColumns;
  final int? tabletColumns;
  final int? desktopColumns;
  final double spacing;
  final double runSpacing;

  const ResponsiveGrid({
    super.key,
    required this.children,
    this.mobileColumns = 1,
    this.tabletColumns = 2,
    this.desktopColumns = 3,
    this.spacing = 16.0,
    this.runSpacing = 16.0,
  });

  @override
  Widget build(BuildContext context) {
    context.initResponsive();

    final columns = ResponsiveUtils.responsive(
      mobile: mobileColumns!,
      tablet: tabletColumns,
      desktop: desktopColumns,
    );

    return Wrap(
      spacing: ResponsiveUtils.spacing(spacing),
      runSpacing: ResponsiveUtils.spacing(runSpacing),
      children: children.map((child) {
        return SizedBox(
          width: (ResponsiveUtils.screenWidth -
                  ResponsiveUtils.spacing(spacing) * (columns - 1)) /
              columns,
          child: child,
        );
      }).toList(),
    );
  }
}
