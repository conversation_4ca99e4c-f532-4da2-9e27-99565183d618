import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../../../../core/helper/get_user.dart';
import '../../../../../core/utils/responsive_utils.dart';
import '../../../../../features/auth/domain/entities/user_entity.dart';
import '../update_profile_image_view.dart';
import 'profile_image_display.dart';

class ProfileHeaderWithImage extends StatefulWidget {
  const ProfileHeaderWithImage({super.key});

  @override
  State<ProfileHeaderWithImage> createState() => _ProfileHeaderWithImageState();
}

class _ProfileHeaderWithImageState extends State<ProfileHeaderWithImage> {
  late UserEntity _currentUser;

  @override
  void initState() {
    super.initState();
    _currentUser = getUser();
    log('ProfileHeaderWithImage - Current user: ${_currentUser.name}, Image URL: ${_currentUser.imageUrl}');
  }

  Future<void> _navigateToUpdateImage() async {
    final result = await Navigator.pushNamed(
      context,
      UpdateProfileImageView.routeName,
    );

    // If user updated their image, refresh the current user data
    if (result is UserEntity) {
      setState(() {
        _currentUser = result;
      });
      log('ProfileHeaderWithImage - Updated user after navigation: ${_currentUser.name}, Image URL: ${_currentUser.imageUrl}');
    } else {
      // Even if no result, refresh from local storage
      setState(() {
        _currentUser = getUser();
      });
      log('ProfileHeaderWithImage - Refreshed user from storage: ${_currentUser.name}, Image URL: ${_currentUser.imageUrl}');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: REdgeInsets.all(ResponsiveUtils.spacing(20)),
      child: Column(
        children: [
          // Profile Image with Edit Button
          ProfileImageDisplay(
            imageUrl: _currentUser.imageUrl,
            size: 120,
            showBorder: true,
          ),

          RSizedBox.height(ResponsiveUtils.spacing(16)),

          // User Name
          Text(
            _currentUser.name,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
            textAlign: TextAlign.center,
          ),

          RSizedBox.height(ResponsiveUtils.spacing(8)),

          // User Email
          Text(
            _currentUser.email,
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),

          RSizedBox.height(ResponsiveUtils.spacing(16)),

          // Edit Profile Image Button
          OutlinedButton.icon(
            onPressed: _navigateToUpdateImage,
            icon: Icon(
              FontAwesomeIcons.pencil,
              size: ResponsiveUtils.spacing(16),
            ),
            label: Text(
              'تحديث الصورة الشخصية',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
            ),
            style: OutlinedButton.styleFrom(
              side: BorderSide(
                color: Theme.of(context).colorScheme.outlineVariant,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: RBorderRadius.circular(20),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
