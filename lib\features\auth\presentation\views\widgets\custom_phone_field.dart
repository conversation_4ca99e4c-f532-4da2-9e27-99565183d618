import 'dart:async';

import 'package:flutter/material.dart';
import 'package:intl_phone_field/intl_phone_field.dart';
import 'package:intl_phone_field/phone_number.dart';

class CustomPhoneField extends StatelessWidget {
  final String initialCountryCode;
  final void Function(String completeNumber)? onPhoneChanged;
  final void Function(String countryCode)? onCountryChanged;
  final String? hintText;
  final String? initialValue;
  final FutureOr<String?> Function(PhoneNumber?)? validator;
  final Function(PhoneNumber?)? onSaved;
  const CustomPhoneField({
    super.key,
    this.initialCountryCode = 'EG',
    this.onPhoneChanged,
    this.onCountryChanged,
    this.hintText,
    this.validator,
    this.onSaved,
    this.initialValue,
  });

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl, //? إجبار الاتجاه إلى اليسار لليمين
      child: Intl<PERSON>honeField(
        initialValue: initialValue,
        onSaved: onSaved,
        validator: validator,
        languageCode: 'ar',
        invalidNumberMessage: 'رقم الهاتف غير صحيح',
        flagsButtonPadding: const EdgeInsets.symmetric(horizontal: 8),
        // ignore: deprecated_member_use
        searchText: 'ابحث عن اسم دولتك',
        flagsButtonMargin: const EdgeInsets.symmetric(horizontal: 8),
        dropdownIconPosition: IconPosition.trailing,
        decoration: InputDecoration(
          hintText: hintText,
          labelText: 'رقم الهاتف',
          hintStyle: Theme.of(context).textTheme.bodyMedium,
          fillColor: Theme.of(context).inputDecorationTheme.fillColor,
          filled: true,
          border: buildBorder(),
          enabledBorder: buildBorder(),
          focusedBorder: buildBorder(),
        ),
        textAlign: TextAlign.right,
        initialCountryCode: initialCountryCode,
        onChanged: (phone) {
          if (onPhoneChanged != null) {
            onPhoneChanged!(phone.completeNumber); // Full phone number
          }
        },
        onCountryChanged: (country) {
          if (onCountryChanged != null) {
            onCountryChanged!(country.dialCode); // Selected country code
          }
        },
      ),
    );
  }

  OutlineInputBorder buildBorder() {
    return const OutlineInputBorder(
      borderSide: BorderSide(color: Color(0xFFE0E0E0)),
    );
  }
}
