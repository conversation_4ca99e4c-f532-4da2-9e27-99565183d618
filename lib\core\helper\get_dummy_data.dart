import '../../features/address_book/domain/entities/address_entity.dart';
import '../../features/home/<USER>/entities/category_entity.dart';
import '../../features/home/<USER>/entities/company_entity.dart';

List<CategoryEntity> dummyCategories = [
  CategoryEntity(id: '1', name: 'Electronics', image: 'assets/images/logo.png'),
  CategoryEntity(id: '2', name: 'Clothing', image: 'assets/images/logo.png'),
  CategoryEntity(id: '3', name: 'Home & Kitchen', image: 'assets/images/logo.png'),
  CategoryEntity(id: '4', name: 'Books', image: 'assets/images/logo.png'),
  CategoryEntity(id: '5', name: 'Sports & Outdoors', image: 'assets/images/logo.png'),
  CategoryEntity(id: '6', name: 'Beauty & Personal Care', image: 'assets/images/logo.png'),
  CategoryEntity(id: '7', name: 'Toys & Games', image: 'assets/images/logo.png'),
  CategoryEntity(id: '8', name: 'Health & Wellness', image: 'assets/images/logo.png'),
  CategoryEntity(id: '9', name: 'Automotive', image: 'assets/images/logo.png'),
  CategoryEntity(id: '10', name: 'Grocery & Gourmet Food', image: 'assets/images/logo.png'),
];

List<CompanyEntity> dummyCompanies = [
  CompanyEntity(id: '1', name: 'TechCorp', image: 'assets/images/logo.png'),
  CompanyEntity(id: '2', name: 'FashionHub', image: 'assets/images/logo.png'),
  CompanyEntity(id: '3', name: 'HomeGoods', image: 'assets/images/logo.png'),
  CompanyEntity(id: '4', name: 'BookLovers', image: 'assets/images/logo.png'),
  CompanyEntity(id: '5', name: 'SportsWorld', image: 'assets/images/logo.png'),
  CompanyEntity(
      id: '6', name: 'BeautyBoutique', image: 'assets/images/logo.png'),
  CompanyEntity(id: '7', name: 'ToyBox', image: 'assets/images/logo.png'),
];

List<AddressEntity> dummyAddresses = [
  AddressEntity(
    id: '1',
    userId: '1',
    title: 'Home',
    fullName: 'John Doe',
    phoneNumber: '+**********',
    street: '123 Main St',
    city: 'New York',
    state: 'NY',
    postalCode: '10001',
    country: 'USA',
    isDefault: true,
    createdAt: DateTime.now(),
  ),
  AddressEntity(
    id: '2',
    userId: '1',
    title: 'Office',
    fullName: 'John Doe',
    phoneNumber: '+**********',
    street: '456 Park Ave',
    city: 'San Francisco',
    state: 'CA',
    postalCode: '94104',
    country: 'USA',
    isDefault: false,
    createdAt: DateTime.now(),
  ),
  AddressEntity(
    id: '3',
    userId: '1',
    title: 'Work',
    fullName: 'John Doe',
    phoneNumber: '+**********',
    street: '789 Market St',
    city: 'Los Angeles',
    state: 'CA',
    postalCode: '90001',
    country: 'USA',
    isDefault: false,
    createdAt: DateTime.now(),
  ),
  AddressEntity(
    id: '4',
    userId: '1',
    title: 'Home',
    fullName: 'John Doe',
    phoneNumber: '+**********',
    street: '123 Main St',
    city: 'New York',
    state: 'NY',
    postalCode: '10001',
    country: 'USA',
    isDefault: false,
    createdAt: DateTime.now(),
  ),
];
