import 'package:flutter/material.dart';
import 'responsive_utils.dart';

/// Test widget to verify ResponsiveUtils functionality
class ResponsiveTestWidget extends StatelessWidget {
  const ResponsiveTestWidget({super.key});

  @override
  Widget build(BuildContext context) {
    context.initResponsive();
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Responsive Test'),
      ),
      body: Padding(
        padding: REdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Screen info
            Container(
              padding: REdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: RBorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Screen Info',
                    style: TextStyle(
                      fontSize: context.rFontSize(18),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const RSizedBox.height(8),
                  Text('Width: ${ResponsiveUtils.screenWidth.toStringAsFixed(1)}px'),
                  Text('Height: ${ResponsiveUtils.screenHeight.toStringAsFixed(1)}px'),
                  Text('Device Type: ${context.isTablet ? "Tablet" : "Mobile"}'),
                  Text('Orientation: ${ResponsiveUtils.isLandscape ? "Landscape" : "Portrait"}'),
                ],
              ),
            ),
            
            const RSizedBox.height(20),
            
            // Responsive spacing examples
            Text(
              'Responsive Spacing Examples',
              style: TextStyle(
                fontSize: context.rFontSize(16),
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const RSizedBox.height(16),
            
            // Small box
            Container(
              width: context.rSpacing(100),
              height: context.rSpacing(50),
              decoration: BoxDecoration(
                color: Colors.red.shade300,
                borderRadius: RBorderRadius.circular(8),
              ),
              child: Center(
                child: Text(
                  'Small',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: context.rFontSize(12),
                  ),
                ),
              ),
            ),
            
            const RSizedBox.height(12),
            
            // Medium box
            Container(
              width: context.rSpacing(150),
              height: context.rSpacing(75),
              decoration: BoxDecoration(
                color: Colors.green.shade300,
                borderRadius: RBorderRadius.circular(12),
              ),
              child: Center(
                child: Text(
                  'Medium',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: context.rFontSize(14),
                  ),
                ),
              ),
            ),
            
            const RSizedBox.height(12),
            
            // Large box
            Container(
              width: context.rSpacing(200),
              height: context.rSpacing(100),
              decoration: BoxDecoration(
                color: Colors.purple.shade300,
                borderRadius: RBorderRadius.circular(16),
              ),
              child: Center(
                child: Text(
                  'Large',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: context.rFontSize(16),
                  ),
                ),
              ),
            ),
            
            const RSizedBox.height(20),
            
            // Grid example
            Text(
              'Responsive Grid',
              style: TextStyle(
                fontSize: context.rFontSize(16),
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const RSizedBox.height(12),
            
            Expanded(
              child: GridView.builder(
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: ResponsiveUtils.getGridCount(
                    mobileCount: 2,
                    tabletCount: 3,
                  ),
                  childAspectRatio: ResponsiveUtils.getAspectRatio(
                    mobileRatio: 1.0,
                    tabletRatio: 1.2,
                  ),
                  mainAxisSpacing: ResponsiveUtils.spacing(8),
                  crossAxisSpacing: ResponsiveUtils.spacing(8),
                ),
                itemCount: 12,
                itemBuilder: (context, index) {
                  return Container(
                    decoration: BoxDecoration(
                      color: Colors.orange.shade300,
                      borderRadius: RBorderRadius.circular(8),
                    ),
                    child: Center(
                      child: Text(
                        '${index + 1}',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: context.rFontSize(18),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Helper function to show responsive test
void showResponsiveTest(BuildContext context) {
  Navigator.of(context).push(
    MaterialPageRoute(
      builder: (context) => const ResponsiveTestWidget(),
    ),
  );
}
