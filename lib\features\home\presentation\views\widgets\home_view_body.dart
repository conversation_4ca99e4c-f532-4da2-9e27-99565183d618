import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../constants.dart';
import '../../../../../core/cubits/products_cubit/products_cubit.dart';
import '../../../../../core/utils/responsive_utils.dart';
import '../featured_products_view.dart';
import 'home_search_field.dart';
import 'products_gridview_blocbuilder.dart';
import 'best_selling_header.dart';
import 'custom_home_app_bar.dart';
import 'featured_list.dart';
import 'categories_section.dart';
import 'companies_section.dart';
import '../../manager/categories_cubit/categories_cubit.dart';
import '../../manager/companies_cubit/companies_cubit.dart';

class HomeViewBody extends StatefulWidget {
  const HomeViewBody({
    super.key,
    this.onTabChange,
  });

  final ValueChanged<int>? onTabChange;

  @override
  State<HomeViewBody> createState() => _HomeViewBodyState();
}

class _HomeViewBodyState extends State<HomeViewBody> {
  @override
  void initState() {
    super.initState();
    context.read<ProductsCubit>().getBestSellingProducts();
    context.read<CategoriesCubit>().getCategories();
    context.read<CompaniesCubit>().getCompanies();
  }

  @override
  Widget build(BuildContext context) {
    context.initResponsive();
    return Padding(
      padding: REdgeInsets.symmetric(horizontal: kHorizontalPadding16),
      child: CustomScrollView(
        physics: const BouncingScrollPhysics(),
        slivers: [
          SliverToBoxAdapter(
            child: Column(
              children: [
                const RSizedBox.height(kTopPadding16),
                const CustomHomeAppBar(),
                const RSizedBox.height(kVerticalPaddingSmall16),
                HomeSearchTextField(onTabChange: widget.onTabChange),
                const RSizedBox.height(16),
                InkWell(
                    onTap: () {
                    Navigator.pushNamed(context, FeaturedProductsView.routeName);
                  },
                  child: const FeaturedList()),
                const RSizedBox.height(12),
                const CategoriesSection(),
                const RSizedBox.height(16),
                const CompaniesSection(),
                const RSizedBox.height(16),
                const BestSellingHeader(),
                const RSizedBox.height(8),
              ],
            ),
          ),
          const ProductsGridViewBlocBuilder(),
        ],
      ),
    );
  }
}
