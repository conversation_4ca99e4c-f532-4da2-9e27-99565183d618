import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../core/helper/build_custom_snack_bar.dart';
import '../../manager/cart_cubit/cart_cubit.dart';
import 'main_view_body.dart';

class MainViewBodyBlocListener extends StatelessWidget {
  const MainViewBodyBlocListener({
    super.key,
    required this.currentViewIndex,
    required this.onTabChange,
  });

  final int currentViewIndex;
  final ValueChanged<int> onTabChange;

  @override
  Widget build(BuildContext context) {
    return BlocListener<CartCubit, CartState>(
      listener: (context, state) {
        if (state is CartItemAdded) {
          showSuccessSnackBar(context, message: 'تم الاضافة بنجاح');
        }

        if (state is CartItemRemoved) {
          showSuccessSnackBar(context, message: 'تم الحذف بنجاح');
        }
      },
      child: MainViewB<PERSON>(
        currentViewIndex: currentViewIndex,
        onTabChange: onTabChange,
      ),
    );
  }
}
