import 'dart:developer';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../domain/entities/review_entity.dart';
import '../../../domain/repos/reviews_repo.dart';

part 'reviews_state.dart';

class ReviewsCubit extends Cubit<ReviewsState> {
  final ReviewsRepo reviewsRepo;

  ReviewsCubit(this.reviewsRepo) : super(ReviewsInitial());

  Future<void> getProductReviews({required String productId}) async {
    emit(ReviewsLoading());
    
    final result = await reviewsRepo.getProductReviews(productId: productId);
    
    result.fold(
      (failure) => emit(ReviewsError(message: failure.message)),
      (reviews) => emit(ReviewsLoaded(reviews: reviews)),
    );
  }

  Future<void> getUserReviews({required String userId}) async {
    emit(ReviewsLoading());
    
    final result = await reviewsRepo.getUserReviews(userId: userId);
    
    result.fold(
      (failure) => emit(ReviewsError(message: failure.message)),
      (reviews) => emit(UserReviewsLoaded(reviews: reviews)),
    );
  }

  Future<void> addReview({required ReviewEntity review}) async {
    emit(ReviewActionLoading());
    
    final result = await reviewsRepo.addReview(review: review);
    
    result.fold(
      (failure) => emit(ReviewsError(message: failure.message)),
      (_) {
        emit(const ReviewActionSuccess(message: 'تم إضافة التقييم بنجاح'));
        // Refresh the reviews for the product
        getProductReviews(productId: review.productId);
      },
    );
  }

  Future<void> updateReview({required ReviewEntity review}) async {
    emit(ReviewActionLoading());
    
    final result = await reviewsRepo.updateReview(review: review);
    
    result.fold(
      (failure) => emit(ReviewsError(message: failure.message)),
      (_) {
        emit(const ReviewActionSuccess(message: 'تم تحديث التقييم بنجاح'));
        // Refresh the reviews for the product
        getProductReviews(productId: review.productId);
      },
    );
  }

  Future<void> deleteReview({
    required String reviewId,
    required String userId,
    required String productId,
  }) async {
    emit(ReviewActionLoading());
    
    final result = await reviewsRepo.deleteReview(
      reviewId: reviewId,
      userId: userId,
    );
    
    result.fold(
      (failure) => emit(ReviewsError(message: failure.message)),
      (_) {
        emit(const ReviewActionSuccess(message: 'تم حذف التقييم بنجاح'));
        // Refresh the reviews for the product
        getProductReviews(productId: productId);
      },
    );
  }

  Future<void> checkUserReviewStatus({
    required String userId,
    required String productId,
  }) async {
    final result = await reviewsRepo.hasUserReviewedProduct(
      userId: userId,
      productId: productId,
    );
    
    result.fold(
      (failure) => log('Error checking review status: ${failure.message}'),
      (hasReviewed) => emit(UserReviewStatusChecked(hasReviewed: hasReviewed)),
    );
  }

  Future<void> getProductReviewStats({required String productId}) async {
    final result = await reviewsRepo.getProductReviewStats(productId: productId);
    
    result.fold(
      (failure) => log('Error getting review stats: ${failure.message}'),
      (stats) => emit(ReviewStatsLoaded(stats: stats)),
    );
  }

  void resetState() {
    emit(ReviewsInitial());
  }
}
