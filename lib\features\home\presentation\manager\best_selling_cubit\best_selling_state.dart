import '../../../../../core/entities/product_entity.dart';

abstract class BestSellingState {}

class BestSellingInitial extends BestSellingState {}

class BestSellingLoading extends BestSellingState {}

class BestSellingLoaded extends BestSellingState {
  final List<ProductEntity> products;

  BestSellingLoaded(this.products);
}

class BestSellingError extends BestSellingState {
  final String message;

  BestSellingError(this.message);
}
