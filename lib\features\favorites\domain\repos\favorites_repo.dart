import 'package:dartz/dartz.dart';
import '../../../../core/entities/product_entity.dart';
import '../../../../core/errors/failure.dart';
import '../entities/favorite_product_entity.dart';

abstract class FavoritesRepo {
  Future<Either<Failure, List<FavoriteProductEntity>>> getFavorites({required String userId});
  Future<Either<Failure, void>> addToFavorites({required String userId, required ProductEntity product});
  Future<Either<Failure, void>> removeFromFavorites({required String userId, required String productId});
  Future<Either<Failure, bool>> isFavorite({required String userId, required String productId});
}
