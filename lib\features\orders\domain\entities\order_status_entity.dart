enum OrderStatus {
  pending,
  confirmed,
  processing,
  shipped,
  delivered,
  cancelled,
}

extension OrderStatusExtension on OrderStatus {
  String get displayName {
    switch (this) {
      case OrderStatus.pending:
        return 'في الانتظار';
      case OrderStatus.confirmed:
        return 'مؤكد';
      case OrderStatus.processing:
        return 'قيد التحضير';
      case OrderStatus.shipped:
        return 'تم الشحن';
      case OrderStatus.delivered:
        return 'تم التسليم';
      case OrderStatus.cancelled:
        return 'ملغي';
    }
  }

  String get description {
    switch (this) {
      case OrderStatus.pending:
        return 'طلبك قيد المراجعة';
      case OrderStatus.confirmed:
        return 'تم تأكيد طلبك';
      case OrderStatus.processing:
        return 'جاري تحضير طلبك';
      case OrderStatus.shipped:
        return 'طلبك في الطريق إليك';
      case OrderStatus.delivered:
        return 'تم تسليم طلبك بنجاح';
      case OrderStatus.cancelled:
        return 'تم إلغاء الطلب';
    }
  }
}
