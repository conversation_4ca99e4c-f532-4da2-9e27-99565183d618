import 'dart:developer';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:image_picker/image_picker.dart';
import '../../../../../core/utils/responsive_utils.dart';
import '../../../../../core/helper/build_custom_snack_bar.dart';
import '../../../../../core/utils/image_compression_utils.dart';

class ImagePickerWidget extends StatefulWidget {
  const ImagePickerWidget({
    super.key,
    required this.onImageSelected,
    this.initialImageUrl,
    this.size = 120,
    this.showRemoveButton = true,
  });

  final Function(File?) onImageSelected;
  final String? initialImageUrl;
  final double size;
  final bool showRemoveButton;

  @override
  State<ImagePickerWidget> createState() => _ImagePickerWidgetState();
}

class _ImagePickerWidgetState extends State<ImagePickerWidget> {
  File? _selectedImage;
  final ImagePicker _picker = ImagePicker();
  bool _isLoading = false;

  Future<void> _pickFromGallery() async {
    try {
      setState(() => _isLoading = true);
      log('Picking image from gallery...');

      final XFile? pickedFile = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 80, // Better compression
      );

      if (pickedFile != null) {
        log('Image picked: ${pickedFile.path}');
        final file = File(pickedFile.path);

        if (await file.exists()) {
          // Check file size (max 5MB)
          final fileSize = await file.length();
          if (fileSize > 5 * 1024 * 1024) {
            _showError('حجم الصورة كبير جداً. الحد الأقصى 5 ميجابايت');
            return;
          }

          // Compress image if needed
          File finalFile = file;
          if (await ImageCompressionUtils.needsCompression(imageFile: file)) {
            log('Compressing image...');
            finalFile = await ImageCompressionUtils.compressImage(
              imageFile: file,
              maxWidth: 1024,
              maxHeight: 1024,
              quality: 80,
              maxSizeKB: 500,
            );
            log('Image compressed successfully');
          }

          setState(() {
            _selectedImage = finalFile;
          });
          widget.onImageSelected(_selectedImage);

          final finalSize = await finalFile.length();
          log('Final image size: ${finalSize / 1024} KB');
        } else {
          _showError('الملف غير موجود');
        }
      }
    } catch (e) {
      log('Error picking from gallery: $e');
      _showError('فشل في اختيار الصورة من المعرض');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _pickFromCamera() async {
    try {
      setState(() => _isLoading = true);
      log('Picking image from camera...');

      final XFile? pickedFile = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 80, // Better compression
      );

      if (pickedFile != null) {
        log('Image captured: ${pickedFile.path}');
        final file = File(pickedFile.path);

        if (await file.exists()) {
          // Check file size (max 5MB)
          final fileSize = await file.length();
          if (fileSize > 5 * 1024 * 1024) {
            _showError('حجم الصورة كبير جداً. الحد الأقصى 5 ميجابايت');
            return;
          }

          // Compress image if needed
          File finalFile = file;
          if (await ImageCompressionUtils.needsCompression(imageFile: file)) {
            log('Compressing image...');
            finalFile = await ImageCompressionUtils.compressImage(
              imageFile: file,
              maxWidth: 1024,
              maxHeight: 1024,
              quality: 80,
              maxSizeKB: 500,
            );
            log('Image compressed successfully');
          }

          setState(() {
            _selectedImage = finalFile;
          });
          widget.onImageSelected(_selectedImage);

          final finalSize = await finalFile.length();
          log('Final image size: ${finalSize / 1024} KB');
        } else {
          _showError('الملف غير موجود');
        }
      }
    } catch (e) {
      log('Error picking from camera: $e');
      _showError('فشل في التقاط الصورة');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showError(String message) {
    if (mounted) {
      showErrorSnackBar(context, message: message);
    }
  }

  void _showImageSourceDialog() {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: RBorderRadius.only(
          topLeft: 20,
          topRight: 20,
        ),
      ),
      builder: (context) => Container(
        padding: REdgeInsets.all(ResponsiveUtils.spacing(20)),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'اختر مصدر الصورة',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            RSizedBox.height(ResponsiveUtils.spacing(20)),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildSourceButton(
                  icon: FontAwesomeIcons.camera,
                  label: 'الكاميرا',
                  onTap: () {
                    Navigator.pop(context);
                    _pickFromCamera();
                  },
                ),
                _buildSourceButton(
                  icon: FontAwesomeIcons.image,
                  label: 'المعرض',
                  onTap: () {
                    Navigator.pop(context);
                    _pickFromGallery();
                  },
                ),
              ],
            ),
            RSizedBox.height(ResponsiveUtils.spacing(20)),
          ],
        ),
      ),
    );
  }

  Widget _buildSourceButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: REdgeInsets.all(ResponsiveUtils.spacing(16)),
        decoration: BoxDecoration(
          border: Border.all(
            color: Theme.of(context).colorScheme.outlineVariant,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              size: ResponsiveUtils.spacing(32),
              color: Theme.of(context).colorScheme.primary,
            ),
            RSizedBox.height(ResponsiveUtils.spacing(8)),
            Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageDisplay() {
    if (_selectedImage != null) {
      return ClipOval(
        child: Image.file(
          _selectedImage!,
          fit: BoxFit.cover,
          width: ResponsiveUtils.spacing(widget.size),
          height: ResponsiveUtils.spacing(widget.size),
        ),
      );
    } else if (widget.initialImageUrl != null &&
        widget.initialImageUrl!.isNotEmpty) {
      return ClipOval(
        child: Image.network(
          widget.initialImageUrl!,
          fit: BoxFit.cover,
          width: ResponsiveUtils.spacing(widget.size),
          height: ResponsiveUtils.spacing(widget.size),
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) return child;
            return Center(
              child: CircularProgressIndicator(
                value: loadingProgress.expectedTotalBytes != null
                    ? loadingProgress.cumulativeBytesLoaded /
                        loadingProgress.expectedTotalBytes!
                    : null,
              ),
            );
          },
          errorBuilder: (context, error, stackTrace) {
            return _buildPlaceholder();
          },
        ),
      );
    } else {
      return _buildPlaceholder();
    }
  }

  Widget _buildPlaceholder() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          FontAwesomeIcons.user,
          size: ResponsiveUtils.spacing(32),
          color: Theme.of(context).colorScheme.outline,
        ),
        RSizedBox.height(ResponsiveUtils.spacing(8)),
        Text(
          'إضافة صورة',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.outline,
              ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        GestureDetector(
          onTap: _isLoading ? null : _showImageSourceDialog,
          child: Container(
            width: ResponsiveUtils.spacing(widget.size),
            height: ResponsiveUtils.spacing(widget.size),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: Theme.of(context).colorScheme.outlineVariant,
                width: 2,
              ),
              color: Theme.of(context).colorScheme.surface,
            ),
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _buildImageDisplay(),
          ),
        ),
        if (widget.showRemoveButton &&
            (_selectedImage != null || widget.initialImageUrl != null)) ...[
          RSizedBox.height(ResponsiveUtils.spacing(8)),
          TextButton(
            onPressed: _isLoading
                ? null
                : () {
                    setState(() {
                      _selectedImage = null;
                    });
                    widget.onImageSelected(null);
                  },
            child: Text(
              'إزالة الصورة',
              style: TextStyle(
                color: Theme.of(context).colorScheme.error,
              ),
            ),
          ),
        ],
      ],
    );
  }
}
