import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../core/utils/responsive_utils.dart';
import '../../manager/cart_cubit/cart_cubit.dart';

class CartHeader extends StatelessWidget {
  const CartHeader({super.key});

  @override
  Widget build(BuildContext context) {
    context.initResponsive();

    return Container(
      width: double.infinity,
      padding: REdgeInsets.symmetric(vertical: 20),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
        borderRadius: RBorderRadius.circular(8),
      ),
      child: Center(
        child: context.watch<CartCubit>().cartEntity.cartItems.isNotEmpty
            ? Text(
                'لديك ${context.watch<CartCubit>().cartEntity.cartItems.length} منتجات في السلة',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.w600,
                    ),
              )
            : Text(
                'السلة فارغة',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
              ),
      ),
    );
  }
}
