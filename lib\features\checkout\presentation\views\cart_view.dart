import 'package:flutter/material.dart';
import '../../../auth/presentation/views/widgets/custom_app_bar.dart';

class CartView extends StatelessWidget {
  const CartView({super.key});
  static const String routeName = 'cart_view';
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: buildCustomAppBar(
        context,
        title: 'مراجعة طلبك',
        onTap: () => Navigator.pop(context),
      ),
      body: const Center(
        child: Column(
          children: [
            Text('السلة'),
          ],
        ),
      ),
    );
  }
}
