import 'package:flutter/material.dart';
import '../../../../../core/utils/responsive_utils.dart';
import '../../../../../core/widgets/custom_network_image.dart';
import '../../../domain/entities/company_entity.dart';

class CompanyItem extends StatelessWidget {
  const CompanyItem({
    super.key,
    required this.company,
    required this.onTap,
  });

  final CompanyEntity company;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    context.initResponsive();
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: ResponsiveUtils.spacing(100),
        padding: REdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: RBorderRadius.circular(12),
          border: Border.all(
            color: Theme.of(context).dividerColor.withValues(alpha: 0.3),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Theme.of(context).shadowColor.withValues(alpha: 0.1),
              blurRadius: ResponsiveUtils.spacing(4),
              offset: Offset(0, ResponsiveUtils.spacing(2)),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: ResponsiveUtils.spacing(60),
              height: ResponsiveUtils.spacing(60),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: RBorderRadius.circular(8),
              ),
              child: ClipRRect(
                borderRadius: RBorderRadius.circular(8),
                child: CustomNetworkImage(
                  imageUrl: company.image,
                ),
              ),
            ),
            const RSizedBox.height(8),
            Text(
              company.name,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}
