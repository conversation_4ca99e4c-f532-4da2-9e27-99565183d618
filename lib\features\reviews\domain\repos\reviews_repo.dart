import 'package:dartz/dartz.dart';
import '../../../../core/errors/failure.dart';
import '../entities/review_entity.dart';

abstract class ReviewsRepo {
  /// Get all reviews for a specific product
  Future<Either<Failure, List<ReviewEntity>>> getProductReviews({
    required String productId,
  });

  /// Get reviews by a specific user
  Future<Either<Failure, List<ReviewEntity>>> getUserReviews({
    required String userId,
  });

  /// Add a new review
  Future<Either<Failure, void>> addReview({
    required ReviewEntity review,
  });

  /// Update an existing review
  Future<Either<Failure, void>> updateReview({
    required ReviewEntity review,
  });

  /// Delete a review
  Future<Either<Failure, void>> deleteReview({
    required String reviewId,
    required String userId,
  });

  /// Check if user has already reviewed a product
  Future<Either<Failure, bool>> hasUserReviewedProduct({
    required String userId,
    required String productId,
  });

  /// Get a specific review by ID
  Future<Either<Failure, ReviewEntity>> getReviewById({
    required String reviewId,
  });

  /// Get review statistics for a product (average rating, total reviews)
  Future<Either<Failure, Map<String, dynamic>>> getProductReviewStats({
    required String productId,
  });
}
