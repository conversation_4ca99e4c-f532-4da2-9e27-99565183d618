import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../core/helper/build_custom_snack_bar.dart';
import '../../../../../core/widgets/custom_button.dart';
import '../../../../checkout/presentation/views/checkout_view.dart';
import '../../manager/cart_cubit/cart_cubit.dart';
import '../../manager/cart_item_cubit/cart_item_cubit.dart';

class CustomCartButton extends StatelessWidget {
  const CustomCartButton({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CartItemCubit, CartItemState>(
      builder: (context, state) {
        return CustomButton(
          bgColor: Theme.of(context).colorScheme.primary,
          textColor: Colors.white,
          onPressed: () {
            if (context.read<CartCubit>().cartEntity.cartItems.isNotEmpty) {
              Navigator.pushNamed(
                context,
                CheckoutView.routeName,
                arguments: context.read<CartCubit>().cartEntity,
              );
            } else {
              showWarningSnackBar(
                context,
                message: 'لا يوجد منتجات في السلة',
              );
            }
          },
          text:
              'الدفع  ${context.watch<CartCubit>().cartEntity.getTotalPrice()} جنيه',
        );
      },
    );
  }
}
