part of 'search_filter_cubit.dart';

abstract class SearchFilterState {}

class SearchFilterInitial extends SearchFilterState {}

class ProductsFiltered extends SearchFilterState {
  final List<ProductEntity> products;
  final String searchQuery;
  final SortOption sortOption;

  ProductsFiltered(this.products, this.searchQuery, this.sortOption);
}

class CategoriesFiltered extends SearchFilterState {
  final List<CategoryEntity> categories;
  final String searchQuery;

  CategoriesFiltered(this.categories, this.searchQuery);
}

class CompaniesFiltered extends SearchFilterState {
  final List<CompanyEntity> companies;
  final String searchQuery;

  CompaniesFiltered(this.companies, this.searchQuery);
}
