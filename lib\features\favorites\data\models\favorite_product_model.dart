import '../../../../core/models/product_model.dart';
import '../../domain/entities/favorite_product_entity.dart';

class FavoriteProductModel extends FavoriteProductEntity {
  FavoriteProductModel({
    required super.id,
    required super.userId,
    required super.product,
    required super.addedAt,
  });

  factory FavoriteProductModel.fromJson(Map<String, dynamic> json) {
    return FavoriteProductModel(
      id: json['id'] ?? '',
      userId: json['userId'] ?? '',
      product: ProductModel.fromJson(json['product']).toEntity(),
      addedAt: DateTime.parse(json['addedAt'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'product': ProductModel.fromEntity(product).toJson(),
      'addedAt': addedAt.toIso8601String(),
    };
  }

  factory FavoriteProductModel.fromEntity(FavoriteProductEntity entity) {
    return FavoriteProductModel(
      id: entity.id,
      userId: entity.userId,
      product: entity.product,
      addedAt: entity.addedAt,
    );
  }
}
