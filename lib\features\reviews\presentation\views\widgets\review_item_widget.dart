import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../../../../core/utils/app_assets.dart';
import '../../../../../core/utils/responsive_utils.dart';
import '../../../../../core/helper/get_user.dart';
import '../../../../../core/widgets/custom_dialog.dart';
import '../../../domain/entities/review_entity.dart';
import '../../manager/reviews_cubit/reviews_cubit.dart';
import 'rating_stars_widget.dart';
import 'package:timeago/timeago.dart' as timeago;

class ReviewItemWidget extends StatelessWidget {
  const ReviewItemWidget({
    super.key,
    required this.review,
    required this.productId,
  });

  final ReviewEntity review;
  final String productId;

  @override
  Widget build(BuildContext context) {
    context.initResponsive();
    final currentUser = getUser();
    final isCurrentUserReview = currentUser.uId == review.userId;

    return Container(
      margin: REdgeInsets.only(bottom: 16),
      padding: REdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(ResponsiveUtils.radius(16)),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).shadowColor.withValues(alpha: 0.08),
            blurRadius: ResponsiveUtils.radius(12),
            offset: Offset(0, ResponsiveUtils.height(2)),
          ),
          BoxShadow(
            color: Theme.of(context).shadowColor.withValues(alpha: 0.04),
            blurRadius: ResponsiveUtils.radius(6),
            offset: Offset(0, ResponsiveUtils.height(1)),
          ),
        ],
        border: Border.all(
          color: Theme.of(context).dividerColor.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // User Info and Rating Row
          Row(
            children: [
              // Enhanced User Avatar
              Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: Theme.of(context)
                        .colorScheme
                        .primary
                        .withValues(alpha: 0.2),
                    width: 2,
                  ),
                ),
                child: CircleAvatar(
                  radius: ResponsiveUtils.radius(22),
                  backgroundImage: review.userImage.isNotEmpty
                      ? CachedNetworkImageProvider(review.userImage)
                      : const AssetImage(Assets.assetsImagesAvatarHomeView)
                          as ImageProvider,
                ),
              ),
              SizedBox(width: ResponsiveUtils.spacing(12)),

              // User Name and Date
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            review.userName,
                            style: TextStyle(
                              fontSize: ResponsiveUtils.fontSize(14),
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        if (review.isVerifiedPurchase)
                          Container(
                            padding: REdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.green.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(
                                ResponsiveUtils.radius(8),
                              ),
                              border: Border.all(
                                color: Colors.green.withValues(alpha: 0.3),
                                width: 1,
                              ),
                            ),
                            child: Text(
                              'مشتري موثق',
                              style: TextStyle(
                                fontSize: ResponsiveUtils.fontSize(10),
                                color: Colors.green[700],
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                      ],
                    ),
                    SizedBox(height: ResponsiveUtils.spacing(4)),
                    Text(
                      timeago.format(review.createdAt, locale: 'ar'),
                      style: TextStyle(
                        fontSize: ResponsiveUtils.fontSize(12),
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),

              // Actions Menu (for current user's reviews)
              if (isCurrentUserReview)
                PopupMenuButton<String>(
                  onSelected: (value) => _handleMenuAction(context, value),
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit, size: 18),
                          SizedBox(width: 8),
                          Text('تعديل'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, size: 18, color: Colors.red),
                          SizedBox(width: 8),
                          Text('حذف', style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                  ],
                  child: Icon(
                    Icons.more_vert,
                    color: Colors.grey[600],
                    size: ResponsiveUtils.iconSize(20),
                  ),
                ),
            ],
          ),

          SizedBox(height: ResponsiveUtils.spacing(12)),

          // Rating Stars
          RatingStarsWidget(
            rating: review.rating.toDouble(),
            size: ResponsiveUtils.iconSize(16),
          ),

          SizedBox(height: ResponsiveUtils.spacing(12)),

          // Review Text
          Text(
            review.reviewText,
            style: TextStyle(
              fontSize: ResponsiveUtils.fontSize(14),
              height: 1.5,
              color: Colors.grey[800],
            ),
          ),

          // Updated indicator
          if (review.updatedAt != null)
            Padding(
              padding: REdgeInsets.only(top: 8),
              child: Text(
                'تم التعديل',
                style: TextStyle(
                  fontSize: ResponsiveUtils.fontSize(11),
                  color: Colors.grey[500],
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }

  void _handleMenuAction(BuildContext context, String action) {
    switch (action) {
      case 'edit':
        // TODO: Navigate to edit review screen
        break;
      case 'delete':
        _showDeleteConfirmation(context);
        break;
    }
  }

  void _showDeleteConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => CustomDialog(
        title: 'حذف التقييم',
        content: 'هل أنت متأكد من حذف هذا التقييم؟',
        primaryButtonText: 'حذف',
        secondaryButtonText: 'إلغاء',
        isDanger: true,
        onPrimaryPressed: () {
          Navigator.of(context).pop();
          context.read<ReviewsCubit>().deleteReview(
                reviewId: review.id,
                userId: review.userId,
                productId: productId,
              );
        },
      ),
    );
  }
}
