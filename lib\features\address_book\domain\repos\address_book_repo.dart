import 'package:dartz/dartz.dart';
import '../../../../core/errors/failure.dart';
import '../entities/address_entity.dart';

abstract class AddressBookRepo {
  Future<Either<Failure, List<AddressEntity>>> getAddresses({required String userId});
  Future<Either<Failure, void>> addAddress({required AddressEntity address});
  Future<Either<Failure, void>> updateAddress({required AddressEntity address});
  Future<Either<Failure, void>> deleteAddress({required String addressId});
  Future<Either<Failure, void>> setDefaultAddress({required String userId, required String addressId});
}
