import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../../../../constants.dart';
import '../../../../../core/utils/responsive_utils.dart';
import '../../manager/search_filter_cubit/search_filter_cubit.dart';
import 'sort_bottom_sheet.dart';

class SearchAndSortHeader extends StatefulWidget {
  const SearchAndSortHeader({
    super.key,
    this.onSearchChanged,
    this.showSortButton = true,
    this.hintText = 'ابحث عن...',
    this.products,
  });

  final Function(String)? onSearchChanged;
  final bool showSortButton;
  final String hintText;
  final List<dynamic>? products; // Products for sorting

  @override
  State<SearchAndSortHeader> createState() => _SearchAndSortHeaderState();
}

class _SearchAndSortHeaderState extends State<SearchAndSortHeader> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.initResponsive();

    return Container(
      padding: REdgeInsets.symmetric(horizontal: kHorizontalPadding16),
      child: Row(
        children: [
          // Sort Button
          if (widget.showSortButton) ...[
            Container(
              height: ResponsiveUtils.spacing(48),
              padding: const EdgeInsets.symmetric(horizontal: 12),
              decoration: BoxDecoration(
                border: Border.all(
                  color: Theme.of(context).dividerColor,
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: InkWell(
                onTap: () => _showSortBottomSheet(context),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SvgPicture.asset(
                      'assets/icons/setting_filter.svg',
                      fit: BoxFit.none,
                    ),
                    const RSizedBox(width: 4),
                    Text(
                      'ترتيب',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Theme.of(context).colorScheme.onSurface,
                          ),
                    ),
                  ],
                ),
              ),
            ),
            const RSizedBox(width: 12),
          ],

          // Search Field
          Expanded(
            child: SizedBox(
              height: 48,
              child: TextField(
                controller: _searchController,
                onChanged: (value) {
                  widget.onSearchChanged?.call(value);
                },
                decoration: InputDecoration(
                  hintText: widget.hintText,
                  hintStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                  prefixIcon: Icon(
                    FontAwesomeIcons.magnifyingGlass,
                    size: ResponsiveUtils.iconSize(16),
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                  suffixIcon: _searchController.text.isNotEmpty
                      ? IconButton(
                          onPressed: () {
                            _searchController.clear();
                            widget.onSearchChanged?.call('');
                          },
                          icon: Icon(
                            Icons.clear,
                            color:
                                Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                        )
                      : null,
                  border: OutlineInputBorder(
                    borderRadius: RBorderRadius.circular(8),
                    borderSide: BorderSide(
                      color: Theme.of(context).dividerColor,
                      width: 1,
                    ),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: RBorderRadius.circular(8),
                    borderSide: BorderSide(
                      color: Theme.of(context).dividerColor,
                      width: 1,
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: RBorderRadius.circular(8),
                    borderSide: BorderSide(
                      color: Theme.of(context).colorScheme.primary,
                      width: 2,
                    ),
                  ),
                  contentPadding: REdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
              ),
            ),
          ),

          //? Filter Button (for future use)
          // const SizedBox(width: 12),
          // Container(
          //   height: 48,
          //   width: 48,
          //   decoration: BoxDecoration(
          //     color: Theme.of(context).colorScheme.primary,
          //     borderRadius: BorderRadius.circular(8),
          //   ),
          //   child: IconButton(
          //     onPressed: () {
          //       ScaffoldMessenger.of(context).showSnackBar(
          //         const SnackBar(
          //           content: Text('الفلاتر قريباً'),
          //           duration: Duration(seconds: 1),
          //         ),
          //       );
          //     },
          //     icon: Icon(
          //       Icons.tune,
          //       color: Theme.of(context).colorScheme.onPrimary,
          //     ),
          //   ),
          // ),
        ],
      ),
    );
  }

  void _showSortBottomSheet(BuildContext context) {
    try {
      final searchFilterCubit = context.read<SearchFilterCubit>();

      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (bottomSheetContext) => BlocProvider.value(
          value: searchFilterCubit,
          child: SortBottomSheet(products: widget.products),
        ),
      );
    } catch (e) {
      // If SearchFilterCubit is not available, show a message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('خدمة الترتيب غير متاحة حالياً'),
          duration: Duration(seconds: 2),
        ),
      );
    }
  }
}
