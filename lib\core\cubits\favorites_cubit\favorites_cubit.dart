import 'dart:developer';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import '../../entities/product_entity.dart';
import '../../helper/get_user.dart';
import '../../../features/auth/domain/entities/user_entity.dart';
import '../../../features/favorites/domain/repos/favorites_repo.dart';

part 'favorites_state.dart';

class GlobalFavoritesCubit extends Cubit<GlobalFavoritesState> {
  GlobalFavoritesCubit(this._favoritesRepo) : super(GlobalFavoritesInitial());

  final FavoritesRepo _favoritesRepo;
  final Set<String> _favoriteProductIds = {};

  Set<String> get favoriteProductIds => _favoriteProductIds;

  Future<void> loadFavorites() async {
    try {
      log('Loading favorites...');

      // Try to get user data with error handling
      UserEntity user;
      try {
        user = getUser();
        log('User loaded successfully: ${user.name} (${user.uId})');
      } catch (e) {
        log('Failed to get user data: ${e.toString()}');
        emit(const GlobalFavoritesError(message: 'يرجى تسجيل الدخول مرة أخرى'));
        return;
      }

      final result = await _favoritesRepo.getFavorites(userId: user.uId);
      result.fold(
        (failure) {
          log('Failed to load favorites: ${failure.message}');
          emit(const GlobalFavoritesError(message: 'حدث خطأ في تحميل المفضلة'));
        },
        (favorites) {
          log('Loaded ${favorites.length} favorites');
          _favoriteProductIds.clear();
          _favoriteProductIds.addAll(favorites.map((fav) => fav.product.code));
          emit(GlobalFavoritesLoaded(
              favoriteProductIds: Set.from(_favoriteProductIds)));
        },
      );
    } catch (e) {
      log('Exception loading favorites: ${e.toString()}');
      emit(const GlobalFavoritesError(message: 'حدث خطأ في تحميل المفضلة'));
    }
  }

  Future<void> toggleFavorite(ProductEntity product) async {
    try {
      log('Toggling favorite for product: ${product.name} (${product.code})');
      final user = getUser();
      log('User ID: ${user.uId}');
      final isCurrentlyFavorite = _favoriteProductIds.contains(product.code);
      log('Is currently favorite: $isCurrentlyFavorite');

      if (isCurrentlyFavorite) {
        log('Removing from favorites...');
        // Remove from favorites
        final result = await _favoritesRepo.removeFromFavorites(
          userId: user.uId,
          productId: product.code,
        );
        result.fold(
          (failure) {
            log('Failed to remove favorite: ${failure.message}');
            emit(GlobalFavoritesError(message: failure.message));
          },
          (_) {
            log('Successfully removed from favorites');
            _favoriteProductIds.remove(product.code);
            emit(GlobalFavoritesLoaded(
                favoriteProductIds: Set.from(_favoriteProductIds)));
          },
        );
      } else {
        log('Adding to favorites...');
        // Add to favorites
        final result = await _favoritesRepo.addToFavorites(
          userId: user.uId,
          product: product,
        );
        result.fold(
          (failure) {
            log('Failed to add favorite: ${failure.message}');
            emit(GlobalFavoritesError(message: failure.message));
          },
          (_) {
            log('Successfully added to favorites');
            _favoriteProductIds.add(product.code);
            emit(GlobalFavoritesLoaded(
                favoriteProductIds: Set.from(_favoriteProductIds)));
          },
        );
      }
    } catch (e) {
      log('Exception toggling favorite: ${e.toString()}');
      emit(const GlobalFavoritesError(message: 'حدث خطأ في تحديث المفضلة'));
    }
  }

  bool isFavorite(String productCode) {
    return _favoriteProductIds.contains(productCode);
  }
}
