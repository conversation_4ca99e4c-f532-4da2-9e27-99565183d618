part of 'companies_cubit.dart';

abstract class CompaniesState {}

class CompaniesInitial extends CompaniesState {}

class CompaniesLoading extends CompaniesState {}

class CompaniesSuccess extends CompaniesState {
  final List<CompanyEntity> companies;

  CompaniesSuccess(this.companies);
}

class CompaniesFailure extends CompaniesState {
  final String errMessage;

  CompaniesFailure(this.errMessage);
}

class CompanyProductsLoading extends CompaniesState {}

class CompanyProductsSuccess extends CompaniesState {
  final List<ProductEntity> products;

  CompanyProductsSuccess(this.products);
}

class CompanyProductsFailure extends CompaniesState {
  final String errMessage;

  CompanyProductsFailure(this.errMessage);
}
