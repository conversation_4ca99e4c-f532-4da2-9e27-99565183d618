part of 'address_book_cubit.dart';

abstract class AddressBookState extends Equatable {
  const AddressBookState();

  @override
  List<Object> get props => [];
}

class AddressBookInitial extends AddressBookState {}

class AddressBookLoading extends AddressBookState {}

class AddressBookLoaded extends AddressBookState {
  final List<AddressEntity> addresses;

  const AddressBookLoaded({required this.addresses});

  @override
  List<Object> get props => [addresses];
}

class AddressBookError extends AddressBookState {
  final String message;

  const AddressBookError({required this.message});

  @override
  List<Object> get props => [message];
}

class AddressAdded extends AddressBookState {}

class AddressUpdated extends AddressBookState {}

class AddressDeleted extends AddressBookState {}

class DefaultAddressSet extends AddressBookState {}
