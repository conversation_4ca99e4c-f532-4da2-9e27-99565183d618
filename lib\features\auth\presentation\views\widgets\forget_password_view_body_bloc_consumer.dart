import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../core/helper/build_custom_snack_bar.dart';
import '../../manager/forget_password_cubit/forget_password_cubit.dart';
import 'forget_password_view_body.dart';

class ForgetPasswordViewBodyBlocConsumer extends StatelessWidget {
  const ForgetPasswordViewBodyBlocConsumer({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<ForgetPasswordCubit, ForgetPasswordState>(
      listener: (context, state) {
        if (state is ForgetPasswordSuccess) {
          showSuccessSnackBar(context, message: state.message);
        } else if (state is ForgetPasswordError) {
          showErrorSnackBar(context, message: state.message);
        }
      },
      builder: (context, state) {
        return const ForgetPasswordViewBody();
      },
    );
  }
}
