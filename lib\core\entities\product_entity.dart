import 'dart:io';
import 'package:equatable/equatable.dart';

import '../../features/reviews/domain/entities/review_entity.dart';

class ProductEntity extends Equatable {
  final String name;
  final String description;
  final num price;
  final String category;
  final String company;
  final String? imageUrl;
  final String code;
  final bool isFeatured;
  final double? discount;
  final List<String>? imageUrls;
  final String? thumbnailUrl;
  final List<File>? additionalImages;
  final List<String>? tags;
  final num? stockQuantity;
  final String? createdAt;
  final String? updatedAt;
  final bool? isAvailable;
  final bool? isOnSale;
  final List<String>? specifications;
  final List<String>? colorsAvailable;
  final num originalPrice;
  final List<ReviewEntity> reviews;

  const ProductEntity({
    required this.name,
    required this.description,
    required this.price,
    required this.category,
    required this.company,
    this.imageUrl,
    required this.code,
    required this.isFeatured,
    this.discount,
    this.imageUrls,
    this.thumbnailUrl,
    this.additionalImages,
    this.tags,
    this.stockQuantity,
    this.createdAt,
    this.updatedAt,
    this.isAvailable,
    this.isOnSale,
    this.specifications,
    this.colorsAvailable,
    required this.originalPrice,
    required this.reviews,
  });

  @override
  List<Object?> get props => [code, name];
}
