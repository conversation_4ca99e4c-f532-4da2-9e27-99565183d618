import 'dart:developer';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../core/entities/product_entity.dart';
import '../../../domain/entities/company_entity.dart';
import '../../../domain/repos/companies_repo.dart';

part 'companies_state.dart';

class CompaniesCubit extends Cubit<CompaniesState> {
  CompaniesCubit(this.companiesRepo) : super(CompaniesInitial());

  final CompaniesRepo companiesRepo;

  Future<void> getCompanies() async {
    emit(CompaniesLoading());

    final result = await companiesRepo.getCompanies();

    result.fold(
      (failure) => emit(CompaniesFailure(failure.message)),
      (companies) => emit(CompaniesSuccess(companies)),
    );
  }

  Future<void> getCompanyProducts({required String company}) async {
    log('🏢 [CompaniesCubit] Getting products for company: $company');
    emit(CompanyProductsLoading());

    final result = await companiesRepo.getCompanyProducts(company: company);

    result.fold(
      (failure) {
        log('❌ [CompaniesCubit] Error getting company products: ${failure.message}');
        emit(CompanyProductsFailure(failure.message));
      },
      (products) {
        log('✅ [CompaniesCubit] Got ${products.length} products for company: $company');
        emit(CompanyProductsSuccess(products));
      },
    );
  }
}
