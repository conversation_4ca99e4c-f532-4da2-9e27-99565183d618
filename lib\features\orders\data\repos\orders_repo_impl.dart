import 'dart:developer';
import 'package:dartz/dartz.dart';
import '../../../../core/errors/failure.dart';
import '../../../../core/services/data_service.dart';
import '../../../../core/utils/backend_end_points.dart';
import '../../../../core/utils/order_id_formatter.dart';
import '../models/user_order_model.dart';
import '../../domain/entities/order_status_entity.dart';
import '../../domain/entities/user_order_entity.dart';
import '../../domain/repos/orders_repo.dart';

class UserOrdersRepoImpl implements UserOrdersRepo {
  final DatabaseService databaseService;

  UserOrdersRepoImpl({required this.databaseService});

  @override
  Future<Either<Failure, List<UserOrderEntity>>> getUserOrders(
      {required String userId}) async {
    try {
      log('Fetching orders for user: $userId');

      // Query Orders collection (with capital O)
      final ordersData = await databaseService.getData(
        path: BackendEndPoints.getUserOrders,
        query: {
          'uId': userId,
        },
      );

      if (ordersData == null) {
        log('No orders data found for user');
        return right([]);
      }

      final ordersList = <UserOrderEntity>[];

      if (ordersData is List) {
        log('Found ${ordersData.length} orders for user');
        for (var orderData in ordersData) {
          if (orderData is Map<String, dynamic>) {
            log('Processing order data: ${orderData.keys.toList()}');
            final order = _mapFirebaseOrderToUserOrder(orderData);
            if (order != null) {
              ordersList.add(order);
              log('Successfully mapped order: ${order.id}');
            } else {
              log('Failed to map order data');
            }
          }
        }
      }

      // Sort by date (newest first) in the app
      ordersList.sort((a, b) => b.orderDate.compareTo(a.orderDate));

      log('Returning ${ordersList.length} orders');
      return right(ordersList);
    } catch (e) {
      log('Exception in UserOrdersRepoImpl.getUserOrders: ${e.toString()}');
      return left(ServerFailure('حدث خطأ في جلب الطلبات'));
    }
  }

  @override
  Future<Either<Failure, UserOrderEntity>> getOrderById(
      {required String orderId}) async {
    try {
      log('Fetching order by ID: $orderId');

      // First try to get the order directly by document ID
      try {
        final orderData = await databaseService.getData(
          path: BackendEndPoints.getUserOrders,
          docuementId: orderId,
        );

        if (orderData != null && orderData is Map<String, dynamic>) {
          orderData['documentId'] = orderId; // Ensure document ID is set
          final order = _mapFirebaseOrderToUserOrder(orderData);
          if (order != null) {
            log('Found order by document ID: $orderId');
            return right(order);
          }
        }
      } catch (e) {
        log('Order not found by document ID, searching in all orders...');
      }

      // If not found by document ID, search through all orders
      final ordersData = await databaseService.getData(
        path: BackendEndPoints.getUserOrders,
      );

      if (ordersData is List) {
        for (var orderData in ordersData) {
          if (orderData is Map<String, dynamic>) {
            final currentOrderId = orderData['documentId'] ??
                orderData['id'] ??
                OrderIdFormatter.generateOrderId();

            log('Comparing order IDs: $currentOrderId vs $orderId');

            if (currentOrderId == orderId) {
              final order = _mapFirebaseOrderToUserOrder(orderData);
              if (order != null) {
                log('Found order by search: $orderId');
                return right(order);
              }
            }
          }
        }
      }

      log('Order not found: $orderId');
      return left(ServerFailure('لم يتم العثور على الطلب'));
    } catch (e) {
      log('Exception in UserOrdersRepoImpl.getOrderById: ${e.toString()}');
      return left(ServerFailure('حدث خطأ في جلب تفاصيل الطلب'));
    }
  }

  @override
  Future<Either<Failure, void>> cancelOrder({required String orderId}) async {
    try {
      log('🚫 [UserOrdersRepo] Cancelling order: $orderId');

      // Update only the order status to cancelled in Firebase (preserves all other data)
      await databaseService.updateData(
        path: BackendEndPoints.getUserOrders,
        documentId: orderId,
        data: {
          'status': 'cancelled',
          'cancelledAt':
              DateTime.now().toIso8601String(), // Track when it was cancelled
        },
      );

      log('✅ [UserOrdersRepo] Order $orderId successfully cancelled');
      return right(null);
    } catch (e) {
      log('❌ [UserOrdersRepo] Exception in cancelOrder: ${e.toString()}');
      return left(ServerFailure('حدث خطأ في إلغاء الطلب. حاول مرة أخرى.'));
    }
  }

  @override
  Future<Either<Failure, void>> updateOrderStatus({
    required String orderId,
    required String status,
  }) async {
    try {
      log('📝 [UserOrdersRepo] Updating order status: $orderId to $status');

      final updateData = <String, dynamic>{
        'status': status,
        'updatedAt': DateTime.now().toIso8601String(),
      };

      // Add specific timestamp for delivered status
      if (status == 'delivered') {
        updateData['deliveredAt'] = DateTime.now().toIso8601String();
      }

      await databaseService.updateData(
        path: BackendEndPoints.getUserOrders,
        documentId: orderId,
        data: updateData,
      );

      log('✅ [UserOrdersRepo] Order $orderId status updated to $status');
      return right(null);
    } catch (e) {
      log('❌ [UserOrdersRepo] Exception in updateOrderStatus: ${e.toString()}');
      return left(ServerFailure('حدث خطأ في تحديث حالة الطلب. حاول مرة أخرى.'));
    }
  }

  UserOrderEntity? _mapFirebaseOrderToUserOrder(
      Map<String, dynamic> orderData) {
    try {
      log('Mapping order data: $orderData');

      // Map Firebase order structure to UserOrderEntity
      final orderProducts = orderData['orderProducts'] as List<dynamic>? ?? [];
      final items = <OrderItemEntity>[];

      for (var product in orderProducts) {
        if (product is Map<String, dynamic>) {
          final quantity = product['quantity'] ?? 1;
          final price = (product['price'] ?? 0).toDouble();

          items.add(OrderItemModel(
            productId: product['code'] ?? product['id'] ?? '',
            productName: product['name'] ?? 'منتج غير محدد',
            productImage: product['imageUrl'] ?? product['image'] ?? '',
            quantity: quantity,
            price: price,
            totalPrice: (price * quantity),
          ));
        }
      }

      // Extract shipping address with better formatting
      final shippingModel =
          orderData['shippingAddressModel'] as Map<String, dynamic>? ?? {};
      final address = shippingModel['address'] ?? '';
      final city = shippingModel['city'] ?? '';
      final phone = shippingModel['phone'] ?? '';

      String shippingAddress = '';
      if (address.isNotEmpty) shippingAddress += address;
      if (city.isNotEmpty) {
        if (shippingAddress.isNotEmpty) shippingAddress += ', ';
        shippingAddress += city;
      }
      if (phone.isNotEmpty) {
        if (shippingAddress.isNotEmpty) shippingAddress += '\nالهاتف: ';
        shippingAddress += phone;
      }

      // Generate order ID from document ID or timestamp using OrderIdFormatter
      final String orderId = orderData['documentId'] ??
          orderData['id'] ??
          OrderIdFormatter.generateOrderId();

      return UserOrderModel(
        id: orderId,
        userId: orderData['uId'] ?? orderData['userId'] ?? '',
        items: items,
        totalAmount: (orderData['totalPrice'] ?? 0).toDouble(),
        status: _parseOrderStatus(orderData['status']),
        orderDate: _parseOrderDate(orderData['date']),
        shippingAddress:
            shippingAddress.isEmpty ? 'عنوان غير محدد' : shippingAddress,
        paymentMethod: _parsePaymentMethod(orderData['paymentMethod']),
      );
    } catch (e) {
      log('Error mapping Firebase order: ${e.toString()}');
      log('Order data that failed: $orderData');
      return null;
    }
  }

  OrderStatus _parseOrderStatus(String? status) {
    switch (status?.toLowerCase()) {
      case 'pending':
      case 'في الانتظار':
        return OrderStatus.pending;
      case 'confirmed':
      case 'مؤكد':
        return OrderStatus.confirmed;
      case 'processing':
      case 'قيد التحضير':
        return OrderStatus.processing;
      case 'shipped':
      case 'تم الشحن':
        return OrderStatus.shipped;
      case 'delivered':
      case 'تم التسليم':
        return OrderStatus.delivered;
      case 'cancelled':
      case 'ملغي':
        return OrderStatus.cancelled;
      default:
        return OrderStatus.pending;
    }
  }

  DateTime _parseOrderDate(Object? date) {
    try {
      if (date == null) return DateTime.now();

      if (date is String) {
        // Try parsing ISO string first
        try {
          return DateTime.parse(date);
        } catch (e) {
          // If that fails, try other common formats
          log('Failed to parse date string: $date');
          return DateTime.now();
        }
      }

      if (date is int) {
        // Assume it's milliseconds since epoch
        return DateTime.fromMillisecondsSinceEpoch(date);
      }

      // If it's a Firestore Timestamp, handle it
      if (date.runtimeType.toString().contains('Timestamp')) {
        return (date as dynamic).toDate();
      }

      return DateTime.now();
    } catch (e) {
      log('Error parsing order date: ${e.toString()}');
      return DateTime.now();
    }
  }

  String _parsePaymentMethod(Object? paymentMethod) {
    if (paymentMethod == null) return 'الدفع عند الاستلام';

    final method = paymentMethod.toString().toLowerCase();
    switch (method) {
      case 'cash':
      case 'الدفع عند الاستلام':
        return 'الدفع عند الاستلام';
      case 'card':
      case 'credit_card':
      case 'بطاقة ائتمان':
        return 'بطاقة ائتمان';
      case 'paypal':
        return 'PayPal';
      case 'bank_transfer':
      case 'تحويل بنكي':
        return 'تحويل بنكي';
      default:
        return paymentMethod.toString();
    }
  }
}
