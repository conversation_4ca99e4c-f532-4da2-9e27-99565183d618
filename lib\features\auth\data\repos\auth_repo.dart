import 'dart:io';
import 'package:dartz/dartz.dart';
import '../../domain/entities/user_entity.dart';

import '../../../../core/errors/failure.dart';

abstract class AuthRepo {
  Future<Either<Failure, UserEntity>> createUserWithEmailAndPassword(
    String name,
    String phone,
    String email,
    String password, {
    File? profileImage,
  });

  Future addUserData({required UserEntity userEntity});
  Future saveUserData({required UserEntity userEntity});

  Future<UserEntity> getUserData({required String uId});

  Future<Either<Failure, UserEntity>> signInWithEmailAndPassword(
    String email,
    String password,
  );

  Future<Either<Failure, UserEntity>> signInWithGoogle();

  Future<Either<Failure, UserEntity>> signInWithFacebook();

  Future<Either<Failure, UserEntity>> signInWithApple();

  Future<Either<Failure, void>> sendPasswordResetEmail({
    required String email,
  });
}
