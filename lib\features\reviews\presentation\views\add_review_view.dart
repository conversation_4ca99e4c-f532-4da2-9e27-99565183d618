import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/services/get_it_service.dart';
import '../../../../core/helper/build_app_bar.dart';
import '../../domain/repos/reviews_repo.dart';
import '../manager/reviews_cubit/reviews_cubit.dart';
import 'widgets/add_review_form.dart';

class AddReviewView extends StatelessWidget {
  const AddReviewView({
    super.key,
    required this.productId,
    this.productName,
  });

  final String productId;
  final String? productName;
  static const String routeName = '/add-review';

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => ReviewsCubit(getIt<ReviewsRepo>()),
      child: Scaffold(
        appBar: buildAppBarWithAlarmWidget(
          context,
          title: 'إضافة تقييم',
          isBack: true,
          isNotification: false,
        ),
        body: AddReviewForm(
          productId: productId,
          productName: productName,
        ),
      ),
    );
  }
}
