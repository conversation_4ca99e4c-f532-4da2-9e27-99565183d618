import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../../../../core/cubits/theme_cubit/theme_cubit.dart';
import '../../../../../core/cubits/theme_cubit/theme_state.dart';
import '../../../../../core/utils/responsive_utils.dart';

class ThemeSwitchItem extends StatelessWidget {
  const ThemeSwitchItem({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeCubit, ThemeState>(
      builder: (context, state) {
        return Container(
          margin: REdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: RBorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ListTile(
            contentPadding: REdgeInsets.symmetric(horizontal: 20, vertical: 8),
            leading: Container(
              width: ResponsiveUtils.spacing(48),
              height: ResponsiveUtils.spacing(48),
              decoration: BoxDecoration(
                color: Theme.of(context)
                    .colorScheme
                    .primary
                    .withValues(alpha: 0.1),
                borderRadius: RBorderRadius.circular(12),
              ),
              child: Icon(
                state.isDarkMode ? FontAwesomeIcons.moon : FontAwesomeIcons.sun,
                color: Theme.of(context).colorScheme.primary,
                size: ResponsiveUtils.iconSize(20),
              ),
            ),
            title: Text(
              'المظهر',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            subtitle: Text(
              state.isDarkMode ? 'المظهر الداكن' : 'المظهر الفاتح',
              style: Theme.of(context).textTheme.bodySmall,
            ),
            trailing: Switch.adaptive(
              value: state.isDarkMode,
              onChanged: (value) {
                context.read<ThemeCubit>().toggleTheme();
              },
              activeColor: Theme.of(context).colorScheme.primary,
            ),
          ),
        );
      },
    );
  }
}
