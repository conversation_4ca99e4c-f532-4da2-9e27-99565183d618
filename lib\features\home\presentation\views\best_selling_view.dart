import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/helper/build_app_bar.dart';
import '../../../../core/repos/products_repos/products_repo.dart';
import '../../../../core/services/get_it_service.dart';
import '../manager/best_selling_cubit/best_selling_cubit.dart';
import 'widgets/best_selling_view_body.dart';

class BestSellingView extends StatelessWidget {
  const BestSellingView({super.key});
  static const routeName = '/best-selling';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: buildAppBarWithAlarmWidget(
        context,
        title: 'الأكثر مبيعًا',
        isBack: true,
        isNotification: false,
      ),
      body: BlocProvider(
        create: (context) => BestSellingCubit(
          getIt.get<ProductsRepo>(),
        )..getBestSellingProducts(),
        child: const BestSellingViewBody(),
      ),
    );
  }
}
