import 'package:get_it/get_it.dart';
import '../cubits/favorites_cubit/favorites_cubit.dart';
import '../repos/orders_repo/orders_repo.dart';
import '../repos/orders_repo/orders_repo_impl.dart';
import '../repos/products_repos/products_repo.dart';
import '../repos/products_repos/products_repo_impl.dart';
import 'data_service.dart';
import 'firebase_auth_service.dart';
import 'firestore_service.dart';
import 'supabase_storage_service.dart';

import '../../features/auth/data/repos/auth_repo_impl.dart';
import '../../features/auth/data/repos/auth_repo.dart';
import '../../features/profile/data/repos/profile_repo_impl.dart';
import '../../features/profile/domain/repos/profile_repo.dart';
import '../../features/orders/data/repos/orders_repo_impl.dart';
import '../../features/orders/domain/repos/orders_repo.dart';
import '../../features/favorites/data/repos/favorites_repo_impl.dart';
import '../../features/favorites/domain/repos/favorites_repo.dart';
import '../../features/address_book/data/repos/address_book_repo_impl.dart';
import '../../features/address_book/domain/repos/address_book_repo.dart';
import '../../features/reviews/data/repos/reviews_repo_impl.dart';
import '../../features/reviews/domain/repos/reviews_repo.dart';
import '../../features/home/<USER>/manager/cart_cubit/cart_cubit.dart';
import '../../features/home/<USER>/repos/categories_repo_impl.dart';
import '../../features/home/<USER>/repos/categories_repo.dart';
import '../../features/home/<USER>/repos/companies_repo_impl.dart';
import '../../features/home/<USER>/repos/companies_repo.dart';

final getIt = GetIt.instance;

void setupGetit() {
  //
  getIt.registerSingleton<FirebaseAuthService>(FirebaseAuthService());
  //
  getIt.registerSingleton<DatabaseService>(FireStoreService());
  //
  getIt.registerSingleton<SupabaseStorageService>(SupabaseStorageService());
  //
  getIt.registerSingleton<AuthRepo>(
    AuthRepoImpl(
      firebaseAuthService: getIt<FirebaseAuthService>(),
      databaseService: getIt<DatabaseService>(),
      storageService: getIt<SupabaseStorageService>(),
    ),
  );
  //
  getIt.registerSingleton<ProductsRepo>(
    ProductsRepoImpl(getIt<DatabaseService>()),
  );
  //
  getIt.registerSingleton<OrdersRepo>(
    OrdersRepoImpl(
      getIt<DatabaseService>(),
    ),
  );
  //
  getIt.registerSingleton<ProfileRepo>(
    ProfileRepoImpl(
      firebaseAuthService: getIt<FirebaseAuthService>(),
      databaseService: getIt<DatabaseService>(),
      storageService: getIt<SupabaseStorageService>(),
    ),
  );
  //
  getIt.registerSingleton<UserOrdersRepo>(
    UserOrdersRepoImpl(
      databaseService: getIt<DatabaseService>(),
    ),
  );
  //
  getIt.registerSingleton<FavoritesRepo>(
    FavoritesRepoImpl(
      databaseService: getIt<DatabaseService>(),
    ),
  );
  //
  getIt.registerSingleton<AddressBookRepo>(
    AddressBookRepoImpl(
      databaseService: getIt<DatabaseService>(),
    ),
  );
  //
  getIt.registerSingleton<ReviewsRepo>(
    ReviewsRepoImpl(
      databaseService: getIt<DatabaseService>(),
    ),
  );
  //
  getIt.registerSingleton<GlobalFavoritesCubit>(
    GlobalFavoritesCubit(getIt<FavoritesRepo>()),
  );
  //
  getIt.registerSingleton<CategoriesRepo>(
    CategoriesRepoImpl(
      databaseService: getIt<DatabaseService>(),
    ),
  );
  //
  getIt.registerSingleton<CompaniesRepo>(
    CompaniesRepoImpl(
      databaseService: getIt<DatabaseService>(),
    ),
  );
  //
  getIt.registerSingleton<CartCubit>(
    CartCubit(),
  );
  //
}
