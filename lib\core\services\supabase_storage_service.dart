import 'dart:developer';
import 'dart:io';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../utils/app_keys.dart';

class SupabaseStorageService {
  static SupabaseClient? _client;

  // Initialize Supabase client
  static Future<void> initialize() async {
    try {
      await Supabase.initialize(
        url: kSupabaseUrl,
        anonKey: kSupabaseKey,
      );
      _client = Supabase.instance.client;
      log('Supabase initialized successfully');
    } catch (e) {
      log('Error initializing Supabase: ${e.toString()}');
      throw Exception('فشل في تهيئة خدمة التخزين');
    }
  }

  // Get Supabase client instance
  static SupabaseClient get client {
    if (_client == null) {
      throw Exception('Supabase not initialized. Call initialize() first.');
    }
    return _client!;
  }

  /// Upload user profile image to users_images bucket
  Future<String> uploadUserImage({
    required File imageFile,
    required String userId,
  }) async {
    try {
      // Create a unique filename
      final String fileName =
          'profile_${userId}_${DateTime.now().millisecondsSinceEpoch}.jpg';

      // Upload the file to users_images bucket
      await client.storage.from('users_images').upload(fileName, imageFile);

      // Get the public URL
      final String publicUrl =
          client.storage.from('users_images').getPublicUrl(fileName);

      log('Image uploaded successfully: $publicUrl');
      return publicUrl;
    } catch (e) {
      log('Error uploading image: ${e.toString()}');
      throw Exception('فشل في رفع الصورة. الرجاء المحاولة مرة أخرى.');
    }
  }

  /// Delete user profile image from users_images bucket
  Future<void> deleteUserImage(String imageUrl) async {
    try {
      // Extract filename from URL
      final Uri uri = Uri.parse(imageUrl);
      final String fileName = uri.pathSegments.last;

      // Delete the file
      await client.storage.from('users_images').remove([fileName]);

      log('Image deleted successfully: $imageUrl');
    } catch (e) {
      log('Error deleting image: ${e.toString()}');
      // Don't throw error for deletion failures
    }
  }

  /// Update user profile image (delete old and upload new)
  Future<String> updateUserImage({
    required File newImageFile,
    required String userId,
    String? oldImageUrl,
  }) async {
    try {
      // Delete old image if exists
      if (oldImageUrl != null && oldImageUrl.isNotEmpty) {
        await deleteUserImage(oldImageUrl);
      }

      // Upload new image
      return await uploadUserImage(
        imageFile: newImageFile,
        userId: userId,
      );
    } catch (e) {
      log('Error updating image: ${e.toString()}');
      throw Exception('فشل في تحديث الصورة. الرجاء المحاولة مرة أخرى.');
    }
  }

  /// Check if users_images bucket exists and create if not
  Future<void> ensureBucketExists() async {
    try {
      // Try to get bucket info
      await client.storage.getBucket('users_images');
      log('users_images bucket exists');
    } catch (e) {
      // If bucket doesn't exist, create it
      try {
        await client.storage.createBucket(
          'users_images',
          const BucketOptions(
            public: true,
            allowedMimeTypes: ['image/jpeg', 'image/png', 'image/jpg'],
          ),
        );
        log('users_images bucket created successfully');
      } catch (createError) {
        log('Error creating bucket: ${createError.toString()}');
        throw Exception('فشل في إنشاء مجلد التخزين');
      }
    }
  }
}
