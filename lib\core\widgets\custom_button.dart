import 'package:flutter/material.dart';

import '../utils/responsive_utils.dart';

class CustomButton extends StatelessWidget {
  const CustomButton({
    super.key,
    required this.text,
    this.bgColor,
    this.textColor,
    this.radius,
    this.onPressed,
  });
  final Color? bgColor;
  final Color? textColor;
  final String text;
  final BorderRadius? radius;
  final Function()? onPressed;
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: ResponsiveUtils.spacing(54),
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: bgColor,
          foregroundColor: textColor,
          shape: RoundedRectangleBorder(
            borderRadius: radius ?? RBorderRadius.circular(16),
          ),
          textStyle: Theme.of(context).textTheme.titleMedium,
        ),
        child: Text(text),
      ),
    );
  }
}
