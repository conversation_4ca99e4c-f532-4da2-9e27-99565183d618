import 'package:flutter/material.dart';

abstract class AppColorsDark {
  static const Color primaryColor = Color(0xFF42a5f5);
  static const Color lightPrimaryColor = Color(0xFF64b5f6);
  static const Color darkPrimaryColor = Color(0xFF1976d2);
  static const Color secondaryColor = Color(0xFF3A3A3A);
  static const Color accentColor = Color(0xFF90CAF9);
  static const Color textColor = Color(0xFFFFFFFF);
  static const Color lightTextColor = Color(0xFFB0BEC5);
  static const Color errorColor = Color(0xFFEF5350);
  static const Color successColor = Color(0xFF66BB6A);
  static const Color backgroundColor = Color(0xFF0D1117);
  static const Color surfaceColor = Color(0xFF161B22);
  static const Color cardColor = Color(0xFF21262D);
  static const Color dividerColor = Color(0xFF30363D);
  static const Color iconColor = Color(0xFFE6EDF3);
}
