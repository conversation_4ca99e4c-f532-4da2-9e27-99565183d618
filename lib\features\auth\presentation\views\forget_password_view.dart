import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/services/get_it_service.dart';
import '../../data/repos/auth_repo.dart';
import '../manager/forget_password_cubit/forget_password_cubit.dart';
import 'widgets/custom_app_bar.dart';
import 'widgets/forget_password_view_body_bloc_consumer.dart';

class ForgetPasswordView extends StatelessWidget {
  const ForgetPasswordView({super.key});
  static const String routeName = 'ForgetPasswordView';
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => ForgetPasswordCubit(
        getIt<AuthRepo>(),
      ),
      child: Scaffold(
        appBar: buildCustomAppBar(
          context,
          title: 'نسيان كلمة المرور',
          onTap: () => Navigator.pop(context),
        ),
        body: const ForgetPasswordViewBodyBlocConsumer(),
      ),
    );
  }
}
