import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../core/utils/responsive_utils.dart';
import '../../../../../core/widgets/custom_loading_indicator.dart';
import '../../../../../core/widgets/custom_error_widget.dart';
import '../../../../../core/helper/build_custom_snack_bar.dart';
import '../../manager/reviews_cubit/reviews_cubit.dart';
import 'review_item_widget.dart';
import 'review_stats_widget.dart';
import 'add_review_button.dart';

class ReviewsViewBody extends StatelessWidget {
  const ReviewsViewBody({
    super.key,
    required this.productId,
  });

  final String productId;

  @override
  Widget build(BuildContext context) {
    context.initResponsive();
    
    return BlocListener<ReviewsCubit, ReviewsState>(
      listener: (context, state) {
        if (state is ReviewActionSuccess) {
         showSuccessSnackBar(context, message: state.message);
        } else if (state is ReviewsError) {
          showErrorSnackBar(context, message: state.message);
        }
      },
      child: RefreshIndicator(
        onRefresh: () async {
          context.read<ReviewsCubit>().getProductReviews(productId: productId);
        },
        child: CustomScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          slivers: [
            // Review Stats Section
            SliverToBoxAdapter(
              child: Padding(
                padding: REdgeInsets.all(16),
                child: ReviewStatsWidget(productId: productId),
              ),
            ),

            // Add Review Button Section
            SliverToBoxAdapter(
              child: Padding(
                padding: REdgeInsets.symmetric(horizontal: 16),
                child: AddReviewButton(productId: productId),
              ),
            ),

            SliverToBoxAdapter(
              child: SizedBox(height: ResponsiveUtils.spacing(16)),
            ),

            // Reviews List Section
            BlocBuilder<ReviewsCubit, ReviewsState>(
              builder: (context, state) {
                if (state is ReviewsLoading) {
                  return const SliverToBoxAdapter(
                    child: CustomLoadingIndicator(),
                  );
                } else if (state is ReviewsError) {
                  return SliverToBoxAdapter(
                    child: CustomErrorWidget(
                      errMessage: state.message,
                      onRetry: () {
                        context.read<ReviewsCubit>().getProductReviews(
                              productId: productId,
                            );
                      },
                    ),
                  );
                } else if (state is ReviewsLoaded) {
                  if (state.reviews.isEmpty) {
                    return SliverToBoxAdapter(
                      child: Center(
                        child: Padding(
                          padding: REdgeInsets.all(32),
                          child: Column(
                            children: [
                              Icon(
                                Icons.rate_review_outlined,
                                size: ResponsiveUtils.iconSize(64),
                                color: Colors.grey[400],
                              ),
                              SizedBox(height: ResponsiveUtils.spacing(16)),
                              Text(
                                'لا توجد تقييمات بعد',
                                style: TextStyle(
                                  fontSize: ResponsiveUtils.fontSize(16),
                                  fontWeight: FontWeight.w500,
                                  color: Colors.grey[600],
                                ),
                              ),
                              SizedBox(height: ResponsiveUtils.spacing(8)),
                              Text(
                                'كن أول من يقيم هذا المنتج',
                                style: TextStyle(
                                  fontSize: ResponsiveUtils.fontSize(14),
                                  color: Colors.grey[500],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  }

                  return SliverPadding(
                    padding: REdgeInsets.symmetric(horizontal: 16),
                    sliver: SliverList(
                      delegate: SliverChildBuilderDelegate(
                        (context, index) {
                          final review = state.reviews[index];
                          return Padding(
                            padding: EdgeInsets.only(
                              bottom: index < state.reviews.length - 1
                                  ? ResponsiveUtils.spacing(16)
                                  : ResponsiveUtils.spacing(32),
                            ),
                            child: ReviewItemWidget(
                              review: review,
                              productId: productId,
                            ),
                          );
                        },
                        childCount: state.reviews.length,
                      ),
                    ),
                  );
                }

                return const SliverToBoxAdapter(child: SizedBox.shrink());
              },
            ),
          ],
        ),
      ),
    );
  }
}
