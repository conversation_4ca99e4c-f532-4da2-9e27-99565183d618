name: hoot_e_commerce
description: "Shatabha E-Commerce - Modern shopping app with Firebase integration"
publish_to: "none" # Remove this line if you wish to publish to pub.dev

version: 1.0.0+1

environment:
  sdk: ">=3.5.0 <4.0.0"

dependencies:
  bloc: ^8.1.4
  cached_network_image: ^3.4.1
  cloud_firestore: ^5.5.0
  crypto: ^3.0.6
  cupertino_icons: ^1.0.8
  dartz: ^0.10.1
  device_preview: ^1.3.1
  dots_indicator: ^3.0.0
  equatable: ^2.0.7
  firebase_auth: ^5.3.3
  firebase_core: ^3.8.0
  firebase_storage: ^12.3.4
  flutter:
    sdk: flutter
  flutter_bloc: ^8.1.6
  flutter_facebook_auth: ^7.1.1
  flutter_intl: ^0.0.1
  flutter_localizations:
    sdk: flutter
  flutter_paypal_payment: ^1.0.8
  flutter_spinkit: ^5.2.1
  flutter_svg: ^2.0.14
  flutter_svg_provider: ^1.0.7
  font_awesome_flutter: ^10.8.0
  get_it: ^8.0.2
  google_fonts: ^6.2.1
  google_sign_in: ^6.2.2
  image: ^4.3.0
  image_picker: ^1.1.2
  intl: 0.20.2
  intl_phone_field: ^3.2.0
  lottie: ^3.3.1
  meta: ^1.15.0
  modal_progress_hud_nsn: ^0.5.1
  path_provider: ^2.1.5
  provider: ^6.1.2
  shared_preferences: ^2.3.3
  sign_in_with_apple: ^6.1.3
  skeletonizer: ^2.1.0+1
  supabase_flutter: ^2.8.0
  timeago: ^3.7.1

dev_dependencies:
  flutter_launcher_icons: ^0.14.1

  flutter_lints: ^5.0.0
  flutter_test:
    sdk: flutter

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/images/logo.png
    - assets/images/ifEmptyImage.png
    - assets/animations/
    - assets/icons/
    - assets/images/avatar_home_view.png
    - assets/images/Bold/
    - assets/images/Outline/

  fonts:
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-Regular.ttf
        - asset: assets/fonts/Cairo-Medium.ttf
          weight: 500
        - asset: assets/fonts/Cairo-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Cairo-Bold.ttf
          weight: 700

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/logo.png"
  adaptive_icon_background: "#000000"
  adaptive_icon_foreground: "assets/images/logo.png"

flutter_assets:
  assets_path: assets/images/
  output_path: lib/core/utils/
  filename: app_assets.dart
flutter_intl:
  enabled: true

