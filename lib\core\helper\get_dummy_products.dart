import 'dart:io';
import '../../features/reviews/domain/entities/review_entity.dart';
import '../entities/product_entity.dart';
import '../utils/app_assets.dart';

ProductEntity getDummyProduct() {
  return ProductEntity(
    name: 'Dummy Product',
    description: 'This is a sample product for testing purposes.',
    price: 199.99,
    category: 'Electronics',
    company: 'TechCorp',
    imageUrl:
        'https://stella-store.com/wp-content/uploads/2021/11/%D9%88%D8%AD%D8%AF%D8%A9-%D8%AD%D9%85%D8%A7%D9%85-60-%D8%B3%D9%85-1-1.jpg', // صورة من Lorem Picsum
    imageUrls: const [
      'https://picsum.photos/200/300',
      'https://picsum.photos/250/350',
      'https://picsum.photos/300/400'
    ],
    thumbnailUrl: 'https://picsum.photos/100/100',
    additionalImages: [
      File('assets/images/sample_product_1.jpg'),
      File('assets/images/sample_product_2.jpg'),
    ],
    code: 'PRD12345',
    isFeatured: true,
    originalPrice: 249.99,
    discount: 50.0,
    tags: const ['New', 'Featured', 'Discount'],
    stockQuantity: 20,
    createdAt: DateTime.now().toIso8601String(),
    updatedAt: DateTime.now().toIso8601String(),
    isAvailable: true,
    isOnSale: true,
    specifications: const [
      'Color: Black',
      'Weight: 1.2kg',
      'Dimensions: 10x5x2cm'
    ],
    colorsAvailable: const ['Black', 'White', 'Blue'],
    reviews: [
      ReviewEntity(
          id: '',
          userId: '',
          productId: '',
          userName: '',
          userImage: '',
          rating: 0,
          reviewText: '',
          createdAt: DateTime.now(),
          isVerifiedPurchase: false,
        ),
    
    ],
  );
}

List<ProductEntity> getDummyProducts() {
  return List.generate(10, (index) => getDummyProduct());
}
