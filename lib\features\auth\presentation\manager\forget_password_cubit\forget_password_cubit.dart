import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../data/repos/auth_repo.dart';

part 'forget_password_state.dart';

class ForgetPasswordCubit extends Cubit<ForgetPasswordState> {
  ForgetPasswordCubit(this._authRepo) : super(ForgetPasswordInitial());
  final AuthRepo _authRepo;

  Future<void> sendPasswordResetEmail({required String email}) async {
    emit(ForgetPasswordLoading());
    final result = await _authRepo.sendPasswordResetEmail(email: email);
    result.fold(
      (failure) => emit(ForgetPasswordError(message: failure.message)),
      (_) => emit(ForgetPasswordSuccess(
        message: 'تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني.',
      )),
    );
  }
}
