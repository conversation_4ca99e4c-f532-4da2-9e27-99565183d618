import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../core/cubits/products_cubit/products_cubit.dart';
import '../../../../../core/repos/products_repos/products_repo.dart';
import '../../../../../core/services/get_it_service.dart';
import '../../manager/search_filter_cubit/search_filter_cubit.dart';
import '../cart_view.dart';
import '../home_view.dart';
import 'products_view_body.dart';
import '../../../../profile/presentation/views/profile_view.dart';

class MainViewBody extends StatelessWidget {
  const MainViewBody({
    super.key,
    required this.currentViewIndex,
    required this.onTabChange,
  });

  final int currentViewIndex;
  final ValueChanged<int> onTabChange;

  @override
  Widget build(BuildContext context) {
    return IndexedStack(
      index: currentViewIndex,
      children: [
        HomeView(onTabChange: onTabChange),
        // Wrap ProductsView with its required providers
        MultiBlocProvider(
          providers: [
            BlocProvider(
              create: (context) => ProductsCubit(
                getIt.get<ProductsRepo>(),
              ),
            ),
            BlocProvider(
              create: (context) => SearchFilterCubit(),
            ),
          ],
          child: const ProductsViewBody(),
        ),
        const CartView(),
        const ProfileView(),
      ],
    );
  }
}
