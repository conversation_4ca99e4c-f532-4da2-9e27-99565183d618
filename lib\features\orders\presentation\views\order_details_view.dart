import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/services/get_it_service.dart';
import '../../../auth/presentation/views/widgets/custom_app_bar.dart';
import '../../domain/repos/orders_repo.dart';
import '../manager/orders_cubit/orders_cubit.dart';
import 'widgets/order_details_view_body.dart';

class OrderDetailsView extends StatelessWidget {
  const OrderDetailsView({super.key, required this.orderId});

  final String orderId;
  static const String routeName = '/order-details';

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) =>
          OrdersCubit(getIt<UserOrdersRepo>())..getOrderById(orderId: orderId),
      child: Scaffold(
        appBar: buildCustomAppBar(
          context,
          title: 'تفاصيل الطلب',
          onTap: () => Navigator.pop(context),
        ),
        body: const OrderDetailsViewBody(),
      ),
    );
  }
}
