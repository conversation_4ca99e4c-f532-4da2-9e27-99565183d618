import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/helper/get_user.dart';
import '../../../../core/services/get_it_service.dart';
import '../../../auth/presentation/views/widgets/custom_app_bar.dart';
import '../../domain/repos/address_book_repo.dart';
import '../manager/address_book_cubit/address_book_cubit.dart';
import 'widgets/address_book_view_body.dart';

class AddressBookView extends StatelessWidget {
  const AddressBookView({super.key});
  static const String routeName = '/address-book';

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => AddressBookCubit(getIt<AddressBookRepo>())..getAddresses(userId: getUser().uId),
      child: Scaffold(
        appBar: buildCustomAppBar(
          context,
          title: 'دفتر العناوين',
          onTap: () => Navigator.pop(context),
        ),
        body: const AddressBookViewBody(),
      ),
    );
  }
}
