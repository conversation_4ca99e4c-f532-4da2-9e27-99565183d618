import 'order_status_entity.dart';

class UserOrderEntity {
  final String id;
  final String userId;
  final List<OrderItemEntity> items;
  final double totalAmount;
  final OrderStatus status;
  final DateTime orderDate;
  final DateTime? deliveryDate;
  final String shippingAddress;
  final String paymentMethod;

  UserOrderEntity({
    required this.id,
    required this.userId,
    required this.items,
    required this.totalAmount,
    required this.status,
    required this.orderDate,
    this.deliveryDate,
    required this.shippingAddress,
    required this.paymentMethod,
  });
}

class OrderItemEntity {
  final String productId;
  final String productName;
  final String productImage;
  final int quantity;
  final double price;
  final double totalPrice;

  OrderItemEntity({
    required this.productId,
    required this.productName,
    required this.productImage,
    required this.quantity,
    required this.price,
    required this.totalPrice,
  });
}
