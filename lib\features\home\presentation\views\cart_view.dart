import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/helper/build_app_bar.dart';
import '../manager/cart_item_cubit/cart_item_cubit.dart';
import 'widgets/cart_view_body.dart';

class CartView extends StatelessWidget {
  const CartView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: buildAppBarWithAlarmWidget(
        context,
        title: 'السلة',
        isBack: false,
        isNotification: false,
      ),
      body: BlocProvider(
        create: (context) => CartItemCubit(),
        child: const CartViewBody(),
      ),
    );
  }
}
