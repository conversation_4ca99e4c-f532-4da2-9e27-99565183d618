part of 'update_profile_image_cubit.dart';

@immutable
sealed class UpdateProfileImageState {}

final class UpdateProfileImageInitial extends UpdateProfileImageState {}

final class UpdateProfileImageLoading extends UpdateProfileImageState {}

final class UpdateProfileImageSuccess extends UpdateProfileImageState {
  final String imageUrl;
  final UserEntity updatedUser;

  UpdateProfileImageSuccess({
    required this.imageUrl,
    required this.updatedUser,
  });
}

final class UpdateProfileImageError extends UpdateProfileImageState {
  final String message;

  UpdateProfileImageError({required this.message});
}
