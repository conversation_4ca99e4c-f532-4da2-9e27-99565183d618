import 'dart:developer';

class DebugLogger {
  static void logCartOperation(String operation, String details) {
    log('🛒 [CART] $operation: $details');
  }

  static void logError(String component, String error) {
    log('❌ [ERROR] $component: $error');
  }

  static void logInfo(String component, String info) {
    log('ℹ️ [INFO] $component: $info');
  }

  static void logWarning(String component, String warning) {
    log('⚠️ [WARNING] $component: $warning');
  }

  static void logSuccess(String component, String success) {
    log('✅ [SUCCESS] $component: $success');
  }

  static void logSeparator() {
    log('═══════════════════════════════════════════════════════════════');
  }

  static void logCartState(List<dynamic> cartItems) {
    logSeparator();
    log('📊 [CART STATE] Total items: ${cartItems.length}');
    for (int i = 0; i < cartItems.length; i++) {
      final item = cartItems[i];
      log('📊   [$i] ${item.productEntity.name} - Qty: ${item.quantity}');
    }
    logSeparator();
  }
}
