import 'package:flutter/material.dart';
import 'responsive_utils.dart';

abstract class AppDecorations {
  static ShapeDecoration greyBoxDecoration(BuildContext context) =>
      ShapeDecoration(
        color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.5),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(ResponsiveUtils.radius(4)),
        ),
      );
}
