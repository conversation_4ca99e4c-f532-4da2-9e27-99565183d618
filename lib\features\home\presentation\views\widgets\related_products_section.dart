import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../core/entities/product_entity.dart';
import '../../../../../core/utils/responsive_utils.dart';
import '../../../../../core/widgets/custom_loading_indicator.dart';
import '../../../../../core/widgets/custom_error_widget.dart';
import '../../../../../core/widgets/custom_product_item.dart';
import '../../manager/categories_cubit/categories_cubit.dart';
import '../category_products_view.dart';

class RelatedProductsSection extends StatelessWidget {
  const RelatedProductsSection({
    super.key,
    required this.currentProduct,
  });

  final ProductEntity currentProduct;

  @override
  Widget build(BuildContext context) {
    context.initResponsive();

    // Trigger category products fetch when widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final categoriesCubit = context.read<CategoriesCubit>();
      // Only fetch if not already loading or loaded for this category
      if (categoriesCubit.state is! CategoryProductsLoading &&
          categoriesCubit.state is! CategoryProductsSuccess) {
        categoriesCubit.getCategoryProducts(category: currentProduct.category);
      }
    });

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section Header
        Padding(
          padding: REdgeInsets.symmetric(horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'قد يعجبك أيضاً',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pushNamed(
                    context,
                    CategoryProductsView.routeName,
                    arguments: currentProduct.category,
                  );
                },
                child: Text(
                  'المزيد',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.w500,
                      ),
                ),
              ),
            ],
          ),
        ),

        const RSizedBox.height(12),

        // Related Products List
        BlocBuilder<CategoriesCubit, CategoriesState>(
          builder: (context, state) {
            if (state is CategoryProductsLoading) {
              return SizedBox(
                height: ResponsiveUtils.spacing(200),
                child: const Center(
                  child: CustomLoadingIndicator(),
                ),
              );
            }

            if (state is CategoryProductsFailure) {
              return Padding(
                padding: REdgeInsets.symmetric(horizontal: 16),
                child: CustomErrorWidget(
                  errMessage: state.errMessage,
                  onRetry: () {
                    context.read<CategoriesCubit>().getCategoryProducts(
                          category: currentProduct.category,
                        );
                  },
                ),
              );
            }

            if (state is CategoryProductsSuccess) {
              // Filter out current product from the category products
              final relatedProducts = state.products
                  .where((product) => product.code != currentProduct.code)
                  .take(5)
                  .toList();

              if (relatedProducts.isEmpty) {
                return const SizedBox.shrink();
              }

              return _buildProductsList(context, relatedProducts);
            }

            return const SizedBox.shrink();
          },
        ),
      ],
    );
  }

  Widget _buildProductsList(
      BuildContext context, List<ProductEntity> products) {
    return SizedBox(
      height: ResponsiveUtils.spacing(280),
      child: ListView.builder(
        padding: REdgeInsets.symmetric(horizontal: 16),
        scrollDirection: Axis.horizontal,
        physics: const BouncingScrollPhysics(),
        itemCount: products.length,
        itemBuilder: (context, index) {
          return Padding(
            padding: REdgeInsets.only(left: 12),
            child: SizedBox(
              width: ResponsiveUtils.spacing(160),
              child: CustomProductItem(
                product: products[index],
              ),
            ),
          );
        },
      ),
    );
  }
}
