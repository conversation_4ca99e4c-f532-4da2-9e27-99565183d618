import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_paypal_payment/flutter_paypal_payment.dart';
import '../../../../../core/helper/build_custom_snack_bar.dart';
import '../../../../../core/utils/app_keys.dart';
import '../../../../../core/utils/responsive_utils.dart';
import '../../../../../core/widgets/custom_dialog.dart';
import '../../../../../main.dart'; // Import for navigatorKey

import '../../../../home/<USER>/views/main_view.dart';
import '../../../domain/entities/order_entity.dart';
import '../../../domain/entities/paypal_payment_entity/paypal_payment_entity.dart';
import '../../manager/add_order_cubit/add_order_cubit.dart';
import 'checkout_steps.dart';
import 'package:provider/provider.dart';

import '../../../../../core/widgets/custom_button.dart';
import 'checkout_steps_page_view.dart';

class CheckoutViewBody extends StatefulWidget {
  const CheckoutViewBody({super.key});

  @override
  State<CheckoutViewBody> createState() => _CheckoutViewBodyState();
}

class _CheckoutViewBodyState extends State<CheckoutViewBody> {
  late PageController pageController;
  ValueNotifier<AutovalidateMode> valueNotifier =
      ValueNotifier(AutovalidateMode.disabled);
  @override
  void initState() {
    pageController = PageController();
    pageController.addListener(() {
      setState(() {
        currentPageIndex = pageController.page!.toInt();
      });
    });
    super.initState();
  }

  @override
  void dispose() {
    pageController.dispose();
    valueNotifier.dispose();
    super.dispose();
  }

  int currentPageIndex = 0;
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  @override
  Widget build(BuildContext context) {
    context.initResponsive();
    return Padding(
      padding: REdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          const RSizedBox.height(20),
          CheckoutSteps(
            onTap: (index) {
              if (index == 0) {
                pageController.animateToPage(index,
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeIn);
              } else if (index == 1) {
                final orderEntity = context.read<OrderEntity>();
                if (orderEntity.payWithCash != null) {
                  pageController.animateToPage(index,
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeIn);
                } else {
                  showWarningSnackBar(
                    context,
                    message: 'يرجى تحديد طريقة الدفع أولاً',
                  );
                }
              } else {
                _handleAddressValidation();
              }
            },
            pageController: pageController,
            currentPageIndex: currentPageIndex,
          ),
          Expanded(
            child: CheckoutStepsPageView(
              valueListenable: valueNotifier,
              pageController: pageController,
              formKey: _formKey,
            ),
          ),
          CustomButton(
            bgColor: Theme.of(context).colorScheme.primary,
            textColor: Colors.white,
            onPressed: () {
              if (currentPageIndex == 0) {
                _handleShippingSectionValidation(context);
              } else if (currentPageIndex == 1) {
                _handleAddressValidation();
              } else {
                _processPayment(context);
              }
            },
            text: getNextButtonText(currentPageIndex),
          ),
          const RSizedBox.height(24),
        ],
      ),
    );
  }

  void _handleShippingSectionValidation(BuildContext context) {
    if (context.read<OrderEntity>().payWithCash != null) {
      pageController.animateToPage(currentPageIndex + 1,
          duration: const Duration(milliseconds: 300), curve: Curves.bounceIn);
    } else {
      showWarningSnackBar(
        context,
        message: 'يرجى تحديد طريقة الدفع أولاً',
      );
    }
  }

  String getNextButtonText(int currentPageIndex) {
    switch (currentPageIndex) {
      case 0:
        return 'التالي';
      case 1:
        return 'التالي';
      case 2:
        return 'الدفع عبر PayPal';
      default:
        return 'التالي';
    }
  }

  void _handleAddressValidation() {
    if (_formKey.currentState!.validate()) {
      _formKey.currentState!.save();
      pageController.animateToPage(
        currentPageIndex + 1,
        duration: const Duration(milliseconds: 300),
        curve: Curves.bounceIn,
      );
    } else {
      valueNotifier.value = AutovalidateMode.always;
    }
  }

  void _processPayment(BuildContext context) {
    final orderEntity = context.read<OrderEntity>();
    final PaypalPaymentEntity paypalPaymentEntity =
        PaypalPaymentEntity.fromEntity(orderEntity);
    final addOrderCubit = context.read<AddOrderCubit>();

    Navigator.of(context).push(MaterialPageRoute(
      builder: (BuildContext context) => PaypalCheckoutView(
        sandboxMode: true, // true for testing, false for production
        clientId: kPaypalClientId,
        secretKey: kPaypalSecretKey,
        transactions: [
          paypalPaymentEntity.toJson(),
        ],
        note: 'Contact us for any questions on your order.',
        onSuccess: (Map params) async {
          try {
            // Close PayPal dialog first
            if (Navigator.canPop(context)) {
              Navigator.pop(context);
            }

            // Add order to database
            addOrderCubit.addOrder(order: orderEntity);

            // Wait a bit for the pop to complete
            await Future.delayed(const Duration(milliseconds: 200));

            // Use global navigator key for safe navigation
            navigatorKey.currentState?.pushNamedAndRemoveUntil(
              MainView.routeName,
              (route) => false,
            );

            // Show success message after navigation
            Future.delayed(const Duration(milliseconds: 500), () {
              final currentContext = navigatorKey.currentContext;
              if (currentContext != null && currentContext.mounted) {
                DialogTypes.showSuccessDialog(
                  context: currentContext,
                  title: 'وصل',
                  content: 'تمت العملية بنجاح',
                );
              }
            });
          } catch (e) {
            // Fallback: use global navigator key
            navigatorKey.currentState?.pushNamedAndRemoveUntil(
              MainView.routeName,
              (route) => false,
            );
          }
        },
        onError: (error) {
          Navigator.pop(context);
          log(error.toString());
          if (context.mounted) {
            showErrorSnackBar(context, message: 'حدث خطأ في عملية الدفع');
          }
        },
        onCancel: () {
          Navigator.pop(context);
        },
      ),
    ));
  }
}
