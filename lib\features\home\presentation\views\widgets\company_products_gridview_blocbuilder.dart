import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../core/helper/get_dummy_products.dart';
import '../../../../../core/utils/app_animations_lottie.dart';
import '../../../../../core/widgets/custom_err_widget.dart';
import '../../../../../core/widgets/custom_empty_state_widget.dart';
import '../../manager/companies_cubit/companies_cubit.dart';
import '../../manager/search_filter_cubit/search_filter_cubit.dart';
import 'products_grid_view.dart';
import 'package:skeletonizer/skeletonizer.dart';

class CompanyProductsGridViewBlocBuilder extends StatelessWidget {
  const CompanyProductsGridViewBlocBuilder({
    super.key,
    required this.companyName,
  });

  final String companyName;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SearchFilterCubit, SearchFilterState>(
      builder: (context, searchState) {
        // If we have search results, show them
        if (searchState is ProductsFiltered) {
          if (searchState.products.isEmpty) {
            return SliverToBoxAdapter(
              child: CustomEmptyStateWidget(
                lottieAsset: AppAnimationsLottie.emptyBox,
                title: 'لا توجد نتائج',
                subtitle: searchState.searchQuery.isNotEmpty
                    ? 'لم نجد منتجات تطابق "${searchState.searchQuery}" من شركة "$companyName"'
                    : 'لا توجد منتجات من شركة "$companyName" حاليا.',
              ),
            );
          }

          return ProductsGridView(
            products: searchState.products,
          );
        }

        // Otherwise, show original company products
        return BlocBuilder<CompaniesCubit, CompaniesState>(
          builder: (context, state) {
            if (state is CompanyProductsLoading) {
              return Skeletonizer.sliver(
                child: ProductsGridView(
                  products: getDummyProducts(),
                ),
              );
            }

            if (state is CompanyProductsSuccess) {
              // Show empty state if no products found
              if (state.products.isEmpty) {
                return SliverToBoxAdapter(
                  child: CustomEmptyStateWidget(
                    lottieAsset: AppAnimationsLottie.emptyBox,
                    title: 'لا توجد منتجات',
                    subtitle:
                        'لا توجد منتجات من شركة "$companyName" حالياً.\nجرب شركة أخرى أو تحقق لاحقاً.',
                  ),
                );
              }

              return ProductsGridView(
                products: state.products,
              );
            }

            if (state is CompanyProductsFailure) {
              return SliverToBoxAdapter(
                child: CustomErrorWidget(
                  errorMessage: state.errMessage,
                  onRetry: () {
                    context
                        .read<CompaniesCubit>()
                        .getCompanyProducts(company: companyName);
                  },
                ),
              );
            }

            // Initial state - show empty or loading
            return const SliverToBoxAdapter(
              child: SizedBox.shrink(),
            );
          },
        );
      },
    );
  }
}
