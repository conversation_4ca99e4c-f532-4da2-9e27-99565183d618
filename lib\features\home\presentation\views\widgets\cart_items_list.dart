import 'package:flutter/material.dart';
import '../../../../../core/utils/responsive_utils.dart';
import '../../../domain/entities/cart_item_entity.dart';
import 'cart_item.dart';

class CarItemsList extends StatelessWidget {
  const CarItemsList({super.key, required this.cartItems});
  final List<CartItemEntity> cartItems;
  @override
  Widget build(BuildContext context) {
    return SliverList.separated(
        separatorBuilder: (context, index) => const CustomDivider(),
        itemCount: cartItems.length,
        itemBuilder: (context, index) {
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: CartItem(
              carItemEntity: cartItems[index],
              index: index,
            ),
          );
        });
  }
}

class CustomDivider extends StatelessWidget {
  const CustomDivider({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Divider(
      color: Theme.of(context).dividerColor,
      height: ResponsiveUtils.spacing(22),
      thickness: 1,
      indent: ResponsiveUtils.spacing(16),
      endIndent: ResponsiveUtils.spacing(16),
    );
  }
}
