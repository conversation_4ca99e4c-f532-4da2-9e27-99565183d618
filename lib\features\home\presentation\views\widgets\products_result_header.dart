import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../../../../constants.dart';

class ProductsResultHeader extends StatelessWidget {
  const ProductsResultHeader({super.key, required this.productsLength});
  final int productsLength;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: kHorizontalPadding16),
      child: Row(
        children: [
          Text(
            '$productsLength نتائج',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const Spacer(),
          InkWell(
            onTap: () {},
            child: SvgPicture.asset(
              'assets/icons/filter.svg',
              fit: BoxFit.none,
            ),
          ),
        ],
      ),
    );
  }
}
