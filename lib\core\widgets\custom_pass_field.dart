import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../utils/responsive_utils.dart';
import 'custom_text_field.dart';

class CustomPasswordField extends StatefulWidget {
  const CustomPasswordField(
      {super.key, this.onSaved, required this.isLoginView});
  final void Function(String?)? onSaved;
  final bool isLoginView;

  @override
  State<CustomPasswordField> createState() => _CustomPasswordFieldState();
}

class _CustomPasswordFieldState extends State<CustomPasswordField> {
  bool isObscureText = true;

  @override
  Widget build(BuildContext context) {
    return CustomTextFormField(
      isObscureText: isObscureText,
      label: 'كلمة المرور',
      keyboardType: TextInputType.visiblePassword,
      suffixIcon: IconButton(
        onPressed: () {
          setState(() {
            isObscureText = !isObscureText;
          });
        },
        icon: isObscureText
            ? Icon(
                size: ResponsiveUtils.iconSize(18),
                FontAwesomeIcons.eyeSlash,
                color:
                    Theme.of(context).iconTheme.color?.withValues(alpha: 0.6))
            : Icon(
                size: ResponsiveUtils.iconSize(18),
                FontAwesomeIcons.eye,
                color:
                    Theme.of(context).iconTheme.color?.withValues(alpha: 0.6)),
      ),
      onSaved: widget.onSaved,
      validator: widget.isLoginView
          ? (value) {
              if (value == null || value.isEmpty) {
                return 'برجاء إدخال كلمة المرور';
              }
              return null;
            }
          : (value) {
              if (value == null || value.isEmpty) {
                return 'برجاء إدخال كلمة المرور';
              }
              if (value.length < 6) {
                return 'برجاء إدخال كلمة مرور قوية';
              }
              return null;
            },
    );
  }
}
