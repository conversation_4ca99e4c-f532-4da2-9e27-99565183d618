class CompanyEntity {
  final String id;
  final String name;
  final String image;
  final String? description;
  final String? createdAt;
  final String? updatedAt;
  final String? website;
  final String? email;
  final String? phone;

  CompanyEntity(
      {required this.id,
      required this.name,
      required this.image,
      this.description = '',
      this.createdAt = '',
      this.updatedAt = '',
      this.website = '',
      this.email = '',
      this.phone = ''});

  factory CompanyEntity.fromJson(Map<String, dynamic> json) {
    return CompanyEntity(
        id: json['id'] ?? '',
        name: json['name'] ?? '',
        image: json['image'] ?? '',
        description: json['description'],
        createdAt: json['createdAt'],
        updatedAt: json['updatedAt'],
        website: json['website'],
        email: json['email'],
        phone: json['phone']);
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'image': image,
      'description': description,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'website': website,
      'email': email,
      'phone': phone
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CompanyEntity &&
        other.id == id &&
        other.name == name &&
        other.image == image;
  }

  @override
  int get hashCode => id.hashCode ^ name.hashCode ^ image.hashCode;

  @override
  String toString() =>
      'CompanyEntity(id: $id, name: $name, image: $image, description: $description, createdAt: $createdAt, updatedAt: $updatedAt, website: $website, email: $email, phone: $phone)';
}
