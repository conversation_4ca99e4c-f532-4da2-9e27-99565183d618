import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import '../../../../../core/utils/responsive_utils.dart';
import '../../../domain/entities/order_entity.dart';
import 'payment_item.dart';

import '../../../../../core/utils/app_assets.dart';

class ShippingAddressWidget extends StatelessWidget {
  const ShippingAddressWidget({
    super.key,
    required this.pageController,
  });

  final PageController pageController;
  @override
  Widget build(BuildContext context) {
    log(context.read<OrderEntity>().toString());
    return PaymentItem(
      tile: 'عنوان التوصيل',
      child: Row(
        children: [
          SvgPicture.asset(
            Assets.assetsImagesLocation,
            width: ResponsiveUtils.spacing(20),
            height: ResponsiveUtils.spacing(20),
          ),
          const RSizedBox.width(8),
          Flexible(
            child: Text(
              ' ${context.read<OrderEntity>().shippingAddressEntity}',
              textAlign: TextAlign.right,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
          const Spacer(),
          GestureDetector(
            onTap: () {
              pageController.animateToPage(1,
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeIn);
            },
            child: SizedBox(
              child: Row(
                children: [
                  SvgPicture.asset(
                    Assets.assetsImagesEdit,
                    width: ResponsiveUtils.spacing(16),
                    height: ResponsiveUtils.spacing(16),
                  ),
                  const RSizedBox.width(4),
                  Text(
                    'تعديل',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          decoration: TextDecoration.underline,
                        ),
                  )
                ],
              ),
            ),
          )
        ],
      ),
    );
  }
}
