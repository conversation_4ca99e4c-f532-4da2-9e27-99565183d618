import 'package:flutter/material.dart';
import '../../../../../core/entities/product_entity.dart';
import '../../../../../core/utils/responsive_utils.dart';

class ProductInfoSection extends StatelessWidget {
  const ProductInfoSection({
    super.key,
    required this.product,
  });

  final ProductEntity product;

  @override
  Widget build(BuildContext context) {
    context.initResponsive();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Company Name
        Container(
          padding: REdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primaryContainer,
            borderRadius:  RBorderRadius.circular(20),
          ),
          child: Text(
            product.company,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                  fontWeight: FontWeight.w500,
                ),
          ),
        ),
        
        const RSizedBox.height(12),
        
        // Product Name
        Text(
          product.name,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
                height: 1.2,
              ),
        ),
        
        const RSizedBox.height(8),
        
        // Product Code
        Row(
          children: [
            Icon(
              Icons.qr_code_2_outlined,
              size: ResponsiveUtils.iconSize(16),
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            const RSizedBox.width(6),
            Text(
              'كود المنتج: ${product.code}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
            ),
          ],
        ),
        
        const RSizedBox.height(8),
        
        // Category
        Row(
          children: [
            Icon(
              Icons.category_outlined,
              size: ResponsiveUtils.iconSize(16),
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            const RSizedBox.width(6),
            Text(
              product.category,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
            ),
          ],
        ),
        
        // Stock Status
        if (product.stockQuantity != null) ...[
          const RSizedBox.height(8),
          Row(
            children: [
              Icon(
                product.stockQuantity! > 0 
                    ? Icons.check_circle_outline 
                    : Icons.cancel_outlined,
                size: ResponsiveUtils.iconSize(16),
                color: product.stockQuantity! > 0 
                    ? Colors.green 
                    : Colors.red,
              ),
              const RSizedBox.width(6),
              Text(
                product.stockQuantity! > 0 
                    ? 'متوفر في المخزن (${product.stockQuantity})'
                    : 'غير متوفر',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: product.stockQuantity! > 0 
                          ? Colors.green 
                          : Colors.red,
                      fontWeight: FontWeight.w500,
                    ),
              ),
            ],
          ),
        ],
      ],
    );
  }
}
