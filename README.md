# 🛒 Shatabha - شَـطْبِهَا

<div align="center">

![Flutter](https://img.shields.io/badge/Flutter-3.5.0+-02569B?style=for-the-badge&logo=flutter&logoColor=white)
![Dart](https://img.shields.io/badge/Dart-3.0+-0175C2?style=for-the-badge&logo=dart&logoColor=white)
![Firebase](https://img.shields.io/badge/Firebase-FFCA28?style=for-the-badge&logo=firebase&logoColor=black)
![Material Design](https://img.shields.io/badge/Material%20Design-3-757575?style=for-the-badge&logo=material-design&logoColor=white)

**A modern Arabic e-commerce Flutter application designed for seamless online shopping experiences**

[![GitHub stars](https://img.shields.io/github/stars/KamelKOFASH/shatabha?style=social)](https://github.com/KamelKOFASH/shatabha/stargazers)
[![GitHub forks](https://img.shields.io/github/forks/KamelKOFASH/shatabha?style=social)](https://github.com/KamelKOFASH/shatabha/network/members)
[![GitHub issues](https://img.shields.io/github/issues/KamelKOFASH/shatabha)](https://github.com/KamelKOFASH/shatabha/issues)
[![GitHub license](https://img.shields.io/github/license/KamelKOFASH/shatabha)](https://github.com/KamelKOFASH/shatabha/blob/main/LICENSE)

</div>

---

## 📖 Table of Contents

- [Overview](#-overview)
- [✨ Features](#-features)
- [📱 Screenshots](#-screenshots)
- [🚀 Quick Start](#-quick-start)
- [🛠️ Tech Stack](#️-tech-stack)
- [🏗️ Architecture](#️-architecture)
- [📦 Installation](#-installation)
- [⚙️ Configuration](#️-configuration)
- [🎯 Usage](#-usage)
- [🧪 Testing](#-testing)
- [📚 API Documentation](#-api-documentation)
- [🤝 Contributing](#-contributing)
- [📄 License](#-license)
- [👨‍💻 About](#-about)

---

## 🌟 Overview

**Shatabha (شَـطْبِهَا)** is a comprehensive e-commerce Flutter application specifically designed for Arabic-speaking users. Built with modern development practices and a focus on user experience, it provides a complete shopping journey from product discovery to secure checkout.

### 🎯 Key Highlights

- **🌍 Arabic-First Design**: Built from the ground up for Arabic users with full RTL support
- **⚡ Performance Optimized**: Smooth performance even on lower-end devices
- **🏗️ Clean Architecture**: Maintainable and scalable codebase following best practices
- **🎨 Modern UI/UX**: Material 3 design with light and dark themes
- **🔒 Secure**: Firebase-powered authentication and data encryption
- **📱 Cross-Platform**: Works seamlessly on iOS, Android, and Web

<div align="center">
  <img src="readme%20image/1.jpg" alt="Shatabha App" width="200">
  <img src="readme%20image/2.jpg" alt="Shatabha App" width="200">
  <img src="readme%20image/3.jpg" alt="Shatabha App" width="200">
</div>

---

## ✨ Features

### 🛍️ **Complete Shopping Experience**
- **📂 Category & Brand Browsing**: Intuitive navigation through product categories and brands
- **⭐ Featured Products**: Discover trending and highlighted items
- **🔍 Advanced Search**: Find products quickly with smart search functionality
- **📋 Product Details**: Comprehensive product information with high-quality images
- **🛒 Smart Shopping Cart**: Add, remove, and manage items with quantity controls
- **❤️ Favorites System**: Save products to your wishlist for later purchase
- **💳 Secure Checkout**: Complete payment flow with multiple payment options
- **📦 Order Tracking**: Real-time order status and delivery tracking

### 🔐 **User Account Management**
- **🔑 Firebase Authentication**: Secure user registration and login
- **👤 Profile Management**: Update personal information and profile pictures
- **📋 Order History**: Complete history of past orders with details
- **📍 Address Book**: Save and manage multiple delivery addresses
- **⚙️ Account Settings**: Customize app preferences and notifications
- **🔒 Data Security**: All user data encrypted and securely stored

### 🎨 **Modern UI with Theme System**
- **🌓 Light & Dark Themes**: Complete theme switching with persistent preferences
- **🎨 Material 3 Design**: Beautiful UI following latest Material Design guidelines
- **➡️ RTL Support**: Full right-to-left text direction support for Arabic
- **📱 Responsive Design**: Adapts perfectly to different screen sizes and orientations
- **🎭 Custom Animations**: Smooth transitions and engaging user interactions
- **🔤 Cairo Font**: Beautiful Arabic typography with custom font family

### 📱 **Technical Excellence**
- **🏗️ Clean Architecture**: Well-organized code with separation of concerns
- **🔄 BLoC/Cubit Pattern**: Efficient state management for optimal performance
- **🔥 Firestore Integration**: Real-time data synchronization
- **🖼️ Cached Network Images**: Fast loading of product images with caching
- **📊 Analytics**: User behavior tracking and performance monitoring
- **🛡️ Error Handling**: Comprehensive error handling with user-friendly messages

---

## 📱 Screenshots

### 🏠 **Home & Navigation**
<div align="center">
  <img src="readme%20image/2.jpg" alt="Home Screen" width="200">
  <img src="readme%20image/3.jpg" alt="Products Screen" width="200">
  <img src="readme%20image/4.jpg" alt="Categories Screen" width="200">
</div>

### 🛒 **Shopping Experience**
<div align="center">
  <img src="readme%20image/5.jpg" alt="Product Details" width="200">
  <img src="readme%20image/6.jpg" alt="Shopping Cart" width="200">
  <img src="readme%20image/7.jpg" alt="Checkout Process" width="200">
  <img src="readme%20image/8.jpg" alt="Payment Options" width="200">
</div>

### 👤 **User Account**
<div align="center">
  <img src="readme%20image/9.jpg" alt="User Profile" width="200">
  <img src="readme%20image/10.jpg" alt="Order History" width="200">
  <img src="readme%20image/11.jpg" alt="Favorites" width="200">
</div>

### ⚙️ **Settings & More**
<div align="center">
  <img src="readme%20image/12.jpg" alt="Account Settings" width="200">
  <img src="readme%20image/13.jpg" alt="Address Book" width="200">
  <img src="readme%20image/14.jpg" alt="Theme Settings" width="200">
  <img src="readme%20image/15.jpg" alt="Notifications" width="200">
  <img src="readme%20image/16.jpg" alt="Help & Support" width="200">
</div>

---

## 🚀 Quick Start

### Prerequisites

Before you begin, ensure you have the following installed:

- **Flutter SDK** (3.5.0 or higher) - [Install Flutter](https://flutter.dev/docs/get-started/install)
- **Dart** (3.0 or higher)
- **Android Studio** or **VS Code** with Flutter extensions
- **Git** for version control
- **Firebase Account** for backend services

### 🏃‍♂️ Quick Installation

```bash
# Clone the repository
git clone https://github.com/KamelKOFASH/shatabha.git

# Navigate to project directory
cd shatabha

# Install dependencies
flutter pub get

# Run the app
flutter run
```

---

## 🛠️ Tech Stack

### **Frontend Development**
| Technology | Version | Purpose |
|------------|---------|---------|
| **Flutter SDK** | 3.5.0+ | Cross-platform UI framework |
| **Dart** | 3.0+ | Programming language |
| **Material 3** | Latest | Design system |
| **Responsive Utils** | Custom | Responsive design utilities |

### **State Management**
| Technology | Purpose |
|------------|---------|
| **BLoC/Cubit Pattern** | Clean and efficient state management |
| **Repository Pattern** | Separation of data sources from business logic |
| **GetIt** | Dependency injection and service locator |

### **Backend Services**
| Service | Purpose |
|---------|---------|
| **Firebase Authentication** | Secure user authentication system |
| **Cloud Firestore** | NoSQL database for product and user data |
| **Firebase Storage** | Image and file storage for product images |
| **Firebase Cloud Functions** | Backend processing for orders |
| **Supabase** | Additional storage and real-time features |

### **UI Components & Features**
| Component | Purpose |
|-----------|---------|
| **Custom Theme System** | Complete light/dark mode with Material 3 colors |
| **Cached Network Image** | Efficient loading and caching of product images |
| **Custom UI Components** | Reusable widgets for consistent design |
| **Skeletonizer** | Loading state placeholders for better UX |
| **Custom Error Handling** | User-friendly error messages and retry options |

### **Data Management**
| Technology | Purpose |
|------------|---------|
| **Shared Preferences** | Local storage for user settings and preferences |
| **Secure Storage** | Encrypted storage for sensitive information |
| **JSON Serialization** | Efficient data parsing and serialization |

---

## 🏗️ Architecture

Shatabha follows a **feature-first architecture** with clean separation of concerns, implementing the **Clean Architecture** pattern:

```
lib/
├── 📁 core/                           # Core functionality
│   ├── 📁 cubits/                     # Global state management
│   │   ├── favorites_cubit/           # Favorites state management
│   │   ├── products_cubit/            # Products state management
│   │   └── theme_cubit/               # Theme state management
│   ├── 📁 entities/                   # Core domain entities
│   │   ├── product_entity.dart        # Product domain model
│   │   └── review_entity.dart         # Review domain model
│   ├── 📁 models/                     # Data models
│   │   ├── product_model.dart         # Product data model
│   │   └── review_model.dart          # Review data model
│   ├── 📁 repos/                      # Repository implementations
│   │   ├── orders_repo/               # Orders repository
│   │   └── products_repos/            # Products repository
│   ├── 📁 services/                   # Core services
│   │   ├── firestore_service.dart     # Firebase database service
│   │   ├── firebase_auth_service.dart # Authentication service
│   │   ├── get_it_service.dart        # Dependency injection
│   │   └── supabase_storage_service.dart # Storage service
│   ├── 📁 utils/                      # Utility functions
│   │   ├── app_assets.dart            # Asset management
│   │   ├── responsive_utils.dart      # Responsive design utilities
│   │   └── app_decorations.dart       # UI decorations
│   ├── 📁 widgets/                    # Reusable widgets
│   │   ├── custom_button.dart         # Custom button component
│   │   ├── custom_dialog.dart         # Custom dialog component
│   │   └── custom_empty_state_widget.dart # Empty state component
│   └── 📁 global/themes/              # Theme configurations
│       ├── app_colors/                # Color schemes
│       ├── theme_data_dark.dart       # Dark theme
│       └── theme_data_light.dart      # Light theme
│
├── 📁 features/                       # Feature modules
│   ├── 📁 auth/                       # Authentication
│   │   ├── 📁 data/                   # Data layer
│   │   ├── 📁 domain/                 # Domain layer
│   │   └── 📁 presentation/           # UI layer
│   ├── 📁 home/                       # Main app screens
│   │   ├── 📁 data/                   # Data repositories
│   │   ├── 📁 domain/                 # Business logic
│   │   └── 📁 presentation/           # UI components
│   │       ├── 📁 manager/            # State management
│   │       └── 📁 views/              # Screen definitions
│   ├── 📁 checkout/                   # Cart and checkout
│   ├── 📁 favorites/                  # Wishlist functionality
│   ├── 📁 orders/                     # Order management
│   ├── 📁 profile/                    # User profile
│   ├── 📁 address_book/               # Address management
│   ├── 📁 account_settings/           # Settings
│   ├── 📁 onBoarding/                 # User onboarding
│   └── 📁 splash/                     # App initialization
│
├── 📁 generated/                      # Generated files (localization)
├── 📁 l10n/                          # Localization files
└── 📄 main.dart                       # App entry point
```

### **Architecture Principles**

- **🔄 Separation of Concerns**: Each layer has a specific responsibility
- **📦 Dependency Inversion**: High-level modules don't depend on low-level modules
- **🎯 Single Responsibility**: Each class has one reason to change
- **🔧 Open/Closed Principle**: Open for extension, closed for modification
- **🔄 Dependency Injection**: Loose coupling through dependency injection

---

## 📦 Installation

### Step 1: Clone the Repository

```bash
git clone https://github.com/KamelKOFASH/shatabha.git
cd shatabha
```

### Step 2: Install Dependencies

```bash
flutter pub get
```

### Step 3: Run the Application

```bash
# For development
flutter run

# For production build
flutter build apk --release
```

### Step 4: Platform-Specific Setup

#### Android
```bash
# Build APK
flutter build apk

# Build App Bundle
flutter build appbundle
```

#### iOS
```bash
# Build for iOS
flutter build ios

# Open in Xcode
open ios/Runner.xcworkspace
```

#### Web
```bash
# Build for web
flutter build web

# Serve locally
flutter run -d chrome
```

---

## ⚙️ Configuration

### Firebase Setup

1. **Create Firebase Project**
   - Go to [Firebase Console](https://console.firebase.google.com/)
   - Create a new project or select existing one

2. **Enable Authentication**
   - Navigate to Authentication > Sign-in method
   - Enable Email/Password authentication
   - Optionally enable Google, Facebook, or Apple sign-in

3. **Set up Cloud Firestore**
   - Go to Firestore Database
   - Create database in production mode
   - Set up security rules

4. **Configure Storage**
   - Go to Storage
   - Set up storage rules for image uploads

5. **Add Configuration Files**
   - Download `google-services.json` for Android
   - Download `GoogleService-Info.plist` for iOS
   - Place files in respective platform directories

### Required Firestore Collections

```javascript
// Users collection
users: {
  userId: {
    name: string,
    email: string,
    phone: string,
    profileImage: string,
    addresses: array,
    createdAt: timestamp,
    updatedAt: timestamp
  }
}

// Products collection
products: {
  productId: {
    name: string,
    description: string,
    price: number,
    originalPrice: number,
    category: string,
    company: string,
    imageUrl: string,
    imageUrls: array,
    code: string,
    isFeatured: boolean,
    discount: number,
    stockQuantity: number,
    avgRating: number,
    reviews: array,
    specifications: array,
    colorsAvailable: array,
    isAvailable: boolean,
    isOnSale: boolean,
    sellingCount: number,
    createdAt: timestamp,
    updatedAt: timestamp
  }
}

// Categories collection
categories: {
  categoryId: {
    name: string,
    imageUrl: string,
    description: string,
    productCount: number
  }
}

// Companies collection
companies: {
  companyId: {
    name: string,
    logoUrl: string,
    description: string,
    productCount: number
  }
}

// Orders collection
orders: {
  orderId: {
    uId: string,
    items: array,
    totalAmount: number,
    status: string,
    shippingAddress: object,
    paymentMethod: string,
    createdAt: timestamp,
    updatedAt: timestamp
  }
}

// Favorites collection
favorites: {
  favoriteId: {
    userId: string,
    productId: string,
    createdAt: timestamp
  }
}
```

### Environment Variables

Create a `.env` file in the root directory:

```env
# Firebase Configuration
FIREBASE_API_KEY=your_api_key
FIREBASE_PROJECT_ID=your_project_id
FIREBASE_MESSAGING_SENDER_ID=your_sender_id
FIREBASE_APP_ID=your_app_id

# Supabase Configuration
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key

# App Configuration
APP_NAME=Shatabha
APP_VERSION=1.0.0
```

---

## 🎯 Usage

### Basic Usage

1. **Launch the App**: Open the app and wait for the splash screen
2. **Onboarding**: Complete the onboarding process (first time only)
3. **Authentication**: Sign up or log in to your account
4. **Browse Products**: Navigate through categories and products
5. **Add to Cart**: Tap the cart icon to add items
6. **Checkout**: Complete your purchase through the checkout process

### Advanced Features

#### Theme Switching
```dart
// Switch to dark theme
context.read<ThemeCubit>().toggleTheme();

// Check current theme
final isDark = context.read<ThemeCubit>().state.isDark;
```

#### Cart Management
```dart
// Add product to cart
context.read<CartCubit>().addProduct(product);

// Remove item from cart
context.read<CartCubit>().deleteCartItemByIndex(index);

// Get cart total
final total = context.read<CartCubit>().cartEntity.getTotalPrice();
```

#### Favorites Management
```dart
// Add to favorites
context.read<GlobalFavoritesCubit>().addToFavorites(product);

// Remove from favorites
context.read<GlobalFavoritesCubit>().removeFromFavorites(productId);

// Check if product is favorited
final isFavorited = context.read<GlobalFavoritesCubit>().isFavorite(productId);
```

---

## 🧪 Testing

### Running Tests

```bash
# Run all tests
flutter test

# Run specific test file
flutter test test/widget_test.dart

# Run tests with coverage
flutter test --coverage

# Generate coverage report
genhtml coverage/lcov.info -o coverage/html
```

### Test Structure

```
test/
├── 📁 unit/                    # Unit tests
│   ├── 📁 cubits/             # State management tests
│   ├── 📁 models/             # Data model tests
│   └── 📁 services/           # Service tests
├── 📁 widget/                 # Widget tests
│   ├── 📁 features/           # Feature-specific tests
│   └── 📁 core/               # Core widget tests
└── 📁 integration/            # Integration tests
```

### Example Test

```dart
import 'package:flutter_test/flutter_test.dart';
import 'package:bloc_test/bloc_test.dart';

void main() {
  group('CartCubit Tests', () {
    late CartCubit cartCubit;

    setUp(() {
      cartCubit = CartCubit();
    });

    tearDown(() {
      cartCubit.close();
    });

    blocTest<CartCubit, CartState>(
      'emits [CartItemAdded] when product is added',
      build: () => cartCubit,
      act: (cubit) => cubit.addProduct(mockProduct),
      expect: () => [isA<CartItemAdded>()],
    );
  });
}
```

---

## 📚 API Documentation

### Core Services

#### DatabaseService
```dart
abstract class DatabaseService {
  Future<void> addData({
    required String path,
    required Map<String, dynamic> data,
    String? documentId,
  });

  Future<void> updateData({
    required String path,
    required Map<String, dynamic> data,
    required String documentId,
  });

  Future<dynamic> getData({
    required String path,
    String? documentId,
    Map<String, dynamic>? query,
  });

  Future<void> deleteData({
    required String path,
    required String documentId,
  });
}
```

#### AuthService
```dart
abstract class AuthService {
  Future<UserCredential> signInWithEmailAndPassword({
    required String email,
    required String password,
  });

  Future<UserCredential> createUserWithEmailAndPassword({
    required String email,
    required String password,
  });

  Future<void> signOut();
  
  User? get currentUser;
}
```

### Repository Pattern

#### ProductsRepository
```dart
abstract class ProductsRepo {
  Future<Either<Failure, List<ProductEntity>>> getProducts();
  Future<Either<Failure, ProductEntity>> getProductById(String id);
  Future<Either<Failure, List<ProductEntity>>> getProductsByCategory(String category);
  Future<Either<Failure, List<ProductEntity>>> getFeaturedProducts();
}
```

### State Management

#### CartCubit
```dart
class CartCubit extends Cubit<CartState> {
  void addProduct(ProductEntity product);
  void removeProduct(String productId);
  void updateQuantity(String productId, int quantity);
  void clearCart();
  double getTotalPrice();
}
```

---

## 🤝 Contributing

We welcome contributions from the community! Here's how you can help:

### 🐛 Reporting Bugs

1. Check existing issues to avoid duplicates
2. Create a new issue with detailed information:
   - **Description**: Clear description of the bug
   - **Steps to Reproduce**: Step-by-step instructions
   - **Expected Behavior**: What should happen
   - **Actual Behavior**: What actually happens
   - **Screenshots**: Visual evidence if applicable
   - **Environment**: Device, OS, Flutter version

### 💡 Suggesting Features

1. Check existing feature requests
2. Create a new issue with:
   - **Feature Description**: What you'd like to see
   - **Use Case**: Why this feature is needed
   - **Mockups**: Visual examples if possible

### 🔧 Contributing Code

1. **Fork the repository**
2. **Create a feature branch**:
   ```bash
   git checkout -b feature/amazing-feature
   ```
3. **Make your changes** following the coding standards
4. **Write tests** for new functionality
5. **Commit your changes**:
   ```bash
   git commit -m 'Add amazing feature'
   ```
6. **Push to the branch**:
   ```bash
   git push origin feature/amazing-feature
   ```
7. **Open a Pull Request**

### 📋 Coding Standards

- Follow **Dart style guide**
- Use **meaningful variable names**
- Write **comprehensive comments**
- Add **unit tests** for new features
- Update **documentation** as needed
- Follow **conventional commits**

### 🏷️ Commit Message Format

```
type(scope): description

[optional body]

[optional footer]
```

Types:
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes
- `refactor`: Code refactoring
- `test`: Adding tests
- `chore`: Maintenance tasks

---

## 📄 License

This project is licensed under the **MIT License** - see the [LICENSE](LICENSE) file for details.

```
MIT License

Copyright (c) 2024 Shatabha

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
```

---

## 👨‍💻 About

<div align="center">
  <img src="readme%20image/17.jpg" width="400" alt="Shatabha App Banner">
</div>

**Shatabha (شَـطْبِهَا)** is more than just an e-commerce app - it's a comprehensive solution designed to provide Arabic-speaking users with a modern, secure, and enjoyable shopping experience.

### 🎯 Our Mission

To create the most user-friendly and feature-rich e-commerce platform specifically tailored for Arabic-speaking communities, combining cutting-edge technology with cultural sensitivity.

### 🌟 Key Differentiators

- **🌍 Arabic-First Design**: Built from the ground up for Arabic users with RTL support
- **⚡ Performance Focused**: Optimized for smooth performance even on lower-end devices
- **🏗️ Clean Architecture**: Maintainable and scalable codebase following best practices
- **🎨 Beautiful UI**: Modern Material 3 design with light and dark themes
- **🔒 Security First**: Enterprise-grade security with Firebase integration
- **📱 Cross-Platform**: Seamless experience across iOS, Android, and Web

### 🚀 Future Roadmap

- [ ] **Multi-language Support**: Expand beyond Arabic
- [ ] **Advanced Analytics**: Enhanced user behavior tracking
- [ ] **AI Recommendations**: Smart product recommendations
- [ ] **Social Features**: User reviews and ratings
- [ ] **Offline Mode**: Basic functionality without internet
- [ ] **Push Notifications**: Real-time order updates
- [ ] **Payment Integration**: More payment gateways
- [ ] **Admin Panel**: Comprehensive admin dashboard

### 📞 Support & Contact

- **📧 Email**: [<EMAIL>](mailto:<EMAIL>)
- **🐛 Issues**: [GitHub Issues](https://github.com/KamelKOFASH/shatabha/issues)
- **💬 Discussions**: [GitHub Discussions](https://github.com/KamelKOFASH/shatabha/discussions)
- **📖 Documentation**: [Wiki](https://github.com/KamelKOFASH/shatabha/wiki)

### 🙏 Acknowledgments

- **Flutter Team** for the amazing framework
- **Firebase Team** for robust backend services
- **Material Design Team** for beautiful design guidelines
- **Open Source Community** for inspiration and support

---

<div align="center">

### 🌟 Star this repository if you find it helpful!

[![GitHub stars](https://img.shields.io/github/stars/KamelKOFASH/shatabha?style=social)](https://github.com/KamelKOFASH/shatabha/stargazers)

**Shatabha - شَـطْبِهَا**  
*A modern Arabic e-commerce experience*

Made with ❤️ by the Mustafa Kamel

</div>
