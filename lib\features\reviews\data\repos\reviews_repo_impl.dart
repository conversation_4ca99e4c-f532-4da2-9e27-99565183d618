import 'dart:developer';
import 'package:dartz/dartz.dart';
import '../../../../core/errors/failure.dart';
import '../../../../core/services/data_service.dart';
import '../../../../core/utils/backend_end_points.dart';
import '../models/review_model.dart';
import '../../domain/entities/review_entity.dart';
import '../../domain/repos/reviews_repo.dart';

class ReviewsRepoImpl implements ReviewsRepo {
  final DatabaseService databaseService;

  ReviewsRepoImpl({required this.databaseService});

  @override
  Future<Either<Failure, List<ReviewEntity>>> getProductReviews({
    required String productId,
  }) async {
    try {
      log('Getting reviews for product: $productId');

      final data = await databaseService.getData(
        path: BackendEndPoints.getReviews,
        query: {
          'where': {
            'productId': productId,
          },
          'orderBy': 'createdAt',
          'descending': true,
        },
      );

      if (data is List) {
        final List<ReviewEntity> reviews = data
            .map((reviewData) => ReviewModel.fromJson(reviewData).toEntity())
            .toList();

        log('Found ${reviews.length} reviews for product $productId');
        return right(reviews);
      }

      return right([]);
    } catch (e) {
      log('Exception in ReviewsRepoImpl.getProductReviews: ${e.toString()}');
      return left(ServerFailure('حدث خطأ في جلب التقييمات'));
    }
  }

  @override
  Future<Either<Failure, List<ReviewEntity>>> getUserReviews({
    required String userId,
  }) async {
    try {
      log('Getting reviews for user: $userId');

      final data = await databaseService.getData(
        path: BackendEndPoints.getReviews,
        query: {
          'where': {
            'userId': userId,
          },
          'orderBy': 'createdAt',
          'descending': true,
        },
      );

      if (data is List) {
        final List<ReviewEntity> reviews = data
            .map((reviewData) => ReviewModel.fromJson(reviewData).toEntity())
            .toList();

        log('Found ${reviews.length} reviews for user $userId');
        return right(reviews);
      }

      return right([]);
    } catch (e) {
      log('Exception in ReviewsRepoImpl.getUserReviews: ${e.toString()}');
      return left(ServerFailure('حدث خطأ في جلب تقييماتك'));
    }
  }

  @override
  Future<Either<Failure, void>> addReview({
    required ReviewEntity review,
  }) async {
    try {
      log('Adding review: ${review.id}');

      final reviewModel = ReviewModel.fromEntity(review);

      await databaseService.addData(
        path: BackendEndPoints.addReview,
        data: reviewModel.toJson(),
        documentId: review.id,
      );

      log('Successfully added review: ${review.id}');
      return right(null);
    } catch (e) {
      log('Exception in ReviewsRepoImpl.addReview: ${e.toString()}');
      return left(ServerFailure('حدث خطأ في إضافة التقييم'));
    }
  }

  @override
  Future<Either<Failure, void>> updateReview({
    required ReviewEntity review,
  }) async {
    try {
      log('Updating review: ${review.id}');

      final reviewModel = ReviewModel.fromEntity(review.copyWith(
        updatedAt: DateTime.now(),
      ));

      await databaseService.updateData(
        path: BackendEndPoints.updateReview,
        data: reviewModel.toJson(),
        documentId: review.id,
      );

      log('Successfully updated review: ${review.id}');
      return right(null);
    } catch (e) {
      log('Exception in ReviewsRepoImpl.updateReview: ${e.toString()}');
      return left(ServerFailure('حدث خطأ في تحديث التقييم'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteReview({
    required String reviewId,
    required String userId,
  }) async {
    try {
      log('Deleting review: $reviewId by user: $userId');

      // First verify the review belongs to the user
      final reviewResult = await getReviewById(reviewId: reviewId);

      return reviewResult.fold(
        (failure) => left(failure),
        (review) async {
          if (review.userId != userId) {
            return left(ServerFailure('غير مسموح لك بحذف هذا التقييم'));
          }

          await databaseService.deleteData(
            path: BackendEndPoints.deleteReview,
            docuementId: reviewId,
          );

          log('Successfully deleted review: $reviewId');
          return right(null);
        },
      );
    } catch (e) {
      log('Exception in ReviewsRepoImpl.deleteReview: ${e.toString()}');
      return left(ServerFailure('حدث خطأ في حذف التقييم'));
    }
  }

  @override
  Future<Either<Failure, bool>> hasUserReviewedProduct({
    required String userId,
    required String productId,
  }) async {
    try {
      log('Checking if user $userId has reviewed product $productId');

      final data = await databaseService.getData(
        path: BackendEndPoints.getReviews,
        query: {
          'where': {
            'userId': userId,
            'productId': productId,
          },
          'limit': 1,
        },
      );

      if (data is List && data.isNotEmpty) {
        log('User has already reviewed this product');
        return right(true);
      }

      log('User has not reviewed this product');
      return right(false);
    } catch (e) {
      log('Exception in ReviewsRepoImpl.hasUserReviewedProduct: ${e.toString()}');
      return left(ServerFailure('حدث خطأ في التحقق من التقييم'));
    }
  }

  @override
  Future<Either<Failure, ReviewEntity>> getReviewById({
    required String reviewId,
  }) async {
    try {
      log('Getting review by ID: $reviewId');

      final data = await databaseService.getData(
        path: BackendEndPoints.getReviews,
        docuementId: reviewId,
      );

      if (data != null && data is Map<String, dynamic>) {
        final review = ReviewModel.fromJson(data).toEntity();
        log('Found review: $reviewId');
        return right(review);
      }

      return left(ServerFailure('التقييم غير موجود'));
    } catch (e) {
      log('Exception in ReviewsRepoImpl.getReviewById: ${e.toString()}');
      return left(ServerFailure('حدث خطأ في جلب التقييم'));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getProductReviewStats({
    required String productId,
  }) async {
    try {
      log('Getting review stats for product: $productId');

      final reviewsResult = await getProductReviews(productId: productId);

      return reviewsResult.fold(
        (failure) => left(failure),
        (reviews) {
          if (reviews.isEmpty) {
            return right({
              'averageRating': 0.0,
              'totalReviews': 0,
              'ratingDistribution': {
                '5': 0,
                '4': 0,
                '3': 0,
                '2': 0,
                '1': 0,
              },
            });
          }

          final totalReviews = reviews.length;
          final totalRating = reviews.fold<double>(
            0.0,
            (sum, review) => sum + review.rating.toDouble(),
          );
          final averageRating = totalRating / totalReviews;

          // Calculate rating distribution
          final ratingDistribution = <String, int>{
            '5': 0,
            '4': 0,
            '3': 0,
            '2': 0,
            '1': 0,
          };

          for (final review in reviews) {
            final rating = review.rating.round().toString();
            ratingDistribution[rating] = (ratingDistribution[rating] ?? 0) + 1;
          }

          final stats = {
            'averageRating': double.parse(averageRating.toStringAsFixed(1)),
            'totalReviews': totalReviews,
            'ratingDistribution': ratingDistribution,
          };

          log('Review stats for product $productId: $stats');
          return right(stats);
        },
      );
    } catch (e) {
      log('Exception in ReviewsRepoImpl.getProductReviewStats: ${e.toString()}');
      return left(ServerFailure('حدث خطأ في جلب إحصائيات التقييمات'));
    }
  }
}
