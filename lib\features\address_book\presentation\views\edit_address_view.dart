import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../core/widgets/custom_button.dart';
import '../../../../core/services/get_it_service.dart';
import '../../domain/entities/address_entity.dart';
import '../../domain/repos/address_book_repo.dart';
import '../manager/address_book_cubit/address_book_cubit.dart';

class EditAddressScreen extends StatefulWidget {
  final AddressEntity address;

  const EditAddressScreen({super.key, required this.address});
  static const String routeName = '/edit-address';

  @override
  State<EditAddressScreen> createState() => _EditAddressScreenState();
}

class _EditAddressScreenState extends State<EditAddressScreen> {
  late TextEditingController _titleController;
  late TextEditingController _nameController;
  late TextEditingController _phoneController;
  late TextEditingController _streetController;
  late TextEditingController _cityController;
  late TextEditingController _stateController;
  late TextEditingController _postalController;
  late TextEditingController _countryController;

  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    
    _titleController = TextEditingController(text: widget.address.title);
    _nameController = TextEditingController(text: widget.address.fullName);
    _phoneController = TextEditingController(text: widget.address.phoneNumber);
    _streetController = TextEditingController(text: widget.address.street);
    _cityController = TextEditingController(text: widget.address.city);
    _stateController = TextEditingController(text: widget.address.state);
    _postalController = TextEditingController(text: widget.address.postalCode);
    _countryController = TextEditingController(text: widget.address.country);
    super.initState();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _nameController.dispose();
    _phoneController.dispose();
    _streetController.dispose();
    _cityController.dispose();
    _stateController.dispose();
    _postalController.dispose();
    _countryController.dispose();
    super.dispose();
  }

  void _updateAddress() {
    if (_formKey.currentState!.validate()) {
      final updated = AddressEntity(
        id: widget.address.id,
        userId: widget.address.userId,
        title: _titleController.text,
        fullName: _nameController.text,
        phoneNumber: _phoneController.text,
        street: _streetController.text,
        city: _cityController.text,
        state: _stateController.text,
        postalCode: _postalController.text,
        country: _countryController.text,
        isDefault: widget.address.isDefault,
        createdAt: widget.address.createdAt,
      );

      context.read<AddressBookCubit>().updateAddress(address: updated);
      Navigator.pop(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<AddressBookCubit>(
      create: (context) => AddressBookCubit(getIt<AddressBookRepo>()),
      child: Scaffold(
        appBar: AppBar(title: const Text('تعديل العنوان')),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Form(
            key: _formKey,
            child: Column(
              children: [
                _buildField(_titleController, 'عنوان (مثلاً: المنزل)'),
                _buildField(_nameController, 'الاسم الكامل'),
                _buildField(_phoneController, 'رقم الهاتف'),
                _buildField(_streetController, 'الشارع'),
                _buildField(_cityController, 'المدينة'),
                _buildField(_stateController, 'المنطقة / المحافظة'),
                _buildField(_postalController, 'الرمز البريدي'),
                _buildField(_countryController, 'البلد'),
                const SizedBox(height: 24),
                CustomButton(
                  text: 'حفظ التعديلات',
                  onPressed: _updateAddress,
                  bgColor: Theme.of(context).colorScheme.primary,
                  textColor: Colors.white,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildField(TextEditingController controller, String hint) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: TextFormField(
        controller: controller,
        decoration: InputDecoration(
          hintText: hint,
          border: const OutlineInputBorder(),
        ),
        validator: (value) =>
            value == null || value.isEmpty ? 'هذا الحقل مطلوب' : null,
      ),
    );
  }
}
