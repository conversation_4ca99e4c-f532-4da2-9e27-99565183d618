import 'package:flutter/material.dart';
import '../utils/responsive_utils.dart';

class CustomErrorWidget extends StatelessWidget {
  const CustomErrorWidget({
    super.key,
    required this.errMessage,
    this.onRetry,
  });

  final String errMessage;
  final VoidCallback? onRetry;

  @override
  Widget build(BuildContext context) {
    context.initResponsive();
    return Container(
      padding: REdgeInsets.all(16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: ResponsiveUtils.iconSize(48),
            color: Theme.of(context).colorScheme.error,
          ),
          const RSizedBox.height(12),
          Text(
            errMessage,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface,
                ),
            textAlign: TextAlign.center,
          ),
          if (onRetry != null) ...[
            const RSizedBox.height(16),
            ElevatedButton(
              onPressed: onRetry,
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primary,
                foregroundColor: Theme.of(context).colorScheme.onPrimary,
              ),
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ],
      ),
    );
  }
}
