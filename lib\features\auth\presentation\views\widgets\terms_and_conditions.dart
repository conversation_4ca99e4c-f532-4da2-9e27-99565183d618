import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import '../../../../../core/utils/responsive_utils.dart';

class TermsAndConditions extends StatefulWidget {
  const TermsAndConditions({super.key, required this.onCheckboxChanged});
  final ValueChanged<bool> onCheckboxChanged;
  @override
  State<TermsAndConditions> createState() => _TermsAndConditionsState();
}

class _TermsAndConditionsState extends State<TermsAndConditions> {
  bool isChecked = false;

  @override
  Widget build(BuildContext context) {
    return Transform.translate(
      offset: const Offset(14, 0),
      child: Row(
        children: [
          Checkbox(
            value: isChecked,
            activeColor: Theme.of(context).colorScheme.primary,
            shape: RoundedRectangleBorder(
              borderRadius: RBorderRadius.circular(4),
            ),
            onChanged: (value) {
              //? Handle checkbox value change
              setState(() {
                isChecked = value ?? false;
                widget.onCheckboxChanged(isChecked);
              });
            },
          ),
          const RSizedBox.width(8), // تعديل العرض ليكون متناسقًا
          Flexible(
            child: RichText(
              text: TextSpan(
                text: 'من خلال إنشاء حساب ، فإنك توافق على ',
                style: Theme.of(context).textTheme.bodyMedium,
                children: [
                  TextSpan(
                    text: 'الشروط والأحكام الخاصة بنا.',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.primary,
                          fontWeight: FontWeight.w600,
                        ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        //? Handle "الشروط والأحكام" click
                      },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
