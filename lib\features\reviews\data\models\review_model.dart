import '../../domain/entities/review_entity.dart';

class ReviewModel extends ReviewEntity {
  const ReviewModel({
    required super.id,
    required super.userId,
    required super.productId,
    required super.userName,
    required super.userImage,
    required super.rating,
    required super.reviewText,
    required super.createdAt,
    super.updatedAt,
    super.isVerifiedPurchase,
  });

  factory ReviewModel.fromEntity(ReviewEntity entity) {
    return ReviewModel(
      id: entity.id,
      userId: entity.userId,
      productId: entity.productId,
      userName: entity.userName,
      userImage: entity.userImage,
      rating: entity.rating,
      reviewText: entity.reviewText,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      isVerifiedPurchase: entity.isVerifiedPurchase,
    );
  }

  factory ReviewModel.fromJson(Map<String, dynamic> json) {
    return ReviewModel(
      id: json['id'] ?? '',
      userId: json['userId'] ?? '',
      productId: json['productId'] ?? '',
      userName: json['userName'] ?? '',
      userImage: json['userImage'] ?? '',
      rating: json['rating'] ?? 0,
      reviewText: json['reviewText'] ?? '',
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: json['updatedAt'] != null 
          ? DateTime.parse(json['updatedAt']) 
          : null,
      isVerifiedPurchase: json['isVerifiedPurchase'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'productId': productId,
      'userName': userName,
      'userImage': userImage,
      'rating': rating,
      'reviewText': reviewText,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'isVerifiedPurchase': isVerifiedPurchase,
    };
  }

  ReviewEntity toEntity() {
    return ReviewEntity(
      id: id,
      userId: userId,
      productId: productId,
      userName: userName,
      userImage: userImage,
      rating: rating,
      reviewText: reviewText,
      createdAt: createdAt,
      updatedAt: updatedAt,
      isVerifiedPurchase: isVerifiedPurchase,
    );
  }
}
