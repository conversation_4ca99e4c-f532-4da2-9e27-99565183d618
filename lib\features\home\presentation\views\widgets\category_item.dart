import 'dart:developer';
import 'package:flutter/material.dart';
import '../../../../../core/utils/responsive_utils.dart';
import '../../../../../core/widgets/custom_network_image.dart';
import '../../../domain/entities/category_entity.dart';

class CategoryItem extends StatelessWidget {
  const CategoryItem({
    super.key,
    required this.category,
    required this.onTap,
    this.hasImage = false,
  });

  final CategoryEntity category;
  final VoidCallback onTap;
  final bool hasImage;

  @override
  Widget build(BuildContext context) {
    context.initResponsive();
    log('Building CategoryItem: ${category.name}, Image: ${category.image}, hasImage: $hasImage');
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: REdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: RBorderRadius.circular(12),
          border: Border.all(
            color: Theme.of(context).dividerColor,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            hasImage
                ? Container(
                    width: ResponsiveUtils.spacing(60),
                    height: ResponsiveUtils.spacing(60),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surface,
                      borderRadius: RBorderRadius.circular(8),
                    ),
                    child: ClipRRect(
                      borderRadius: RBorderRadius.circular(8),
                      child: CustomNetworkImage(
                        imageUrl: category.image,
                      ),
                    ),
                  )
                : const SizedBox.shrink(),
            const RSizedBox.height(8),
            Text(
              category.name,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}
