import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:dartz/dartz.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../../../constants.dart';
import '../../../../core/errors/exceptions.dart';
import '../../../../core/services/supabase_storage_service.dart';

import '../../../../core/errors/failure.dart';
import '../../../../core/services/data_service.dart';
import '../../../../core/utils/backend_end_points.dart';
import '../models/user_model.dart';

import '../../domain/entities/user_entity.dart';
// ignore: unused_import
import 'package:shared_preferences/shared_preferences.dart';

import '../../../../core/services/firebase_auth_service.dart';
import '../../../../core/services/shared_preferences_singleton.dart';
import 'auth_repo.dart';

class AuthRepoImpl implements AuthRepo {
  final FirebaseAuthService firebaseAuthService;
  final DatabaseService databaseService;
  final SupabaseStorageService storageService;

  AuthRepoImpl({
    required this.databaseService,
    required this.firebaseAuthService,
    required this.storageService,
  });

  @override
  Future<Either<Failure, UserEntity>> createUserWithEmailAndPassword(
    String name,
    String phone,
    String email,
    String password, {
    File? profileImage,
  }) async {
    User? user;
    try {
      user = await firebaseAuthService.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      // Upload profile image if provided
      String? imageUrl;
      if (profileImage != null) {
        // Ensure bucket exists before uploading
        await storageService.ensureBucketExists();

        imageUrl = await storageService.uploadUserImage(
          imageFile: profileImage,
          userId: user.uid,
        );
      }

      await addUserData(
        userEntity: UserEntity(
          uId: user.uid,
          name: name,
          phone: phone,
          email: email,
          imageUrl: imageUrl,
        ),
      );

      return right(UserModel(
        name: name,
        phone: phone,
        email: email,
        uId: user.uid,
        imageUrl: imageUrl,
      ));
    } on CustomException catch (e) {
      await deleteUser(user);
      return left(ServerFailure(e.message));
    } catch (e) {
      await deleteUser(user);
      log(
        'Exception in AuthRepoImpl.createUserWithEmailAndPassword: ${e.toString()}',
      );
      return left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, UserEntity>> signInWithEmailAndPassword(
    String email,
    String password,
  ) async {
    try {
      final user = await firebaseAuthService.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      final userEntity = await getUserData(uId: user.uid);
      await saveUserData(userEntity: userEntity);
      return right(userEntity);
    } on CustomException catch (e) {
      return left(ServerFailure(e.message));
    } catch (e) {
      log(
        'Exception in AuthRepoImpl.signInWithEmailAndPassword: ${e.toString()}',
      );
      return left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, UserEntity>> signInWithGoogle() async {
    User? user;
    try {
      user = await firebaseAuthService.signInWithGoogle();
      final isUserExist = await databaseService.checkIfDataExists(
        path: BackendEndPoints.getUserData,
        docuementId: user.uid,
      );
      if (isUserExist) {
        await getUserData(uId: user.uid);
      } else {
        await addUserData(userEntity: UserModel.fromFirebaseUser(user));
        await saveUserData(userEntity: UserModel.fromFirebaseUser(user));
      }
      await saveUserData(userEntity: UserModel.fromFirebaseUser(user));

      return right(UserModel.fromFirebaseUser(user));
    } catch (e) {
      await deleteUser(user);
      log(
        'Exception in AuthRepoImpl.signInWithGoogle: ${e.toString()}',
      );
      return left(
        ServerFailure(
          'حدث خطأ ما. الرجاء المحاولة مرة اخرى.',
        ),
      );
    }
  }

  @override
  Future<Either<Failure, UserEntity>> signInWithFacebook() async {
    User? user;
    try {
      user = await firebaseAuthService.signInWithFacebook();
      final isUserExist = await databaseService.checkIfDataExists(
        path: BackendEndPoints.getUserData,
        docuementId: user.uid,
      );
      if (isUserExist) {
        await getUserData(uId: user.uid);
      } else {
        await addUserData(userEntity: UserModel.fromFirebaseUser(user));
        await saveUserData(userEntity: UserModel.fromFirebaseUser(user));
      }
      return right(UserModel.fromFirebaseUser(user));
    } catch (e) {
      await deleteUser(user);
      log(
        'Exception in AuthRepoImpl.signInWithFacebook: ${e.toString()}',
      );
      return left(ServerFailure('حدث خطأ ما. الرجاء المحاولة مرة اخرى.'));
    }
  }

  @override
  Future<Either<Failure, UserEntity>> signInWithApple() async {
    User? user;
    try {
      user = await firebaseAuthService.signInWithApple();
      await addUserData(userEntity: UserModel.fromFirebaseUser(user));
      return right(UserModel.fromFirebaseUser(user));
    } catch (e) {
      await deleteUser(user);
      log(
        'Exception in AuthRepoImpl.signInWithApple: ${e.toString()}',
      );
      return left(ServerFailure('حدث خطأ ما. الرجاء المحاولة مرة اخرى.'));
    }
  }

  @override
  Future addUserData({required UserEntity userEntity}) async {
    try {
      await databaseService.addData(
        path: BackendEndPoints.addUserData,
        data: UserModel.fromEntity(userEntity).toMap(),
        documentId: userEntity.uId,
      );
    } catch (e) {
      log(
        'Exception in AuthRepoImpl.addUserData: ${e.toString()}',
      );
      throw ServerFailure('حدث خطأ ما. الرجاء المحاولة مرة اخرى.');
    }
  }

  Future<void> deleteUser(User? user) async {
    if (user != null) {
      await firebaseAuthService.deleteUser();
    }
  }

  @override
  Future<UserEntity> getUserData({required String uId}) async {
    final userData = await databaseService.getData(
      path: BackendEndPoints.getUserData,
      docuementId: uId,
    );
    return UserModel.fromJson(userData);
  }

  @override
  Future saveUserData({required UserEntity userEntity}) async {
    final jsonData = jsonEncode(UserModel.fromEntity(userEntity).toMap());
    await SharedPreferencesSingleton.setString(
      kUserData,
      jsonData,
    );
  }

  @override
  Future<Either<Failure, void>> sendPasswordResetEmail({
    required String email,
  }) async {
    try {
      await firebaseAuthService.sendPasswordResetEmail(email: email);
      return right(null);
    } catch (e) {
      log(
        'Exception in AuthRepoImpl.sendPasswordResetEmail: ${e.toString()}',
      );
      return left(
        ServerFailure(
          'حدث خطأ ما. الرجاء المحاولة مرة اخرى.',
        ),
      );
    }
  }
}
